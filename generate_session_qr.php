<?php
require_once 'config/functions.php';

header('Content-Type: application/json');

if (!isset($_GET['session_id'])) {
    echo json_encode(['success' => false, 'error' => 'Session ID is required']);
    exit;
}

$session_id = sanitize($_GET['session_id']);

// Get session details
$stmt = $conn->prepare("
    SELECT cs.*, c.course_code, c.course_title, c.qr_code
    FROM class_sessions cs
    JOIN courses c ON cs.course_id = c.course_id
    WHERE cs.session_id = ? AND cs.status = 'active'
");
$stmt->bind_param("i", $session_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    echo json_encode(['success' => false, 'error' => 'Active session not found']);
    exit;
}

$session = $result->fetch_assoc();

// Check if course already has a QR code
if (!empty($session['qr_code']) && file_exists($session['qr_code'])) {
    echo json_encode([
        'success' => true,
        'qr_code' => $session['qr_code'],
        'course_code' => $session['course_code'],
        'course_title' => $session['course_title']
    ]);
} else {
    // Generate new QR code for the course if it doesn't exist
    $attendance_url = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/student/scan_qr.php?course_id=" . $session['course_id'] . "&session_id=" . $session_id;
    
    $qr_path = generateQRCode($attendance_url);

    if ($qr_path) {
        // Update course with QR code path
        $stmt = $conn->prepare("UPDATE courses SET qr_code = ? WHERE course_id = ?");
        $stmt->bind_param("si", $qr_path, $session['course_id']);
        $stmt->execute();

        echo json_encode([
            'success' => true,
            'qr_code' => $qr_path,
            'course_code' => $session['course_code'],
            'course_title' => $session['course_title']
        ]);
    } else {
        echo json_encode(['success' => false, 'error' => 'Failed to generate QR code']);
    }
}
?>
