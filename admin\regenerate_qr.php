<?php
require_once '../config/functions.php';
require_once 'auth_check.php';

// Get course ID from URL
$course_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($course_id <= 0) {
    redirect('courses.php');
}

// Get course details
$stmt = $conn->prepare("SELECT * FROM courses WHERE course_id = ?");
$stmt->bind_param("i", $course_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    redirect('courses.php');
}

$course = $result->fetch_assoc();

// Generate QR code data
$qr_data = json_encode([
    'course_id' => $course['course_id'],
    'course_code' => $course['course_code'],
    'course_title' => $course['course_title'],
    'dept_id' => $course['dept_id'],
    'level_id' => $course['level_id'],
    'semester_id' => $course['semester_id'],
    'timestamp' => time()
]);

// Generate new QR code
$qr_code = generateQRCode($qr_data);

// Update course with new QR code
$stmt = $conn->prepare("UPDATE courses SET qr_code = ? WHERE course_id = ?");
$stmt->bind_param("si", $qr_code, $course_id);

if ($stmt->execute()) {
    // Redirect back to courses page with success message
    $_SESSION['success_message'] = "QR code regenerated successfully for " . $course['course_code'];
} else {
    // Redirect back to courses page with error message
    $_SESSION['error_message'] = "Error regenerating QR code: " . $conn->error;
}

redirect('courses.php');
?>
