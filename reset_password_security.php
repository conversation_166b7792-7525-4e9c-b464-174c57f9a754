<?php
require_once 'config/functions.php';
require_once 'config/security_questions_functions.php';

// Ensure security questions tables exist
ensureSecurityQuestionsTables();

$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$user_type = isset($_POST['user_type']) ? $_POST['user_type'] : (isset($_GET['user_type']) ? $_GET['user_type'] : '');
$error = '';
$success = '';

// Step 1: User identification
if ($step == 1 && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $identifier = sanitize($_POST['identifier']);
    $user_type = sanitize($_POST['user_type']);
    
    if (empty($identifier) || empty($user_type)) {
        $error = 'Please fill in all fields';
    } else {
        if ($user_type === 'lecturer') {
            $user = findLecturerByIdentifier($identifier);
            if ($user) {
                $questions = getLecturerSecurityQuestions($user['lecturer_id']);
            }
        } elseif ($user_type === 'student') {
            $user = findStudentByIdentifier($identifier);
            if ($user) {
                $questions = getStudentSecurityQuestions($user['student_id']);
            }
        }
        
        if (!$user) {
            $error = 'User not found. Please check your ' . ($user_type === 'lecturer' ? 'staff code or email' : 'matric number or email');
        } elseif (empty($questions)) {
            $error = 'No security questions found for this account. Please contact the administrator.';
        } else {
            // Store user info in session for next step
            $_SESSION['reset_user'] = $user;
            $_SESSION['reset_questions'] = $questions;
            $_SESSION['reset_user_type'] = $user_type;
            header("Location: reset_password_security.php?step=2");
            exit;
        }
    }
}

// Step 2: Security questions verification
if ($step == 2) {
    if (!isset($_SESSION['reset_user'])) {
        header("Location: reset_password_security.php?step=1");
        exit;
    }
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $answers = [];
        foreach ($_SESSION['reset_questions'] as $i => $question) {
            $answers[] = sanitize($_POST['answer_' . $i]);
        }
        
        $user_id = $_SESSION['reset_user'][$_SESSION['reset_user_type'] . '_id'];
        
        if ($_SESSION['reset_user_type'] === 'lecturer') {
            $verified = verifyLecturerSecurityAnswers($user_id, $answers);
        } else {
            $verified = verifyStudentSecurityAnswers($user_id, $answers);
        }
        
        if ($verified) {
            header("Location: reset_password_security.php?step=3");
            exit;
        } else {
            $error = 'Security answers are incorrect. Please try again.';
        }
    }
}

// Step 3: Password reset
if ($step == 3) {
    if (!isset($_SESSION['reset_user'])) {
        header("Location: reset_password_security.php?step=1");
        exit;
    }
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $new_password = $_POST['new_password'];
        $confirm_password = $_POST['confirm_password'];
        
        if (empty($new_password) || empty($confirm_password)) {
            $error = 'Please fill in all fields';
        } elseif ($new_password !== $confirm_password) {
            $error = 'Passwords do not match';
        } elseif (strlen($new_password) < 6) {
            $error = 'Password must be at least 6 characters long';
        } else {
            $user_id = $_SESSION['reset_user'][$_SESSION['reset_user_type'] . '_id'];
            
            if ($_SESSION['reset_user_type'] === 'lecturer') {
                $reset_success = resetLecturerPassword($user_id, $new_password);
            } else {
                $reset_success = resetStudentPassword($user_id, $new_password);
            }
            
            if ($reset_success) {
                $success = 'Password reset successfully! You can now login with your new password.';
                // Clear session data
                unset($_SESSION['reset_user']);
                unset($_SESSION['reset_questions']);
                unset($_SESSION['reset_user_type']);
            } else {
                $error = 'Failed to reset password. Please try again.';
            }
        }
    }
}
?>

<?php include 'includes/header.php'; ?>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3 class="text-center">
                    <i class="fas fa-key me-2"></i>Password Reset
                    <?php if ($step == 1): ?>
                        - Step 1: Identify Account
                    <?php elseif ($step == 2): ?>
                        - Step 2: Security Questions
                    <?php elseif ($step == 3): ?>
                        - Step 3: New Password
                    <?php endif; ?>
                </h3>
            </div>
            <div class="card-body">
                <?php if (!empty($success)): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                    </div>
                    <div class="text-center">
                        <a href="index.php" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>Go to Login
                        </a>
                    </div>
                <?php else: ?>
                    <?php if (!empty($error)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                        </div>
                    <?php endif; ?>

                    <?php if ($step == 1): ?>
                        <!-- Step 1: User Identification -->
                        <form method="POST" action="">
                            <div class="mb-3">
                                <label for="user_type" class="form-label">I am a:</label>
                                <select class="form-select" id="user_type" name="user_type" required>
                                    <option value="">Select user type</option>
                                    <option value="lecturer" <?php echo $user_type === 'lecturer' ? 'selected' : ''; ?>>Lecturer</option>
                                    <option value="student" <?php echo $user_type === 'student' ? 'selected' : ''; ?>>Student</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="identifier" class="form-label">
                                    <span id="identifier-label">Staff Code/Email or Matric Number/Email</span>
                                </label>
                                <input type="text" class="form-control" id="identifier" name="identifier" 
                                       placeholder="Enter your staff code, matric number, or email" required>
                                <div class="form-text" id="identifier-help">
                                    Enter your staff code or email (for lecturers) or matric number or email (for students)
                                </div>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>Find Account
                                </button>
                                <a href="index.php" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>Back to Login
                                </a>
                            </div>
                        </form>

                    <?php elseif ($step == 2): ?>
                        <!-- Step 2: Security Questions -->
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Please answer your security questions to verify your identity.
                        </div>
                        
                        <div class="mb-3">
                            <strong>Account:</strong> <?php echo htmlspecialchars($_SESSION['reset_user']['name']); ?>
                            (<?php echo htmlspecialchars($_SESSION['reset_user'][$_SESSION['reset_user_type'] === 'lecturer' ? 'staff_code' : 'matric_number']); ?>)
                        </div>

                        <form method="POST" action="">
                            <?php foreach ($_SESSION['reset_questions'] as $i => $question): ?>
                                <div class="mb-3">
                                    <label for="answer_<?php echo $i; ?>" class="form-label">
                                        <strong>Question <?php echo $i + 1; ?>:</strong> <?php echo htmlspecialchars($question['question_text']); ?>
                                    </label>
                                    <input type="text" class="form-control" id="answer_<?php echo $i; ?>" 
                                           name="answer_<?php echo $i; ?>" placeholder="Enter your answer" required>
                                </div>
                            <?php endforeach; ?>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-check me-2"></i>Verify Answers
                                </button>
                                <a href="reset_password_security.php?step=1" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>Back
                                </a>
                            </div>
                        </form>

                    <?php elseif ($step == 3): ?>
                        <!-- Step 3: New Password -->
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            Identity verified! Please enter your new password.
                        </div>

                        <form method="POST" action="">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="new_password" class="form-label">New Password</label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="new_password" name="new_password" 
                                               minlength="6" required>
                                        <button type="button" class="input-group-text" onclick="togglePasswordVisibility('new_password')">
                                            <i class="fas fa-eye" id="new-password-toggle-icon"></i>
                                        </button>
                                    </div>
                                    <div class="form-text">Password must be at least 6 characters long</div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="confirm_password" class="form-label">Confirm New Password</label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                               minlength="6" required>
                                        <button type="button" class="input-group-text" onclick="togglePasswordVisibility('confirm_password')">
                                            <i class="fas fa-eye" id="confirm-password-toggle-icon"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save me-2"></i>Reset Password
                                </button>
                            </div>
                        </form>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>

<script>
// Update labels based on user type selection
document.getElementById('user_type').addEventListener('change', function() {
    const userType = this.value;
    const identifierLabel = document.getElementById('identifier-label');
    const identifierHelp = document.getElementById('identifier-help');
    const identifierInput = document.getElementById('identifier');
    
    if (userType === 'lecturer') {
        identifierLabel.textContent = 'Staff Code or Email';
        identifierHelp.textContent = 'Enter your staff code or email address';
        identifierInput.placeholder = 'Enter your staff code or email';
    } else if (userType === 'student') {
        identifierLabel.textContent = 'Matric Number or Email';
        identifierHelp.textContent = 'Enter your matric number or email address';
        identifierInput.placeholder = 'Enter your matric number or email';
    } else {
        identifierLabel.textContent = 'Staff Code/Email or Matric Number/Email';
        identifierHelp.textContent = 'Enter your staff code or email (for lecturers) or matric number or email (for students)';
        identifierInput.placeholder = 'Enter your staff code, matric number, or email';
    }
});

// Password visibility toggle
function togglePasswordVisibility(fieldId) {
    const passwordField = document.getElementById(fieldId);
    const iconId = fieldId === 'new_password' ? 'new-password-toggle-icon' : 'confirm-password-toggle-icon';
    const toggleIcon = document.getElementById(iconId);

    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

// Password confirmation validation
if (document.getElementById('confirm_password')) {
    document.getElementById('confirm_password').addEventListener('input', function() {
        const newPassword = document.getElementById('new_password').value;
        const confirmPassword = this.value;
        
        if (confirmPassword && newPassword !== confirmPassword) {
            this.classList.add('is-invalid');
        } else {
            this.classList.remove('is-invalid');
        }
    });
}
</script>
