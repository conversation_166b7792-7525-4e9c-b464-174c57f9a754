<?php
// Only run if called directly or if explicitly requested
$show_output = isset($_GET['show_output']) || (php_sapi_name() === 'cli') || (basename($_SERVER['PHP_SELF']) === 'create_security_questions_tables.php');

if (!isset($conn)) {
    require_once 'database.php';
}

// Create security_questions table if it doesn't exist
$result = $conn->query("SHOW TABLES LIKE 'security_questions'");
if ($result->num_rows == 0) {
    $sql = "CREATE TABLE security_questions (
        question_id INT AUTO_INCREMENT PRIMARY KEY,
        question_text VARCHAR(255) NOT NULL,
        is_active TINYINT(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";

    if ($conn->query($sql)) {
        if ($show_output) echo "Security questions table created successfully<br>";

        // Insert default security questions (Windows-style)
        $default_questions = [
            "What was the name of your first pet?",
            "What is your mother's maiden name?",
            "What was the name of your first school?",
            "What is the name of the city where you were born?",
            "What was your childhood nickname?",
            "What is the name of your favorite childhood friend?",
            "What was the make of your first car?",
            "What is your father's middle name?",
            "What was the name of the street you grew up on?",
            "What is your favorite book?",
            "What was the name of your first employer?",
            "What is your favorite movie?",
            "What was your favorite food as a child?",
            "What is the name of your favorite teacher?",
            "What was the name of your first stuffed animal?"
        ];

        foreach ($default_questions as $question) {
            $stmt = $conn->prepare("INSERT INTO security_questions (question_text) VALUES (?)");
            $stmt->bind_param("s", $question);
            $stmt->execute();
        }

        if ($show_output) echo "Default security questions inserted successfully<br>";
    } else {
        if ($show_output) echo "Error creating security questions table: " . $conn->error . "<br>";
    }
} else {
    if ($show_output) echo "Security questions table already exists<br>";
}

// Create user_security_answers table for students if it doesn't exist
$result = $conn->query("SHOW TABLES LIKE 'student_security_answers'");
if ($result->num_rows == 0) {
    $sql = "CREATE TABLE student_security_answers (
        answer_id INT AUTO_INCREMENT PRIMARY KEY,
        student_id INT NOT NULL,
        question_id INT NOT NULL,
        answer_hash VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (student_id) REFERENCES students(student_id) ON DELETE CASCADE,
        FOREIGN KEY (question_id) REFERENCES security_questions(question_id),
        UNIQUE KEY unique_student_question (student_id, question_id)
    )";

    if ($conn->query($sql)) {
        if ($show_output) echo "Student security answers table created successfully<br>";
    } else {
        if ($show_output) echo "Error creating student security answers table: " . $conn->error . "<br>";
    }
} else {
    if ($show_output) echo "Student security answers table already exists<br>";
}

// Create user_security_answers table for lecturers if it doesn't exist
$result = $conn->query("SHOW TABLES LIKE 'lecturer_security_answers'");
if ($result->num_rows == 0) {
    $sql = "CREATE TABLE lecturer_security_answers (
        answer_id INT AUTO_INCREMENT PRIMARY KEY,
        lecturer_id INT NOT NULL,
        question_id INT NOT NULL,
        answer_hash VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (lecturer_id) REFERENCES lecturers(lecturer_id) ON DELETE CASCADE,
        FOREIGN KEY (question_id) REFERENCES security_questions(question_id),
        UNIQUE KEY unique_lecturer_question (lecturer_id, question_id)
    )";

    if ($conn->query($sql)) {
        if ($show_output) echo "Lecturer security answers table created successfully<br>";
    } else {
        if ($show_output) echo "Error creating lecturer security answers table: " . $conn->error . "<br>";
    }
} else {
    if ($show_output) echo "Lecturer security answers table already exists<br>";
}

if ($show_output) echo "Security questions database setup completed<br>";
?>
