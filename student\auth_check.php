<?php
require_once '../config/functions.php';

// Start the session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if student is logged in
if (!isset($_SESSION['student_id'])) {
    // Clear any existing session data
    $_SESSION = array();
    session_destroy();

    // Start a new session for the redirect
    session_start();
    $_SESSION['error_message'] = "You must login to access the student area.";

    // Redirect to login page
    header("Location: ../index.php");
    exit();
}

// Check for session timeout (30 minutes of inactivity)
$max_idle_time = 1800; // 30 minutes in seconds
if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > $max_idle_time)) {
    // Session has expired due to inactivity
    $_SESSION = array();
    session_destroy();

    // Start a new session for the redirect
    session_start();
    $_SESSION['error_message'] = "Your session has expired due to inactivity. Please login again.";

    // Redirect to login page
    header("Location: ../index.php");
    exit();
}

// Update last activity time
$_SESSION['last_activity'] = time();
?>
