<?php
require_once '../config/functions.php';
require_once 'auth_check.php';

// Check if course ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Location: courses.php');
    exit();
}

$course_id = sanitize($_GET['id']);

// Get course details
$stmt = $conn->prepare("SELECT * FROM courses WHERE course_id = ?");
$stmt->bind_param("i", $course_id);
$stmt->execute();
$course_result = $stmt->get_result();

if ($course_result->num_rows === 0) {
    header('Location: courses.php');
    exit();
}

$course = $course_result->fetch_assoc();

// Get departments, levels, and semesters for dropdown
$departments = $conn->query("SELECT * FROM departments ORDER BY dept_name");
$levels = $conn->query("SELECT * FROM levels ORDER BY level_id");
$semesters = $conn->query("SELECT DISTINCT semester_name, semester_id FROM semesters ORDER BY semester_id");

// Process form submission
$message = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $course_code = sanitize($_POST['course_code']);
    $course_title = sanitize($_POST['course_title']);
    $dept_id = sanitize($_POST['dept_id']);
    $level_id = sanitize($_POST['level_id']);
    $semester_id = sanitize($_POST['semester_id']);

    if (empty($course_code) || empty($course_title) || empty($dept_id) || empty($level_id) || empty($semester_id)) {
        $message = displayError("All fields are required");
    } else {
        // Check if course code already exists (excluding current course)
        $stmt = $conn->prepare("SELECT course_id FROM courses WHERE course_code = ? AND course_id != ?");
        $stmt->bind_param("si", $course_code, $course_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $message = displayError("Course code already exists");
        } else {
            // Generate new QR code data
            $qr_data = json_encode([
                'course_code' => $course_code,
                'course_title' => $course_title,
                'dept_id' => $dept_id,
                'level_id' => $level_id,
                'semester_id' => $semester_id,
                'timestamp' => time()
            ]);

            // Generate new QR code
            $qr_code = generateQRCode($qr_data);

            // Update course
            $stmt = $conn->prepare("UPDATE courses SET course_code = ?, course_title = ?, dept_id = ?, level_id = ?, semester_id = ?, qr_code = ? WHERE course_id = ?");
            $stmt->bind_param("ssiiisi", $course_code, $course_title, $dept_id, $level_id, $semester_id, $qr_code, $course_id);

            if ($stmt->execute()) {
                $message = displaySuccess("Course updated successfully!");
                // Refresh course data
                $stmt = $conn->prepare("SELECT * FROM courses WHERE course_id = ?");
                $stmt->bind_param("i", $course_id);
                $stmt->execute();
                $course_result = $stmt->get_result();
                $course = $course_result->fetch_assoc();
            } else {
                $message = displayError("Error updating course: " . $conn->error);
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Course - Ogbonnaya Onu Polytechnic</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="../images/logo.jpg" alt="Logo" width="30" height="30" class="d-inline-block align-text-top me-2 rounded-circle">
                Admin Dashboard
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Students
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="students.php">All Students</a></li>
                            <li><a class="dropdown-item" href="pending_students.php">Pending Approvals</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Lecturers
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="lecturers.php">All Lecturers</a></li>
                            <li><a class="dropdown-item" href="pending_lecturers.php">Pending Approvals</a></li>
                            <li><a class="dropdown-item" href="assign_courses.php">Assign Courses</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Courses
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="courses.php">All Courses</a></li>
                            <li><a class="dropdown-item" href="add_course.php">Add New Course</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Departments
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="departments.php">All Departments</a></li>
                            <li><a class="dropdown-item" href="add_department.php">Add New Department</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">Reports</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-1"></i> <?php echo $_SESSION['username']; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profile</a></li>
                            <li><a class="dropdown-item" href="change_password.php">Change Password</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col-md-12">
                <h2>Edit Course</h2>
                <p class="text-muted">Update course details and regenerate QR code</p>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="courses.php">Courses</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Edit Course</li>
                    </ol>
                </nav>
            </div>
        </div>

        <?php echo $message; ?>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Edit Course Details</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="">
                            <div class="mb-3">
                                <label for="course_code" class="form-label">Course Code</label>
                                <input type="text" class="form-control" id="course_code" name="course_code" value="<?php echo htmlspecialchars($course['course_code']); ?>" required>
                            </div>

                            <div class="mb-3">
                                <label for="course_title" class="form-label">Course Title</label>
                                <input type="text" class="form-control" id="course_title" name="course_title" value="<?php echo htmlspecialchars($course['course_title']); ?>" required>
                            </div>

                            <div class="mb-3">
                                <label for="dept_id" class="form-label">Department</label>
                                <select class="form-select" id="dept_id" name="dept_id" required>
                                    <option value="">Select Department</option>
                                    <?php while ($dept = $departments->fetch_assoc()): ?>
                                        <option value="<?php echo $dept['dept_id']; ?>" <?php echo ($dept['dept_id'] == $course['dept_id']) ? 'selected' : ''; ?>>
                                            <?php echo $dept['dept_name']; ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="level_id" class="form-label">Level</label>
                                <select class="form-select" id="level_id" name="level_id" required>
                                    <option value="">Select Level</option>
                                    <?php while ($level = $levels->fetch_assoc()): ?>
                                        <option value="<?php echo $level['level_id']; ?>" <?php echo ($level['level_id'] == $course['level_id']) ? 'selected' : ''; ?>>
                                            <?php echo $level['level_name']; ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="semester_id" class="form-label">Semester</label>
                                <select class="form-select" id="semester_id" name="semester_id" required>
                                    <option value="">Select Semester</option>
                                    <option value="1" <?php echo ($course['semester_id'] == 1) ? 'selected' : ''; ?>>First Semester</option>
                                    <option value="2" <?php echo ($course['semester_id'] == 2) ? 'selected' : ''; ?>>Second Semester</option>
                                </select>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Update Course
                                </button>
                                <a href="courses.php" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Back to Courses
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Current QR Code</h5>
                    </div>
                    <div class="card-body text-center">
                        <h6><?php echo htmlspecialchars($course['course_code'] . ' - ' . $course['course_title']); ?></h6>
                        <div class="qr-code my-3">
                            <img src="../<?php echo $course['qr_code']; ?>" alt="QR Code" class="img-fluid" style="max-width: 200px;">
                        </div>
                        <p class="mb-3">Scan this QR code to mark attendance for this course.</p>
                        <a href="../<?php echo $course['qr_code']; ?>" download="<?php echo $course['course_code']; ?>_QR.png" class="btn btn-outline-primary">
                            <i class="fas fa-download"></i> Download QR Code
                        </a>
                        <div class="alert alert-info mt-3">
                            <small><i class="fas fa-info-circle"></i> QR code will be automatically regenerated when you update the course.</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-3 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; <?php echo date('Y'); ?> Ogbonnaya Onu Polytechnic. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="../about.php" class="text-white me-3">About</a>
                    <a href="../support.php" class="text-white">Support</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Session Manager -->
    <script src="../assets/js/session_manager.js"></script>
</body>
</html>
