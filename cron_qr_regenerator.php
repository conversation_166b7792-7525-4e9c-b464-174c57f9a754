#!/usr/bin/env php
<?php
/**
 * Cron Job QR Code Regenerator
 * 
 * This script is designed to be run as a cron job to automatically
 * regenerate QR codes for all courses in the system.
 * 
 * To set up as a cron job, add this line to your crontab:
 * 0 */2 * * * /usr/bin/php /path/to/your/attendance/system/cron_qr_regenerator.php
 * 
 * This will run every 2 hours.
 */

// Ensure this script is only run from command line or with proper authentication
if (php_sapi_name() !== 'cli') {
    // If not CLI, check for secret key
    if (!isset($_GET['secret']) || $_GET['secret'] !== 'qr_cron_2025') {
        http_response_code(403);
        die('Access denied. This script should be run via cron job.');
    }
}

// Change to the script's directory
chdir(__DIR__);

// Include the background regenerator
require_once 'background_qr_regenerator.php';

// If run from CLI, output the result
if (php_sapi_name() === 'cli') {
    echo "QR Code regeneration cron job completed at " . date('Y-m-d H:i:s') . "\n";
}
?>
