<?php
require_once '../config/functions.php';

// Check if student is logged in
if (!isStudentLoggedIn()) {
    redirect('../index.php');
}

// Get student details
$student_id = $_SESSION['student_id'];
$student = getStudentDetails($student_id);

// Get department, level, and semester names
$dept_name = getDepartmentName($student['dept_id']);
$level_name = getLevelName($student['level_id']);
$semester_name = getSemesterName($student['semester_id']);

// Get missed lectures for this student
$missed_lectures = $conn->query("
    SELECT ml.*, c.course_code, c.course_title, cs.start_time, l.name as lecturer_name
    FROM missed_lectures ml
    JOIN courses c ON ml.course_id = c.course_id
    JOIN class_sessions cs ON ml.session_id = cs.session_id
    JOIN lecturers l ON cs.lecturer_id = l.lecturer_id
    WHERE ml.student_id = $student_id
    ORDER BY ml.session_date DESC
");

// Get attendance rate for each course
$courses = $conn->query("
    SELECT c.*, d.dept_name
    FROM courses c
    JOIN departments d ON c.dept_id = d.dept_id
    WHERE c.dept_id = {$student['dept_id']}
    AND c.level_id = {$student['level_id']}
    AND c.semester_id = {$student['semester_id']}
    ORDER BY c.course_code
");

$course_attendance_rates = [];
while ($course = $courses->fetch_assoc()) {
    // Get total sessions for this course
    $total_sessions_query = $conn->query("
        SELECT COUNT(*) as total
        FROM class_sessions
        WHERE course_id = {$course['course_id']} AND status = 'ended'
    ");
    $total_sessions = $total_sessions_query->fetch_assoc()['total'];

    // Get attended sessions for this course
    $attended_sessions_query = $conn->query("
        SELECT COUNT(*) as attended
        FROM attendance
        WHERE student_id = $student_id AND course_id = {$course['course_id']}
    ");
    $attended_sessions = $attended_sessions_query->fetch_assoc()['attended'];

    // Calculate attendance rate
    $attendance_rate = ($total_sessions > 0) ? ($attended_sessions / $total_sessions) * 100 : 0;

    $course_attendance_rates[$course['course_id']] = [
        'course_code' => $course['course_code'],
        'course_title' => $course['course_title'],
        'total_sessions' => $total_sessions,
        'attended_sessions' => $attended_sessions,
        'attendance_rate' => $attendance_rate
    ];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Missed Lectures - Student Dashboard</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .progress {
            height: 10px;
        }
        .attendance-good {
            background-color: #28a745;
        }
        .attendance-warning {
            background-color: #ffc107;
        }
        .attendance-danger {
            background-color: #dc3545;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="../images/logo.jpg" alt="Logo" width="30" height="30" class="d-inline-block align-text-top me-2 rounded-circle">
                Student Dashboard
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">My Courses</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="attendance.php">My Attendance</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="missed_lectures.php">Missed Lectures</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-1"></i> <?php echo $student['name']; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profile</a></li>
                            <li><a class="dropdown-item" href="change_password.php">Change Password</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col-md-12">
                <h2>Missed Lectures</h2>
                <p class="text-muted">View lectures you have missed and your attendance rates</p>
            </div>
        </div>

        <!-- Attendance Rates -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i> Attendance Rates</h5>
                    </div>
                    <div class="card-body">
                        <?php if (count($course_attendance_rates) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>Course Code</th>
                                            <th>Course Title</th>
                                            <th>Sessions Attended</th>
                                            <th>Total Sessions</th>
                                            <th>Attendance Rate</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($course_attendance_rates as $course_id => $data): ?>
                                            <tr>
                                                <td><?php echo $data['course_code']; ?></td>
                                                <td><?php echo $data['course_title']; ?></td>
                                                <td><?php echo $data['attended_sessions']; ?></td>
                                                <td><?php echo $data['total_sessions']; ?></td>
                                                <td>
                                                    <?php
                                                    $rate = $data['attendance_rate'];
                                                    $progress_class = 'attendance-danger';
                                                    if ($rate >= 75) {
                                                        $progress_class = 'attendance-good';
                                                    } elseif ($rate >= 50) {
                                                        $progress_class = 'attendance-warning';
                                                    }
                                                    ?>
                                                    <div class="progress">
                                                        <div class="progress-bar <?php echo $progress_class; ?>" role="progressbar" style="width: <?php echo $rate; ?>%" aria-valuenow="<?php echo $rate; ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                                    </div>
                                                    <small class="mt-1 d-block"><?php echo number_format($rate, 1); ?>%</small>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> No course data available.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Missed Lectures -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0"><i class="fas fa-calendar-times me-2"></i> Missed Lectures</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($missed_lectures->num_rows > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover data-table">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Course Code</th>
                                            <th>Course Title</th>
                                            <th>Lecturer</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($lecture = $missed_lectures->fetch_assoc()): ?>
                                            <tr>
                                                <td><?php echo date('d M Y', strtotime($lecture['session_date'])); ?></td>
                                                <td><?php echo $lecture['course_code']; ?></td>
                                                <td><?php echo $lecture['course_title']; ?></td>
                                                <td><?php echo $lecture['lecturer_name']; ?></td>
                                                <td><span class="badge bg-danger">Missed</span></td>
                                            </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i> Great job! You haven't missed any lectures.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-3 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; <?php echo date('Y'); ?> Ogbonnaya Onu Polytechnic. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="../about.php" class="text-white me-3">About</a>
                    <a href="../contact.php" class="text-white">Contact</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/script.js"></script>
    <script>
        $(document).ready(function() {
            $('.data-table').DataTable({
                "pageLength": 10,
                "order": [[0, "desc"]],
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
            });
        });
    </script>
</body>
</html>
