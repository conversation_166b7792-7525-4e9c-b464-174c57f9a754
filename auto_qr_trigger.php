<?php
/**
 * Auto QR Code Trigger
 * This file can be included in admin pages to automatically generate QR codes
 * for any courses that don't have them
 */

// Only run if we have a database connection
if (isset($conn) && $conn instanceof mysqli) {
    
    // QR Code generation function - Returns URL for dynamic generation
    function generateQRCode($data) {
        $encoded_data = urlencode($data);
        $qr_url = "https://chart.googleapis.com/chart?chs=300x300&cht=qr&chl=" . $encoded_data;
        return $qr_url;
    }
    
    // Auto-generate QR codes for courses without them
    function autoTriggerQRGeneration($conn) {
        $result = $conn->query("SELECT course_id, course_code, course_title FROM courses WHERE qr_code IS NULL OR qr_code = ''");
        $updated_count = 0;
        
        if ($result && $result->num_rows > 0) {
            while ($course = $result->fetch_assoc()) {
                $qr_data = "course_code=" . $course['course_code'] . "&course_title=" . $course['course_title'];
                $qr_code = generateQRCode($qr_data);
                
                $stmt = $conn->prepare("UPDATE courses SET qr_code = ? WHERE course_id = ?");
                $stmt->bind_param("si", $qr_code, $course['course_id']);
                if ($stmt->execute()) {
                    $updated_count++;
                }
            }
        }
        
        return $updated_count;
    }
    
    // Run the auto-trigger (silently in background)
    $auto_qr_count = autoTriggerQRGeneration($conn);
    
    // Optional: Store result in session for admin notification
    if ($auto_qr_count > 0 && isset($_SESSION)) {
        $_SESSION['auto_qr_generated'] = $auto_qr_count;
    }
}
?>
