<?php
// Security Questions Helper Functions

// Function to ensure security questions tables exist (silent)
function ensureSecurityQuestionsTables() {
    global $conn;

    // Create security_questions table if it doesn't exist
    $result = $conn->query("SHOW TABLES LIKE 'security_questions'");
    if ($result->num_rows == 0) {
        $sql = "CREATE TABLE security_questions (
            question_id INT AUTO_INCREMENT PRIMARY KEY,
            question_text VARCHAR(255) NOT NULL,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

        if ($conn->query($sql)) {
            // Insert default security questions (Windows-style)
            $default_questions = [
                "What was the name of your first pet?",
                "What is your mother's maiden name?",
                "What was the name of your first school?",
                "What is the name of the city where you were born?",
                "What was your childhood nickname?",
                "What is the name of your favorite childhood friend?",
                "What was the make of your first car?",
                "What is your father's middle name?",
                "What was the name of the street you grew up on?",
                "What is your favorite book?",
                "What was the name of your first employer?",
                "What is your favorite movie?",
                "What was your favorite food as a child?",
                "What is the name of your favorite teacher?",
                "What was the name of your first stuffed animal?"
            ];

            foreach ($default_questions as $question) {
                $stmt = $conn->prepare("INSERT INTO security_questions (question_text) VALUES (?)");
                $stmt->bind_param("s", $question);
                $stmt->execute();
            }
        }
    }

    // Create student_security_answers table if it doesn't exist
    $result = $conn->query("SHOW TABLES LIKE 'student_security_answers'");
    if ($result->num_rows == 0) {
        $sql = "CREATE TABLE student_security_answers (
            answer_id INT AUTO_INCREMENT PRIMARY KEY,
            student_id INT NOT NULL,
            question_id INT NOT NULL,
            answer_hash VARCHAR(255) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (student_id) REFERENCES students(student_id) ON DELETE CASCADE,
            FOREIGN KEY (question_id) REFERENCES security_questions(question_id),
            UNIQUE KEY unique_student_question (student_id, question_id)
        )";
        $conn->query($sql);
    }

    // Create lecturer_security_answers table if it doesn't exist
    $result = $conn->query("SHOW TABLES LIKE 'lecturer_security_answers'");
    if ($result->num_rows == 0) {
        $sql = "CREATE TABLE lecturer_security_answers (
            answer_id INT AUTO_INCREMENT PRIMARY KEY,
            lecturer_id INT NOT NULL,
            question_id INT NOT NULL,
            answer_hash VARCHAR(255) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (lecturer_id) REFERENCES lecturers(lecturer_id) ON DELETE CASCADE,
            FOREIGN KEY (question_id) REFERENCES security_questions(question_id),
            UNIQUE KEY unique_lecturer_question (lecturer_id, question_id)
        )";
        $conn->query($sql);
    }
}

/**
 * Get all active security questions
 */
function getSecurityQuestions() {
    global $conn;
    $result = $conn->query("SELECT question_id, question_text FROM security_questions WHERE is_active = 1 ORDER BY question_text");
    return $result->fetch_all(MYSQLI_ASSOC);
}

/**
 * Save security answers for a student
 */
function saveStudentSecurityAnswers($student_id, $question1_id, $answer1, $question2_id, $answer2) {
    global $conn;
    
    // Hash the answers for security
    $answer1_hash = password_hash(strtolower(trim($answer1)), PASSWORD_DEFAULT);
    $answer2_hash = password_hash(strtolower(trim($answer2)), PASSWORD_DEFAULT);
    
    // Begin transaction
    $conn->begin_transaction();
    
    try {
        // Delete any existing answers for this student
        $stmt = $conn->prepare("DELETE FROM student_security_answers WHERE student_id = ?");
        $stmt->bind_param("i", $student_id);
        $stmt->execute();
        
        // Insert new answers
        $stmt = $conn->prepare("INSERT INTO student_security_answers (student_id, question_id, answer_hash) VALUES (?, ?, ?)");
        
        // Insert first answer
        $stmt->bind_param("iis", $student_id, $question1_id, $answer1_hash);
        $stmt->execute();
        
        // Insert second answer
        $stmt->bind_param("iis", $student_id, $question2_id, $answer2_hash);
        $stmt->execute();
        
        $conn->commit();
        return true;
    } catch (Exception $e) {
        $conn->rollback();
        return false;
    }
}

/**
 * Save security answers for a lecturer
 */
function saveLecturerSecurityAnswers($lecturer_id, $question1_id, $answer1, $question2_id, $answer2) {
    global $conn;
    
    // Hash the answers for security
    $answer1_hash = password_hash(strtolower(trim($answer1)), PASSWORD_DEFAULT);
    $answer2_hash = password_hash(strtolower(trim($answer2)), PASSWORD_DEFAULT);
    
    // Begin transaction
    $conn->begin_transaction();
    
    try {
        // Delete any existing answers for this lecturer
        $stmt = $conn->prepare("DELETE FROM lecturer_security_answers WHERE lecturer_id = ?");
        $stmt->bind_param("i", $lecturer_id);
        $stmt->execute();
        
        // Insert new answers
        $stmt = $conn->prepare("INSERT INTO lecturer_security_answers (lecturer_id, question_id, answer_hash) VALUES (?, ?, ?)");
        
        // Insert first answer
        $stmt->bind_param("iis", $lecturer_id, $question1_id, $answer1_hash);
        $stmt->execute();
        
        // Insert second answer
        $stmt->bind_param("iis", $lecturer_id, $question2_id, $answer2_hash);
        $stmt->execute();
        
        $conn->commit();
        return true;
    } catch (Exception $e) {
        $conn->rollback();
        return false;
    }
}

/**
 * Get security questions for a student
 */
function getStudentSecurityQuestions($student_id) {
    global $conn;
    $stmt = $conn->prepare("
        SELECT sa.question_id, sq.question_text 
        FROM student_security_answers sa 
        JOIN security_questions sq ON sa.question_id = sq.question_id 
        WHERE sa.student_id = ? 
        ORDER BY sa.answer_id
    ");
    $stmt->bind_param("i", $student_id);
    $stmt->execute();
    $result = $stmt->get_result();
    return $result->fetch_all(MYSQLI_ASSOC);
}

/**
 * Get security questions for a lecturer
 */
function getLecturerSecurityQuestions($lecturer_id) {
    global $conn;
    $stmt = $conn->prepare("
        SELECT la.question_id, sq.question_text 
        FROM lecturer_security_answers la 
        JOIN security_questions sq ON la.question_id = sq.question_id 
        WHERE la.lecturer_id = ? 
        ORDER BY la.answer_id
    ");
    $stmt->bind_param("i", $lecturer_id);
    $stmt->execute();
    $result = $stmt->get_result();
    return $result->fetch_all(MYSQLI_ASSOC);
}

/**
 * Verify security answers for a student
 */
function verifyStudentSecurityAnswers($student_id, $answers) {
    global $conn;
    $stmt = $conn->prepare("
        SELECT sa.question_id, sa.answer_hash 
        FROM student_security_answers sa 
        WHERE sa.student_id = ? 
        ORDER BY sa.answer_id
    ");
    $stmt->bind_param("i", $student_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $stored_answers = $result->fetch_all(MYSQLI_ASSOC);
    
    if (count($stored_answers) != count($answers)) {
        return false;
    }
    
    for ($i = 0; $i < count($stored_answers); $i++) {
        $provided_answer = strtolower(trim($answers[$i]));
        if (!password_verify($provided_answer, $stored_answers[$i]['answer_hash'])) {
            return false;
        }
    }
    
    return true;
}

/**
 * Verify security answers for a lecturer
 */
function verifyLecturerSecurityAnswers($lecturer_id, $answers) {
    global $conn;
    $stmt = $conn->prepare("
        SELECT la.question_id, la.answer_hash 
        FROM lecturer_security_answers la 
        WHERE la.lecturer_id = ? 
        ORDER BY la.answer_id
    ");
    $stmt->bind_param("i", $lecturer_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $stored_answers = $result->fetch_all(MYSQLI_ASSOC);
    
    if (count($stored_answers) != count($answers)) {
        return false;
    }
    
    for ($i = 0; $i < count($stored_answers); $i++) {
        $provided_answer = strtolower(trim($answers[$i]));
        if (!password_verify($provided_answer, $stored_answers[$i]['answer_hash'])) {
            return false;
        }
    }
    
    return true;
}

/**
 * Find student by matric number or email
 */
function findStudentByIdentifier($identifier) {
    global $conn;
    $stmt = $conn->prepare("SELECT student_id, name, matric_number, email FROM students WHERE matric_number = ? OR email = ?");
    $stmt->bind_param("ss", $identifier, $identifier);
    $stmt->execute();
    $result = $stmt->get_result();
    return $result->fetch_assoc();
}

/**
 * Find lecturer by staff code or email
 */
function findLecturerByIdentifier($identifier) {
    global $conn;
    $stmt = $conn->prepare("SELECT lecturer_id, name, staff_code, email FROM lecturers WHERE staff_code = ? OR email = ?");
    $stmt->bind_param("ss", $identifier, $identifier);
    $stmt->execute();
    $result = $stmt->get_result();
    return $result->fetch_assoc();
}

/**
 * Reset password for student
 */
function resetStudentPassword($student_id, $new_password) {
    global $conn;
    $hashed_password = hashPassword($new_password);
    $stmt = $conn->prepare("UPDATE students SET password = ? WHERE student_id = ?");
    $stmt->bind_param("si", $hashed_password, $student_id);
    return $stmt->execute();
}

/**
 * Reset password for lecturer
 */
function resetLecturerPassword($lecturer_id, $new_password) {
    global $conn;
    $hashed_password = hashPassword($new_password);
    $stmt = $conn->prepare("UPDATE lecturers SET password = ? WHERE lecturer_id = ?");
    $stmt->bind_param("si", $hashed_password, $lecturer_id);
    return $stmt->execute();
}
?>
