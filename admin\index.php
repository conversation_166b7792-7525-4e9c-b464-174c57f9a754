<?php
// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once '../config/functions.php';
require_once 'auth_check.php';

// Get current academic session
$current_session = getCurrentAcademicSession();

// Get statistics for dashboard
$totalStudents = $conn->query("SELECT COUNT(*) as count FROM students")->fetch_assoc()['count'];
$pendingStudents = $conn->query("SELECT COUNT(*) as count FROM students WHERE status = 'pending'")->fetch_assoc()['count'];
$totalLecturers = $conn->query("SELECT COUNT(*) as count FROM lecturers")->fetch_assoc()['count'];
$pendingLecturers = $conn->query("SELECT COUNT(*) as count FROM lecturers WHERE status = 'pending'")->fetch_assoc()['count'];
$totalCourses = $conn->query("SELECT COUNT(*) as count FROM courses")->fetch_assoc()['count'];
$totalDepartments = $conn->query("SELECT COUNT(*) as count FROM departments")->fetch_assoc()['count'];
$totalAttendance = $conn->query("SELECT COUNT(*) as count FROM attendance")->fetch_assoc()['count'];

// Get recent activities
$recentStudents = $conn->query("SELECT * FROM students ORDER BY created_at DESC LIMIT 5");
$recentLecturers = $conn->query("SELECT * FROM lecturers ORDER BY created_at DESC LIMIT 5");

// Get admin notification count
$unreadNotifications = getAdminNotificationCount();

// Check if attendance table has created_at column
$result = $conn->query("SHOW COLUMNS FROM attendance LIKE 'created_at'");
if ($result && $result->num_rows > 0) {
    $recentAttendance = $conn->query("
        SELECT a.*, s.name as student_name, s.matric_number, c.course_code, c.course_title
        FROM attendance a
        JOIN students s ON a.student_id = s.student_id
        JOIN courses c ON a.course_id = c.course_id
        ORDER BY a.created_at DESC LIMIT 5
    ");
} else {
    // Use attendance_date and attendance_time instead
    $recentAttendance = $conn->query("
        SELECT a.*, s.name as student_name, s.matric_number, c.course_code, c.course_title
        FROM attendance a
        JOIN students s ON a.student_id = s.student_id
        JOIN courses c ON a.course_id = c.course_id
        ORDER BY a.attendance_date DESC, a.attendance_time DESC LIMIT 5
    ");
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Ogbonnaya Onu Polytechnic</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="../images/logo.jpg" alt="Logo" width="30" height="30" class="d-inline-block align-text-top me-2 rounded-circle">
                Admin Dashboard
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="academic_sessions.php">
                            <i class="fas fa-calendar-alt me-1"></i>Academic Sessions
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Students
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="students.php">All Students</a></li>
                            <li><a class="dropdown-item" href="pending_students.php">Pending Approvals</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Lecturers
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="lecturers.php">All Lecturers</a></li>
                            <li><a class="dropdown-item" href="pending_lecturers.php">Pending Approvals</a></li>
                            <li><a class="dropdown-item" href="assign_courses.php">Assign Courses</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Courses
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="courses.php">All Courses</a></li>
                            <li><a class="dropdown-item" href="add_course.php">Add New Course</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Departments
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="departments.php">All Departments</a></li>
                            <li><a class="dropdown-item" href="add_department.php">Add New Department</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Reports
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="reports.php">Attendance Reports</a></li>
                            <li><a class="dropdown-item" href="absent_students.php">Absent Students</a></li>
                        </ul>
                    </li>

                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link position-relative" href="notifications.php">
                            <i class="fas fa-bell"></i>
                            <?php if ($unreadNotifications > 0): ?>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                    <?php echo $unreadNotifications > 99 ? '99+' : $unreadNotifications; ?>
                                    <span class="visually-hidden">unread notifications</span>
                                </span>
                            <?php endif; ?>
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-1"></i> <?php echo $_SESSION['username']; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profile</a></li>
                            <li><a class="dropdown-item" href="change_password.php">Change Password</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <!-- Header Section with Current Session -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-1">Admin Dashboard</h2>
                        <p class="text-muted mb-0">Welcome to the Ogbonnaya Onu Polytechnic Attendance System Admin Panel</p>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <small class="text-muted d-block">Current Academic Session</small>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-calendar-alt text-primary me-2"></i>
                                <span class="fw-bold text-dark"><?php echo $current_session['academic_year']; ?></span>
                                <span class="mx-2">•</span>
                                <span class="text-primary"><?php echo $current_session['semester_name']; ?></span>
                            </div>
                        </div>
                        <a href="academic_sessions.php" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-cog me-1"></i>Manage
                        </a>
                    </div>
                </div>
                <hr class="mt-3 mb-0">
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="row dashboard-stats mb-4">
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-user-graduate dashboard-icon"></i>
                        <h5 class="card-title">Total Students</h5>
                        <h2 class="card-text"><?php echo $totalStudents; ?></h2>
                        <p class="text-muted"><?php echo $pendingStudents; ?> pending approval</p>
                    </div>
                    <div class="card-footer">
                        <a href="students.php" class="btn btn-sm btn-primary">View All</a>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-chalkboard-teacher dashboard-icon"></i>
                        <h5 class="card-title">Total Lecturers</h5>
                        <h2 class="card-text"><?php echo $totalLecturers; ?></h2>
                        <p class="text-muted"><?php echo $pendingLecturers; ?> pending approval</p>
                    </div>
                    <div class="card-footer">
                        <a href="lecturers.php" class="btn btn-sm btn-primary">View All</a>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-book dashboard-icon"></i>
                        <h5 class="card-title">Total Courses</h5>
                        <h2 class="card-text"><?php echo $totalCourses; ?></h2>
                        <p class="text-muted"><?php echo $totalDepartments; ?> departments</p>
                    </div>
                    <div class="card-footer">
                        <a href="courses.php" class="btn btn-sm btn-primary">View All</a>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-clipboard-check dashboard-icon"></i>
                        <h5 class="card-title">Total Attendance</h5>
                        <h2 class="card-text"><?php echo $totalAttendance; ?></h2>
                        <p class="text-muted">Across all courses</p>
                    </div>
                    <div class="card-footer">
                        <a href="reports.php" class="btn btn-sm btn-primary">View Reports</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 col-sm-6 mb-2">
                                <a href="add_department.php" class="btn btn-outline-primary d-block">
                                    <i class="fas fa-plus-circle me-2"></i> Add Department
                                </a>
                            </div>
                            <div class="col-md-3 col-sm-6 mb-2">
                                <a href="add_course.php" class="btn btn-outline-primary d-block">
                                    <i class="fas fa-plus-circle me-2"></i> Add Course
                                </a>
                            </div>
                            <div class="col-md-3 col-sm-6 mb-2">
                                <a href="pending_students.php" class="btn btn-outline-warning d-block">
                                    <i class="fas fa-user-check me-2"></i> Approve Students
                                </a>
                            </div>
                            <div class="col-md-3 col-sm-6 mb-2">
                                <a href="pending_lecturers.php" class="btn btn-outline-warning d-block">
                                    <i class="fas fa-user-check me-2"></i> Approve Lecturers
                                </a>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-3 col-sm-6 mb-2">
                                <a href="students.php" class="btn btn-outline-info d-block">
                                    <i class="fas fa-user-graduate me-2"></i> View All Students
                                </a>
                            </div>
                            <div class="col-md-3 col-sm-6 mb-2">
                                <a href="lecturers.php" class="btn btn-outline-info d-block">
                                    <i class="fas fa-chalkboard-teacher me-2"></i> View All Lecturers
                                </a>
                            </div>
                            <div class="col-md-3 col-sm-6 mb-2">
                                <a href="absent_students.php" class="btn btn-outline-danger d-block">
                                    <i class="fas fa-user-times me-2"></i> Absent Students
                                </a>
                            </div>
                            <div class="col-md-3 col-sm-6 mb-2">
                                <button onclick="triggerManualQRRegeneration()" class="btn btn-outline-success d-block" id="qr-regen-btn">
                                    <i class="fas fa-qrcode me-2"></i> Regenerate QR Codes
                                </button>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="row">
            <!-- Recent Students -->
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h5>Recent Student Registrations</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($recentStudents && $recentStudents->num_rows > 0): ?>
                            <ul class="list-group">
                                <?php while ($student = $recentStudents->fetch_assoc()): ?>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong><?php echo $student['name']; ?></strong><br>
                                            <small class="text-muted"><?php echo $student['matric_number']; ?></small>
                                        </div>
                                        <span class="badge rounded-pill <?php echo $student['status'] === 'approved' ? 'bg-success' : ($student['status'] === 'pending' ? 'bg-warning' : 'bg-danger'); ?>">
                                            <?php echo ucfirst($student['status']); ?>
                                        </span>
                                    </li>
                                <?php endwhile; ?>
                            </ul>
                        <?php else: ?>
                            <p class="text-center">No recent student registrations</p>
                        <?php endif; ?>
                    </div>
                    <div class="card-footer text-center">
                        <a href="students.php" class="btn btn-sm btn-primary">View All Students</a>
                    </div>
                </div>
            </div>

            <!-- Recent Lecturers -->
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h5>Recent Lecturer Registrations</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($recentLecturers && $recentLecturers->num_rows > 0): ?>
                            <ul class="list-group">
                                <?php while ($lecturer = $recentLecturers->fetch_assoc()): ?>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong><?php echo $lecturer['name']; ?></strong><br>
                                            <small class="text-muted"><?php echo $lecturer['staff_code']; ?></small>
                                        </div>
                                        <span class="badge rounded-pill <?php echo $lecturer['status'] === 'approved' ? 'bg-success' : ($lecturer['status'] === 'pending' ? 'bg-warning' : 'bg-danger'); ?>">
                                            <?php echo ucfirst($lecturer['status']); ?>
                                        </span>
                                    </li>
                                <?php endwhile; ?>
                            </ul>
                        <?php else: ?>
                            <p class="text-center">No recent lecturer registrations</p>
                        <?php endif; ?>
                    </div>
                    <div class="card-footer text-center">
                        <a href="lecturers.php" class="btn btn-sm btn-primary">View All Lecturers</a>
                    </div>
                </div>
            </div>

            <!-- Recent Attendance -->
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h5>Recent Attendance</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($recentAttendance && $recentAttendance->num_rows > 0): ?>
                            <ul class="list-group">
                                <?php while ($attendance = $recentAttendance->fetch_assoc()): ?>
                                    <li class="list-group-item">
                                        <div>
                                            <strong><?php echo $attendance['student_name']; ?></strong>
                                            <small>(<?php echo $attendance['matric_number']; ?>)</small><br>
                                            <small class="text-muted">
                                                <?php echo $attendance['course_code']; ?> -
                                                <?php echo date('d M Y, h:i A', strtotime($attendance['attendance_date'] . ' ' . $attendance['attendance_time'])); ?>
                                            </small>
                                        </div>
                                    </li>
                                <?php endwhile; ?>
                            </ul>
                        <?php else: ?>
                            <p class="text-center">No recent attendance records</p>
                        <?php endif; ?>
                    </div>
                    <div class="card-footer text-center">
                        <a href="reports.php" class="btn btn-sm btn-primary">View All Attendance</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add extra space before footer -->
    <div class="container">
        <div class="row">
            <div class="col-12" style="height: 100px;"></div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-3 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; <?php echo date('Y'); ?> Ogbonnaya Onu Polytechnic. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="../about.php" class="text-white me-3">About</a>
                    <a href="../support.php" class="text-white">Support</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/script.js"></script>
    <!-- Session Manager -->
    <script src="../assets/js/session_manager.js"></script>
    <!-- Notification Manager -->
    <script src="assets/js/notification_manager.js"></script>

    <script>
        // Manual QR regeneration function
        async function triggerManualQRRegeneration() {
            const btn = document.getElementById('qr-regen-btn');
            const originalText = btn.innerHTML;

            // Show loading state
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Regenerating...';

            try {
                if (window.triggerQRRegeneration) {
                    await window.triggerQRRegeneration();

                    // Show success state
                    btn.innerHTML = '<i class="fas fa-check me-2"></i> Completed!';
                    btn.className = 'btn btn-success d-block';

                    // Reset after 3 seconds
                    setTimeout(() => {
                        btn.disabled = false;
                        btn.innerHTML = originalText;
                        btn.className = 'btn btn-outline-success d-block';
                    }, 3000);
                } else {
                    throw new Error('QR regeneration service not available');
                }
            } catch (error) {
                console.error('Manual QR regeneration failed:', error);

                // Show error state
                btn.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i> Failed';
                btn.className = 'btn btn-danger d-block';

                // Reset after 3 seconds
                setTimeout(() => {
                    btn.disabled = false;
                    btn.innerHTML = originalText;
                    btn.className = 'btn btn-outline-success d-block';
                }, 3000);
            }
        }

        // Show QR regeneration status on page load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                if (window.getQRRegenerationStatus) {
                    const status = window.getQRRegenerationStatus();
                    console.log('QR Regeneration Status:', status);
                }
            }, 2000);
        });
    </script>
</body>
</html>
