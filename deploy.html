<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Attendance System - Deployment Portal</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: url('images/image2.jpg') center/cover no-repeat fixed;
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow-x: hidden;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.4);
            z-index: 0;
        }

        .deployment-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            padding: 3rem;
            max-width: 800px;
            width: 90%;
            text-align: center;
            position: relative;
            overflow: hidden;
            z-index: 1;
        }

        .deployment-container::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transform: rotate(45deg);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .logo-section {
            margin-bottom: 2rem;
            position: relative;
            z-index: 2;
        }

        .school-logo {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 4px solid #667eea;
            margin-bottom: 1rem;
            animation: pulse 2s infinite;
            object-fit: cover;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .logo-icon {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 1rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .institution-name {
            font-size: 1.8rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .system-title {
            font-size: 1.3rem;
            font-weight: 500;
            color: #667eea;
            margin-bottom: 1rem;
        }

        .system-subtitle {
            font-size: 1rem;
            color: #6c757d;
            margin-bottom: 2rem;
            font-style: italic;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
            position: relative;
            z-index: 2;
        }

        .feature-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 1.5rem;
            border: 1px solid rgba(102, 126, 234, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.2);
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .feature-icon {
            font-size: 2rem;
            color: #667eea;
            margin-bottom: 1rem;
        }

        .feature-title {
            font-size: 1rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .feature-desc {
            font-size: 0.9rem;
            color: #6c757d;
            line-height: 1.4;
        }

        .deploy-section {
            margin-top: 3rem;
            position: relative;
            z-index: 2;
        }

        .deploy-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            font-size: 1.2rem;
            font-weight: 600;
            padding: 1rem 3rem;
            border-radius: 50px;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }

        .deploy-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .deploy-btn:active {
            transform: translateY(-1px);
        }

        .deploy-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .deploy-btn:hover::before {
            left: 100%;
        }

        .status-section {
            margin-top: 2rem;
            padding: 1.5rem;
            background: rgba(248, 249, 250, 0.8);
            border-radius: 15px;
            border-left: 4px solid #667eea;
            display: none;
            position: relative;
            z-index: 2;
        }

        .status-section.show {
            display: block;
            animation: slideIn 0.5s ease;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(102, 126, 234, 0.3);
            border-radius: 50%;
            border-top-color: #667eea;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .footer-text {
            margin-top: 2rem;
            font-size: 0.9rem;
            color: #6c757d;
            position: relative;
            z-index: 2;
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .floating-element {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }

        .floating-element:nth-child(1) {
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-element:nth-child(2) {
            top: 20%;
            right: 10%;
            animation-delay: 2s;
        }

        .floating-element:nth-child(3) {
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }

        .floating-element:nth-child(4) {
            bottom: 10%;
            right: 20%;
            animation-delay: 1s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        @media (max-width: 768px) {
            .deployment-container {
                padding: 2rem;
                margin: 1rem;
            }
            
            .institution-name {
                font-size: 1.4rem;
            }
            
            .system-title {
                font-size: 1.1rem;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Floating Background Elements -->
    <div class="floating-elements">
        <i class="fas fa-qrcode floating-element" style="font-size: 3rem;"></i>
        <i class="fas fa-graduation-cap floating-element" style="font-size: 2.5rem;"></i>
        <i class="fas fa-users floating-element" style="font-size: 3.5rem;"></i>
        <i class="fas fa-chart-line floating-element" style="font-size: 2rem;"></i>
    </div>

    <div class="deployment-container">
        <!-- Logo Section -->
        <div class="logo-section">
            <img src="images/logo.jpg" alt="Ogbonnaya Onu Polytechnic Logo" class="school-logo">
            <h1 class="institution-name">Ogbonnaya Onu Polytechnic</h1>
            <h2 class="system-title">Enhanced Attendance Management System</h2>
            <p class="system-subtitle">Mitigating Proxy Attendance Through Advanced QR Code Technology</p>
        </div>

        <!-- Features Grid -->
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-qrcode"></i>
                </div>
                <div class="feature-title">Anti-Proxy System</div>
                <div class="feature-desc">Eliminates proxy attendance with secure QR verification</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-users-cog"></i>
                </div>
                <div class="feature-title">User Management</div>
                <div class="feature-desc">Complete admin, lecturer & student workflow</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <div class="feature-title">Analytics</div>
                <div class="feature-desc">Real-time reports and attendance insights</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="feature-title">Fraud Prevention</div>
                <div class="feature-desc">Advanced security measures to prevent attendance fraud</div>
            </div>
        </div>

        <!-- Deploy Section -->
        <div class="deploy-section">
            <h3 class="mb-3" style="color: #2c3e50; font-weight: 600;">Ready to Deploy?</h3>
            <p class="text-muted mb-4">Click the button below to automatically configure and setup your anti-proxy attendance system</p>
            
            <button class="btn deploy-btn" onclick="deploySystem()">
                <i class="fas fa-rocket me-2"></i>
                Deploy System
            </button>
        </div>

        <!-- Status Section -->
        <div class="status-section" id="statusSection">
            <div id="statusContent">
                <div class="loading-spinner"></div>
                <span id="statusText">Initializing deployment...</span>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer-text">
            <i class="fas fa-shield-alt me-1"></i>
            Secure • Reliable • Efficient Attendance Management
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function deploySystem() {
            const statusSection = document.getElementById('statusSection');
            const statusText = document.getElementById('statusText');
            const deployBtn = document.querySelector('.deploy-btn');
            
            // Show status section
            statusSection.classList.add('show');
            
            // Disable button
            deployBtn.disabled = true;
            deployBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Deploying...';
            
            // Simulate deployment steps
            const steps = [
                'Checking system requirements...',
                'Configuring database connection...',
                'Creating database tables...',
                'Setting up admin account...',
                'Installing course data...',
                'Finalizing configuration...',
                'Deployment complete!'
            ];
            
            let currentStep = 0;
            
            const updateStatus = () => {
                if (currentStep < steps.length - 1) {
                    statusText.textContent = steps[currentStep];
                    currentStep++;
                    setTimeout(updateStatus, 1500);
                } else {
                    // Final step
                    statusText.textContent = steps[currentStep];
                    document.querySelector('.loading-spinner').style.display = 'none';
                    
                    // Redirect to setup after a short delay
                    setTimeout(() => {
                        window.location.href = 'setup.php';
                    }, 2000);
                }
            };
            
            // Start the deployment simulation
            setTimeout(updateStatus, 1000);
        }

        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Animate feature cards on scroll
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.animation = 'slideIn 0.6s ease forwards';
                    }
                });
            }, observerOptions);

            document.querySelectorAll('.feature-card').forEach(card => {
                observer.observe(card);
            });
        });
    </script>
</body>
</html>
