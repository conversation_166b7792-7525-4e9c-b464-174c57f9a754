/**
 * Admin Notification Manager
 * Handles real-time notification updates and alerts
 */

class NotificationManager {
    constructor() {
        this.lastNotificationCount = 0;
        this.checkInterval = 30000; // 30 seconds
        this.isInitialized = false;
        this.hasShownInitialAlert = false;
        this.activeToasts = new Set(); // Track active toasts to prevent duplicates
        this.init();
    }

    init() {
        // Initialize notification count without showing alerts
        this.updateNotificationCount(true);

        // Set up periodic checking
        setInterval(() => {
            this.updateNotificationCount();
        }, this.checkInterval);

        // Mark as initialized after a short delay to prevent initial alerts
        setTimeout(() => {
            this.isInitialized = true;
            this.hasShownInitialAlert = true;
        }, 2000);

        // Add click handler to notification bell to clear toasts
        this.setupNotificationBellHandler();

        console.log('Notification Manager initialized');
    }

    async updateNotificationCount(isInitialLoad = false) {
        try {
            const response = await fetch('get_notification_count.php');
            const data = await response.json();

            if (data.success) {
                const currentCount = data.unread_notifications;

                // Only show alerts if:
                // 1. Not initial load
                // 2. System is initialized
                // 3. Current count is greater than last known count
                // 4. We haven't shown the initial alert yet (prevents duplicate alerts on page load)
                if (!isInitialLoad && this.isInitialized && this.hasShownInitialAlert && currentCount > this.lastNotificationCount) {
                    const newNotifications = currentCount - this.lastNotificationCount;
                    this.showNewNotificationAlert(newNotifications);
                    this.playNotificationSound();
                }

                this.lastNotificationCount = currentCount;
                this.updateNotificationBadge(currentCount);
                this.updateDashboardStats(data);
            }
        } catch (error) {
            console.error('Error fetching notification count:', error);
        }
    }

    updateNotificationBadge(count) {
        const notificationBadge = document.querySelector('.navbar-nav .nav-link .badge');
        const notificationLink = document.querySelector('.navbar-nav .nav-link[href="notifications.php"]');
        
        if (count > 0) {
            if (notificationBadge) {
                notificationBadge.textContent = count > 99 ? '99+' : count;
            } else if (notificationLink) {
                // Create badge if it doesn't exist
                const badge = document.createElement('span');
                badge.className = 'position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger';
                badge.innerHTML = `${count > 99 ? '99+' : count}<span class="visually-hidden">unread notifications</span>`;
                notificationLink.appendChild(badge);
            }
        } else {
            // Remove badge if no unread notifications
            if (notificationBadge) {
                notificationBadge.remove();
            }
        }
    }

    updateDashboardStats(data) {
        // Update pending students count
        const pendingStudentsElement = document.querySelector('.card-text + p');
        if (pendingStudentsElement && pendingStudentsElement.textContent.includes('pending approval')) {
            pendingStudentsElement.textContent = `${data.pending_students} pending approval`;
        }
        
        // Update pending lecturers count
        const pendingLecturersElements = document.querySelectorAll('.card-text + p');
        if (pendingLecturersElements.length > 1) {
            const pendingLecturersElement = pendingLecturersElements[1];
            if (pendingLecturersElement && pendingLecturersElement.textContent.includes('pending approval')) {
                pendingLecturersElement.textContent = `${data.pending_lecturers} pending approval`;
            }
        }
    }

    showNewNotificationAlert(newCount) {
        // Prevent duplicate toasts
        const toastId = `notification-toast-${Date.now()}`;
        if (this.activeToasts.size > 0) {
            // If there's already an active toast, don't show another one
            return;
        }

        // Create toast notification
        const toast = document.createElement('div');
        toast.className = 'toast position-fixed top-0 end-0 m-3';
        toast.style.zIndex = '9999';
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');
        toast.setAttribute('data-toast-id', toastId);

        toast.innerHTML = `
            <div class="toast-header bg-primary text-white">
                <i class="fas fa-bell me-2"></i>
                <strong class="me-auto">New Notification${newCount > 1 ? 's' : ''}</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                ${newCount} new registration${newCount > 1 ? 's' : ''} require${newCount === 1 ? 's' : ''} your approval.
                <div class="mt-2">
                    <a href="notifications.php" class="btn btn-sm btn-primary">View Notifications</a>
                </div>
            </div>
        `;

        document.body.appendChild(toast);
        this.activeToasts.add(toastId);

        // Show toast
        const bsToast = new bootstrap.Toast(toast, {
            autohide: true,
            delay: 8000
        });
        bsToast.show();

        // Remove toast element after it's hidden
        toast.addEventListener('hidden.bs.toast', () => {
            this.activeToasts.delete(toastId);
            toast.remove();
        });

        // Add pulse animation to notification bell
        const notificationLink = document.querySelector('.navbar-nav .nav-link[href="notifications.php"]');
        if (notificationLink) {
            notificationLink.classList.add('notification-pulse');
            setTimeout(() => {
                notificationLink.classList.remove('notification-pulse');
            }, 3000);
        }
    }

    playNotificationSound() {
        // Create audio element for notification sound
        try {
            // Use Web Audio API to create a simple notification beep
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);
            
            gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.3);
        } catch (error) {
            console.log('Audio notification not supported:', error);
        }
    }

    // Method to manually refresh notifications (without showing alerts)
    refresh() {
        this.updateNotificationCount(true);
    }

    // Method to force immediate refresh (useful after admin actions)
    forceRefresh() {
        this.clearActiveToasts();
        this.updateNotificationCount(true);
    }

    // Method to reset the notification state (useful when user views notifications)
    resetNotificationState() {
        this.hasShownInitialAlert = true;
        // Clear any active toasts
        this.clearActiveToasts();
        // Update count without showing alerts
        this.updateNotificationCount(true);
    }

    // Method to clear all active toasts
    clearActiveToasts() {
        const activeToastElements = document.querySelectorAll('[data-toast-id]');
        activeToastElements.forEach(toast => {
            const bsToast = bootstrap.Toast.getInstance(toast);
            if (bsToast) {
                bsToast.hide();
            }
        });
        this.activeToasts.clear();
    }

    // Setup click handler for notification bell
    setupNotificationBellHandler() {
        document.addEventListener('click', (e) => {
            const notificationLink = e.target.closest('a[href="notifications.php"]');
            if (notificationLink) {
                // Clear toasts when user clicks notification bell
                this.clearActiveToasts();
            }

            // Handle approval/rejection buttons
            const approveBtn = e.target.closest('button[name="approve"]');
            const rejectBtn = e.target.closest('button[name="reject"]');

            if (approveBtn || rejectBtn) {
                // Refresh notification count after approval/rejection
                setTimeout(() => {
                    this.updateNotificationCount(true);
                }, 1000);
            }
        });
    }
}

// CSS for notification pulse animation
const style = document.createElement('style');
style.textContent = `
    @keyframes notification-pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }
    
    .notification-pulse {
        animation: notification-pulse 0.6s ease-in-out 3;
    }
    
    .toast {
        min-width: 300px;
    }
`;
document.head.appendChild(style);

// Initialize notification manager when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize on admin pages
    if (window.location.pathname.includes('/admin/')) {
        window.notificationManager = new NotificationManager();
    }
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NotificationManager;
}
