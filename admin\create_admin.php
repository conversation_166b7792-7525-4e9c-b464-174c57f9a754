<?php
require_once '../config/functions.php';

// Create admin account with hardcoded credentials
$username = 'admin';
$password = 'admin@2023';

// Check if admin table exists
$tableExists = $conn->query("SHOW TABLES LIKE 'admin'")->num_rows > 0;

if (!$tableExists) {
    // Create admin table
    $conn->query("
        CREATE TABLE admin (
            admin_id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ");
    echo "Admin table created.<br>";
}

// Check if admin account exists
$stmt = $conn->prepare("SELECT admin_id FROM admin WHERE username = ?");
$stmt->bind_param("s", $username);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    // Update existing admin password
    $hashed_password = hashPassword($password);
    $stmt = $conn->prepare("UPDATE admin SET password = ? WHERE username = ?");
    $stmt->bind_param("ss", $hashed_password, $username);

    if ($stmt->execute()) {
        echo "Admin password updated successfully!<br>";
        echo "Username: $username<br>";
        echo "Password: $password<br>";
    } else {
        echo "Error updating admin password: " . $conn->error . "<br>";
    }
} else {
    // Insert new admin account
    $hashed_password = hashPassword($password);
    $stmt = $conn->prepare("INSERT INTO admin (username, password) VALUES (?, ?)");
    $stmt->bind_param("ss", $username, $hashed_password);

    if ($stmt->execute()) {
        echo "Admin account created successfully!<br>";
        echo "Username: $username<br>";
        echo "Password: $password<br>";
    } else {
        echo "Error creating admin account: " . $conn->error . "<br>";
    }
}

// Display raw password hash for debugging
echo "<hr>";
echo "Debug info:<br>";
echo "Raw password: $password<br>";
echo "Hashed password: " . hashPassword($password) . "<br>";
echo "<a href='../index.php'>Go to Login Page</a>";
?>
