<?php
require_once '../config/functions.php';
require_once 'auth_check.php';

// Get departments, levels, and semesters for dropdown
$departments = $conn->query("SELECT * FROM departments ORDER BY dept_name");
$levels = $conn->query("SELECT * FROM levels ORDER BY level_id");
$semesters = $conn->query("SELECT DISTINCT semester_name, semester_id FROM semesters ORDER BY semester_id");

// Process form submission
$message = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $course_code = sanitize($_POST['course_code']);
    $course_title = sanitize($_POST['course_title']);
    $dept_id = sanitize($_POST['dept_id']);
    $level_id = sanitize($_POST['level_id']);
    $semester_id = sanitize($_POST['semester_id']);

    if (empty($course_code) || empty($course_title) || empty($dept_id) || empty($level_id) || empty($semester_id)) {
        $message = displayError("All fields are required");
    } else {
        // Check if course code already exists
        $stmt = $conn->prepare("SELECT course_id FROM courses WHERE course_code = ?");
        $stmt->bind_param("s", $course_code);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $message = displayError("Course code already exists");
        } else {
            // Generate QR code data
            $qr_data = json_encode([
                'course_code' => $course_code,
                'course_title' => $course_title,
                'dept_id' => $dept_id,
                'level_id' => $level_id,
                'semester_id' => $semester_id,
                'timestamp' => time()
            ]);

            // Generate QR code URL (with correct path)
            $qr_code = generateQRCode($qr_data);

            // Insert new course
            $stmt = $conn->prepare("INSERT INTO courses (course_code, course_title, dept_id, level_id, semester_id, qr_code) VALUES (?, ?, ?, ?, ?, ?)");
            $stmt->bind_param("ssiiis", $course_code, $course_title, $dept_id, $level_id, $semester_id, $qr_code);

            if ($stmt->execute()) {
                $message = displaySuccess("Course added successfully!");
            } else {
                $message = displayError("Error adding course: " . $conn->error);
            }
        }
    }
}

// Get all courses with department, level, and semester names
$courses = $conn->query("
    SELECT c.*, d.dept_name, l.level_name, s.semester_name
    FROM courses c
    JOIN departments d ON c.dept_id = d.dept_id
    JOIN levels l ON c.level_id = l.level_id
    JOIN semesters s ON c.semester_id = s.semester_id
    ORDER BY c.course_order ASC, c.course_code ASC
");
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Course - Ogbonnaya Onu Polytechnic</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="../images/logo.jpg" alt="Logo" width="30" height="30" class="d-inline-block align-text-top me-2 rounded-circle">
                Admin Dashboard
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Students
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="students.php">All Students</a></li>
                            <li><a class="dropdown-item" href="pending_students.php">Pending Approvals</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Lecturers
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="lecturers.php">All Lecturers</a></li>
                            <li><a class="dropdown-item" href="pending_lecturers.php">Pending Approvals</a></li>
                            <li><a class="dropdown-item" href="assign_courses.php">Assign Courses</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Courses
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="courses.php">All Courses</a></li>
                            <li><a class="dropdown-item active" href="add_course.php">Add New Course</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Departments
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="departments.php">All Departments</a></li>
                            <li><a class="dropdown-item" href="add_department.php">Add New Department</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">Reports</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-1"></i> <?php echo $_SESSION['username']; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profile</a></li>
                            <li><a class="dropdown-item" href="change_password.php">Change Password</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col-md-12">
                <h2>Add New Course</h2>
                <p class="text-muted">Create a new course with QR code for attendance</p>
            </div>
        </div>

        <?php echo $message; ?>

        <div class="row">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>Add Course</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="">
                            <div class="mb-3">
                                <label for="course_code" class="form-label">Course Code</label>
                                <input type="text" class="form-control" id="course_code" name="course_code" required>
                            </div>

                            <div class="mb-3">
                                <label for="course_title" class="form-label">Course Title</label>
                                <input type="text" class="form-control" id="course_title" name="course_title" required>
                            </div>

                            <div class="mb-3">
                                <label for="dept_id" class="form-label">Department</label>
                                <select class="form-select" id="dept_id" name="dept_id" required>
                                    <option value="">Select Department</option>
                                    <?php while ($dept = $departments->fetch_assoc()): ?>
                                        <option value="<?php echo $dept['dept_id']; ?>"><?php echo $dept['dept_name']; ?></option>
                                    <?php endwhile; ?>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="level_id" class="form-label">Level</label>
                                <select class="form-select" id="level_id" name="level_id" required>
                                    <option value="">Select Level</option>
                                    <?php while ($level = $levels->fetch_assoc()): ?>
                                        <option value="<?php echo $level['level_id']; ?>"><?php echo $level['level_name']; ?></option>
                                    <?php endwhile; ?>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="semester_id" class="form-label">Semester</label>
                                <select class="form-select" id="semester_id" name="semester_id" required>
                                    <option value="">Select Semester</option>
                                    <option value="1">First Semester</option>
                                    <option value="2">Second Semester</option>
                                </select>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">Add Course</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5>Existing Courses</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($courses->num_rows > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover data-table">
                                    <thead>
                                        <tr>
                                            <th>Code</th>
                                            <th>Title</th>
                                            <th>Department</th>
                                            <th>Level</th>
                                            <th>Semester</th>
                                            <th>QR Code</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($course = $courses->fetch_assoc()): ?>
                                            <tr>
                                                <td><?php echo $course['course_code']; ?></td>
                                                <td><?php echo $course['course_title']; ?></td>
                                                <td><?php echo $course['dept_name']; ?></td>
                                                <td><?php echo $course['level_name']; ?></td>
                                                <td><?php echo $course['semester_name']; ?></td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#qrModal<?php echo $course['course_id']; ?>">
                                                        <i class="fas fa-qrcode"></i> View QR
                                                    </button>

                                                    <!-- QR Code Modal -->
                                                    <div class="modal fade" id="qrModal<?php echo $course['course_id']; ?>" tabindex="-1" aria-labelledby="qrModalLabel<?php echo $course['course_id']; ?>" aria-hidden="true">
                                                        <div class="modal-dialog modal-dialog-centered">
                                                            <div class="modal-content">
                                                                <div class="modal-header">
                                                                    <h5 class="modal-title" id="qrModalLabel<?php echo $course['course_id']; ?>">QR Code for <?php echo $course['course_code']; ?></h5>
                                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                </div>
                                                                <div class="modal-body text-center">
                                                                    <h6><?php echo $course['course_title']; ?></h6>
                                                                    <div class="qr-code my-3">
                                                                        <img src="../qr_codes/<?php echo $course['qr_code']; ?>" alt="QR Code" class="img-fluid">
                                                                    </div>
                                                                    <p class="mb-0">Scan this QR code to mark attendance for this course.</p>
                                                                </div>
                                                                <div class="modal-footer">
                                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                                    <a href="../<?php echo $course['qr_code']; ?>" download="<?php echo $course['course_code']; ?>_QR.png" class="btn btn-primary">
                                                                        <i class="fas fa-download"></i> Download
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">No courses found. Add your first course.</div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-3 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; <?php echo date('Y'); ?> Ogbonnaya Onu Polytechnic. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="../about.php" class="text-white me-3">About</a>
                    <a href="../support.php" class="text-white">Support</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/script.js"></script>
    <!-- Session Manager -->
    <script src="../assets/js/session_manager.js"></script>
    <script>
        $(document).ready(function() {
            $('.data-table').DataTable({
                "order": [[0, "asc"]]
            });
        });
    </script>
</body>
</html>
