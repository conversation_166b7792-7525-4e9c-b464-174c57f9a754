<?php
require_once '../config/functions.php';
require_once 'auth_check.php';

// Set content type to JSON
header('Content-Type: application/json');

// Get filter parameters
$dept_id = isset($_GET['dept_id']) ? sanitize($_GET['dept_id']) : null;
$level_id = isset($_GET['level_id']) ? sanitize($_GET['level_id']) : null;

// Build query based on filters
$query = "SELECT course_id, course_code, course_title FROM courses WHERE 1=1";
$params = [];
$types = "";

if (!empty($dept_id)) {
    $query .= " AND dept_id = ?";
    $params[] = $dept_id;
    $types .= "i";
}

if (!empty($level_id)) {
    $query .= " AND level_id = ?";
    $params[] = $level_id;
    $types .= "i";
}

$query .= " ORDER BY course_code";

// Prepare and execute the query
$stmt = $conn->prepare($query);

if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}

$stmt->execute();
$result = $stmt->get_result();

// Fetch all courses
$courses = [];
while ($course = $result->fetch_assoc()) {
    $courses[] = [
        'course_id' => $course['course_id'],
        'course_code' => $course['course_code'],
        'course_title' => $course['course_title']
    ];
}

// Return JSON response
echo json_encode($courses);
?>
