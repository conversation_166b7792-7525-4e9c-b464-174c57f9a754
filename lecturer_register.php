<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database connection and functions
require_once 'config/functions.php';

// Check if user is already logged in
if (isLecturerLoggedIn()) {
    redirect('lecturer/index.php');
}

// Process registration form
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitize($_POST['name']);
    $staff_code = sanitize($_POST['staff_code']);
    $email = sanitize($_POST['email']);
    $phone = sanitize($_POST['phone']);
    $dept_id = sanitize($_POST['dept_id']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    $question1_id = sanitize($_POST['question1_id']);
    $answer1 = sanitize($_POST['answer1']);
    $question2_id = sanitize($_POST['question2_id']);
    $answer2 = sanitize($_POST['answer2']);

    // Validate input
    if (empty($name) || empty($staff_code) || empty($email) || empty($phone) || empty($dept_id) || empty($password) || empty($confirm_password) || empty($question1_id) || empty($answer1) || empty($question2_id) || empty($answer2)) {
        $error = 'Please fill in all fields including security questions';
    } elseif ($password !== $confirm_password) {
        $error = 'Passwords do not match';
    } elseif (!preg_match('/^[0-9]{11}$/', $phone)) {
        $error = 'Phone number must be exactly 11 digits';
    } elseif ($question1_id === $question2_id) {
        $error = 'Please select two different security questions';
    } else {
        // Check if staff code or email already exists
        $stmt = $conn->prepare("SELECT lecturer_id FROM lecturers WHERE staff_code = ? OR email = ?");
        $stmt->bind_param("ss", $staff_code, $email);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $error = 'Staff code or email already exists. If you have already registered, please wait for admin approval.';
        } else {
            try {
                // Hash password
                $hashed_password = hashPassword($password);

                // Insert new lecturer
                $stmt = $conn->prepare("INSERT INTO lecturers (name, staff_code, email, phone, dept_id, password, status) VALUES (?, ?, ?, ?, ?, ?, 'pending')");
                if (!$stmt) {
                    throw new Exception("Prepare failed: " . $conn->error);
                }

                $stmt->bind_param("ssssss", $name, $staff_code, $email, $phone, $dept_id, $hashed_password);

                if ($stmt->execute()) {
                    $lecturer_id = $conn->insert_id;

                    // Save security answers
                    if (saveLecturerSecurityAnswers($lecturer_id, $question1_id, $answer1, $question2_id, $answer2)) {
                        // Create notification for admin about new lecturer registration
                        $admin_message = "New lecturer registration: $name ($staff_code) has registered and requires approval.";
                        createNotification(1, 'admin', $admin_message, $lecturer_id, 'lecturer_registration');

                        $success = 'Registration successful! Your account is pending approval by the administrator.';
                    } else {
                        // If security answers failed, delete the lecturer record
                        $conn->query("DELETE FROM lecturers WHERE lecturer_id = $lecturer_id");
                        throw new Exception("Failed to save security answers");
                    }
                } else {
                    throw new Exception("Execute failed: " . $stmt->error);
                }
            } catch (Exception $e) {
                $error = 'Registration failed: ' . $e->getMessage();
            }
        }
    }
}
?>

<?php include 'includes/header.php'; ?>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3 class="text-center">Lecturer Registration</h3>
            </div>
            <div class="card-body">
                <?php if (!empty($success)): ?>
                    <div class="alert alert-success"><?php echo $success; ?></div>
                    <div class="text-center">
                        <a href="index.php" class="btn btn-primary">Back to Login</a>
                    </div>
                <?php else: ?>
                    <?php if (!empty($error)): ?>
                        <div class="alert alert-danger"><?php echo $error; ?></div>
                    <?php endif; ?>

                    <form method="POST" action="lecturer_register.php" id="registration-form">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Full Name</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="staff_code" class="form-label">Staff Code</label>
                                <input type="text" class="form-control" id="staff_code" name="staff_code" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>

                        <div class="mb-3">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control" id="phone" name="phone" pattern="[0-9]{11}" maxlength="11" placeholder="Enter 11-digit phone number" required>
                            <div class="form-text">Please enter exactly 11 digits (e.g., 08012345678)</div>
                            <div id="phone-feedback" class="invalid-feedback" style="display: none;">
                                Phone number must be exactly 11 digits
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="dept_id" class="form-label">Department</label>
                            <select class="form-select" id="dept_id" name="dept_id" required>
                                <option value="">Select Department</option>
                                <?php
                                $departments = $conn->query("SELECT * FROM departments ORDER BY dept_name");
                                while ($dept = $departments->fetch_assoc()) {
                                    echo "<option value='" . $dept['dept_id'] . "'>" . htmlspecialchars($dept['dept_name']) . "</option>";
                                }
                                ?>
                            </select>
                        </div>

                        <!-- Security Questions Section -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-shield-alt me-2"></i>Security Questions</h5>
                                <small class="text-muted">These will help you reset your password if needed</small>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="question1_id" class="form-label">Security Question 1</label>
                                        <select class="form-select" id="question1_id" name="question1_id" required>
                                            <option value="">Select a security question</option>
                                            <?php
                                            $questions = getSecurityQuestions();
                                            foreach ($questions as $question) {
                                                echo "<option value='" . $question['question_id'] . "'>" . htmlspecialchars($question['question_text']) . "</option>";
                                            }
                                            ?>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="answer1" class="form-label">Answer 1</label>
                                        <input type="text" class="form-control" id="answer1" name="answer1" placeholder="Enter your answer" required>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="question2_id" class="form-label">Security Question 2</label>
                                        <select class="form-select" id="question2_id" name="question2_id" required>
                                            <option value="">Select a different security question</option>
                                            <?php
                                            foreach ($questions as $question) {
                                                echo "<option value='" . $question['question_id'] . "'>" . htmlspecialchars($question['question_text']) . "</option>";
                                            }
                                            ?>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="answer2" class="form-label">Answer 2</label>
                                        <input type="text" class="form-control" id="answer2" name="answer2" placeholder="Enter your answer" required>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">Password</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="password" name="password" required>
                                    <button type="button" class="input-group-text" onclick="togglePasswordVisibility('password')">
                                        <i class="fas fa-eye" id="password-toggle-icon"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="confirm_password" class="form-label">Confirm Password</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                    <button type="button" class="input-group-text" onclick="togglePasswordVisibility('confirm_password')">
                                        <i class="fas fa-eye" id="confirm-password-toggle-icon"></i>
                                    </button>
                                </div>
                                <div class="invalid-feedback" id="password-error"></div>
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">Register</button>
                            <a href="index.php" class="btn btn-secondary">Back to Login</a>
                        </div>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>

<script>
function togglePasswordVisibility(fieldId) {
    const passwordField = document.getElementById(fieldId);
    const iconId = fieldId === 'password' ? 'password-toggle-icon' : 'confirm-password-toggle-icon';
    const toggleIcon = document.getElementById(iconId);

    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

// Phone number validation
document.getElementById('phone').addEventListener('input', function(e) {
    // Remove any non-digit characters
    let value = e.target.value.replace(/\D/g, '');

    // Limit to 11 digits
    if (value.length > 11) {
        value = value.slice(0, 11);
    }

    e.target.value = value;

    // Show validation feedback
    const feedback = document.getElementById('phone-feedback');
    if (value.length === 11) {
        e.target.classList.remove('is-invalid');
        e.target.classList.add('is-valid');
        if (feedback) feedback.style.display = 'none';
    } else {
        e.target.classList.remove('is-valid');
        e.target.classList.add('is-invalid');
        if (feedback) feedback.style.display = 'block';
    }
});

// Security questions validation
document.getElementById('question1_id').addEventListener('change', function() {
    validateSecurityQuestions();
});

document.getElementById('question2_id').addEventListener('change', function() {
    validateSecurityQuestions();
});

function validateSecurityQuestions() {
    const question1 = document.getElementById('question1_id').value;
    const question2 = document.getElementById('question2_id').value;
    const feedback = document.getElementById('security-feedback');

    if (question1 && question2 && question1 === question2) {
        if (!feedback) {
            const feedbackDiv = document.createElement('div');
            feedbackDiv.id = 'security-feedback';
            feedbackDiv.className = 'alert alert-warning mt-2';
            feedbackDiv.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>Please select two different security questions.';
            document.querySelector('.card-body').appendChild(feedbackDiv);
        }
        document.getElementById('question2_id').classList.add('is-invalid');
    } else {
        if (feedback) {
            feedback.remove();
        }
        document.getElementById('question2_id').classList.remove('is-invalid');
    }
}

// Form submission validation
document.getElementById('registration-form').addEventListener('submit', function(e) {
    const phone = document.getElementById('phone').value;
    const question1 = document.getElementById('question1_id').value;
    const question2 = document.getElementById('question2_id').value;

    if (phone.length !== 11) {
        e.preventDefault();
        alert('Please enter exactly 11 digits for phone number');
        return false;
    }

    if (question1 === question2) {
        e.preventDefault();
        alert('Please select two different security questions');
        return false;
    }
});
</script>
