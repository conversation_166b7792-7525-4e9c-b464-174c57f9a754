# Automatic QR Code Regeneration Setup

This system includes automatic QR code regeneration functionality that works in the background to keep QR codes fresh and secure.

## Features

### 1. **Background JavaScript Regeneration**
- Automatically runs every hour when admin pages are open
- Runs when browser tab becomes active after being inactive
- Shows subtle notifications to admin users
- Stores last regeneration time in browser localStorage

### 2. **Manual Regeneration Button**
- Available on admin dashboard
- Provides immediate feedback with loading states
- Shows success/error status

### 3. **Server-Side Background Script**
- Can be called via AJAX or directly
- Logs all regeneration activities
- Handles errors gracefully
- Updates database with regeneration timestamps

### 4. **Cron Job Support**
- Dedicated cron script for server-level automation
- Can run independently of user activity
- Suitable for production environments

## Setup Instructions

### Option 1: Browser-Based Auto Regeneration (Already Active)
✅ **Already configured and working!**
- Runs automatically when admin users are active
- No additional setup required
- QR codes regenerate every hour during admin activity

### Option 2: Server Cron Job (Recommended for Production)

1. **Make the cron script executable:**
   ```bash
   chmod +x /path/to/attendance/cron_qr_regenerator.php
   ```

2. **Add to crontab (run every 2 hours):**
   ```bash
   crontab -e
   ```
   Add this line:
   ```
   0 */2 * * * /usr/bin/php /path/to/your/attendance/cron_qr_regenerator.php
   ```

3. **Alternative: Run every hour:**
   ```
   0 * * * * /usr/bin/php /path/to/your/attendance/cron_qr_regenerator.php
   ```

### Option 3: Manual Web-Based Trigger
You can manually trigger regeneration by visiting:
```
http://yoursite.com/attendance/background_qr_regenerator.php?secret=qr_regen_2025
```

## How It Works

### Automatic Detection
The system automatically detects when QR codes need regeneration based on:
- Time since last regeneration (configurable interval)
- User activity on admin pages
- Manual triggers

### QR Code Generation Process
1. **Fetch all courses** from the database
2. **Generate new QR codes** for each course
3. **Update database** with new QR code paths
4. **Log activities** for monitoring
5. **Clean up old QR files** (optional)

### Security Features
- **Secret key protection** for direct web access
- **Admin-only manual triggers**
- **Secure file paths** for QR code storage
- **Activity logging** for audit trails

## Monitoring

### Check Regeneration Status
In browser console (on admin pages):
```javascript
// Get current status
console.log(getQRRegenerationStatus());

// Manually trigger regeneration
triggerQRRegeneration();
```

### Log Files
Check the regeneration logs at:
```
/logs/qr_regeneration.log
```

### Database Monitoring
Check last regeneration time:
```sql
SELECT * FROM system_settings WHERE setting_key = 'last_qr_regeneration';
```

## Configuration

### Adjust Regeneration Interval
Edit `assets/js/auto_qr_regenerator.js`:
```javascript
this.regenerationInterval = 60 * 60 * 1000; // 1 hour (change as needed)
```

### Disable Auto Regeneration
To disable automatic regeneration, remove or comment out:
```html
<!-- Auto QR Regenerator Script -->
<script src="../assets/js/auto_qr_regenerator.js"></script>
```

## Troubleshooting

### Common Issues

1. **QR codes not regenerating:**
   - Check browser console for JavaScript errors
   - Verify file permissions on QR codes directory
   - Check server logs for PHP errors

2. **Cron job not working:**
   - Verify PHP path: `which php`
   - Check cron logs: `tail -f /var/log/cron`
   - Test script manually: `php cron_qr_regenerator.php`

3. **Permission errors:**
   ```bash
   chmod 755 admin/qr_codes/
   chown www-data:www-data admin/qr_codes/
   ```

### Manual Testing
Test the regeneration system:
```bash
# Test background script
php background_qr_regenerator.php

# Test cron script
php cron_qr_regenerator.php
```

## Benefits

✅ **Always Fresh QR Codes** - Regular regeneration keeps codes secure
✅ **No Admin Intervention** - Runs automatically in background  
✅ **Multiple Fallbacks** - Browser, cron, and manual options
✅ **Activity Logging** - Full audit trail of regenerations
✅ **Error Handling** - Graceful failure recovery
✅ **Performance Optimized** - Minimal server impact

The system is now fully configured and will automatically maintain fresh QR codes for all courses!
