<?php
require_once '../config/functions.php';

// Check if lecturer is logged in
if (!isLecturerLoggedIn()) {
    redirect('../index.php');
}

// Get lecturer details
$lecturer_id = $_SESSION['lecturer_id'];
$lecturer = getLecturerDetails($lecturer_id);

// Check if course ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    redirect('index.php');
}

$course_id = sanitize($_GET['id']);

// Check if this course is assigned to the lecturer
$stmt = $conn->prepare("
    SELECT c.*, d.dept_name, l.level_name, s.semester_name
    FROM lecturer_courses lc
    JOIN courses c ON lc.course_id = c.course_id
    JOIN departments d ON c.dept_id = d.dept_id
    JOIN levels l ON c.level_id = l.level_id
    JOIN semesters s ON c.semester_id = s.semester_id
    WHERE lc.lecturer_id = ? AND c.course_id = ?
");
$stmt->bind_param("ii", $lecturer_id, $course_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    redirect('index.php');
}

$course = $result->fetch_assoc();

// Get students in this course's department, level, and semester
$students = $conn->query("
    SELECT * FROM students
    WHERE dept_id = {$course['dept_id']}
    AND level_id = {$course['level_id']}
    AND semester_id = {$course['semester_id']}
    AND status = 'approved'
    ORDER BY name
");

// Get attendance records for this course
$attendance = $conn->query("
    SELECT a.*, s.name, s.matric_number
    FROM attendance a
    JOIN students s ON a.student_id = s.student_id
    WHERE a.course_id = $course_id
    ORDER BY a.attendance_date DESC, a.attendance_time DESC
");

// Count total students and attendance
$totalStudents = $students->num_rows;
$totalAttendance = $attendance->num_rows;
$uniqueAttendees = $conn->query("SELECT COUNT(DISTINCT student_id) as count FROM attendance WHERE course_id = $course_id")->fetch_assoc()['count'];
$attendanceRate = ($totalStudents > 0) ? round(($uniqueAttendees / $totalStudents) * 100) : 0;

// Always generate QR code to ensure it's available
$qr_data = json_encode([
    'course_code' => $course['course_code'],
    'course_title' => $course['course_title'],
    'course_id' => $course['course_id'],
    'timestamp' => time()
]);

// Only generate a new QR code if one doesn't exist
if (empty($course['qr_code']) || !file_exists('../' . $course['qr_code'])) {
    $qr_code = generateQRCode($qr_data);

    $stmt = $conn->prepare("UPDATE courses SET qr_code = ? WHERE course_id = ?");
    $stmt->bind_param("si", $qr_code, $course_id);
    $stmt->execute();

    $course['qr_code'] = $qr_code;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $course['course_code']; ?> - Ogbonnaya Onu Polytechnic</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        /* Main Styles for Ogbonnaya Onu Polytechnic Attendance System */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }

        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .card-header {
            background-color: #343a40;
            color: white;
            border-radius: 10px 10px 0 0 !important;
        }

        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
        }

        .btn-primary:hover {
            background-color: #0069d9;
            border-color: #0062cc;
        }

        .btn-success {
            background-color: #28a745;
            border-color: #28a745;
        }

        .btn-success:hover {
            background-color: #218838;
            border-color: #1e7e34;
        }

        .qr-container {
            text-align: center;
            margin: 20px 0;
        }

        .qr-code {
            padding: 15px;
            background: white;
            display: inline-block;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="../images/logo.jpg" alt="Logo" width="30" height="30" class="d-inline-block align-text-top me-2">
                Lecturer Dashboard
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="courses.php">My Courses</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="attendance.php">Attendance</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">Reports</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">Students</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-1"></i> <?php echo $lecturer['name']; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profile</a></li>
                            <li><a class="dropdown-item" href="change_password.php">Change Password</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col-md-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="courses.php">My Courses</a></li>
                        <li class="breadcrumb-item active" aria-current="page"><?php echo $course['course_code']; ?></li>
                    </ol>
                </nav>
                <h2><?php echo $course['course_code']; ?> - <?php echo $course['course_title']; ?></h2>
                <p class="text-muted">Course Details and Attendance Management</p>
            </div>
        </div>

        <div class="row">
            <!-- Course Info -->
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h5>Course Information</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Course Code:</strong> <?php echo $course['course_code']; ?></p>
                        <p><strong>Course Title:</strong> <?php echo $course['course_title']; ?></p>
                        <p><strong>Department:</strong> <?php echo $course['dept_name']; ?></p>
                        <p><strong>Level:</strong> <?php echo $course['level_name']; ?></p>
                        <p><strong>Semester:</strong> <?php echo $course['semester_name']; ?></p>
                        <p><strong>Total Students:</strong> <?php echo $totalStudents; ?></p>
                        <p><strong>Attendance Rate:</strong> <?php echo $attendanceRate; ?>% (<?php echo $uniqueAttendees; ?> of <?php echo $totalStudents; ?> students)</p>

                        <div class="progress mb-3">
                            <div class="progress-bar bg-<?php echo ($attendanceRate >= 75) ? 'success' : (($attendanceRate >= 50) ? 'warning' : 'danger'); ?>"
                                 role="progressbar"
                                 style="width: <?php echo $attendanceRate; ?>%"
                                 aria-valuenow="<?php echo $attendanceRate; ?>"
                                 aria-valuemin="0"
                                 aria-valuemax="100"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- QR Code -->
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h5>Attendance QR Code</h5>
                    </div>
                    <div class="card-body text-center">
                        <p>Students can scan this QR code to mark their attendance for this course.</p>
                        <div class="qr-code my-3">
                            <?php if (!empty($course['qr_code']) && file_exists('../' . $course['qr_code'])): ?>
                                <img src="../<?php echo $course['qr_code']; ?>" alt="QR Code" class="img-fluid">
                            <?php else: ?>
                                <div id="qr-loading" class="text-center">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p class="mt-2">Generating QR code...</p>
                                </div>
                                <script>
                                    // Auto-generate QR code on page load if not available
                                    document.addEventListener('DOMContentLoaded', function() {
                                        setTimeout(function() {
                                            document.getElementById('generate-qr').click();
                                        }, 500);
                                    });
                                </script>
                            <?php endif; ?>
                        </div>
                        <div class="d-flex justify-content-center gap-2">
                            <?php if (!empty($course['qr_code']) && file_exists('../' . $course['qr_code'])): ?>
                                <a href="../<?php echo $course['qr_code']; ?>" download="<?php echo $course['course_code']; ?>_QR.png" class="btn btn-primary">
                                    <i class="fas fa-download"></i> Download QR Code
                                </a>
                                <button id="regenerate-qr" class="btn btn-warning" data-course-id="<?php echo $course['course_id']; ?>">
                                    <i class="fas fa-sync-alt"></i> Regenerate
                                </button>
                            <?php else: ?>
                                <button id="generate-qr" class="btn btn-warning" data-course-id="<?php echo $course['course_id']; ?>">
                                    <i class="fas fa-sync-alt"></i> Generate QR Code
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h5>Attendance Statistics</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 border rounded bg-light">
                                    <h3><?php echo $totalStudents; ?></h3>
                                    <p class="mb-0">Total Students</p>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 border rounded bg-light">
                                    <h3><?php echo $uniqueAttendees; ?></h3>
                                    <p class="mb-0">Attendees</p>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 border rounded bg-light">
                                    <h3><?php echo $totalAttendance; ?></h3>
                                    <p class="mb-0">Total Records</p>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 border rounded bg-light">
                                    <h3><?php echo $attendanceRate; ?>%</h3>
                                    <p class="mb-0">Attendance Rate</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Students List -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Students</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($students->num_rows > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover data-table">
                                    <thead>
                                        <tr>
                                            <th>Matric Number</th>
                                            <th>Name</th>
                                            <th>Attendance Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($student = $students->fetch_assoc()):
                                            // Check if student has attended this course
                                            $query = "SELECT * FROM attendance WHERE student_id = {$student['student_id']} AND course_id = $course_id";
                                            $attended = $conn->query($query)->num_rows > 0;
                                        ?>
                                            <tr>
                                                <td><?php echo $student['matric_number']; ?></td>
                                                <td><?php echo $student['name']; ?></td>
                                                <td>
                                                    <?php if ($attended): ?>
                                                        <span class="badge bg-success">Present</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">Absent</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">No students found for this course.</div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Attendance Records -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Attendance Records</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($attendance->num_rows > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover data-table">
                                    <thead>
                                        <tr>
                                            <th>Matric Number</th>
                                            <th>Name</th>
                                            <th>Date</th>
                                            <th>Time</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($record = $attendance->fetch_assoc()): ?>
                                            <tr>
                                                <td><?php echo $record['matric_number']; ?></td>
                                                <td><?php echo $record['name']; ?></td>
                                                <td><?php echo date('d M Y', strtotime($record['attendance_date'])); ?></td>
                                                <td><?php echo date('h:i A', strtotime($record['attendance_time'])); ?></td>
                                            </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">No attendance records found for this course.</div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-3 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; <?php echo date('Y'); ?> Ogbonnaya Onu Polytechnic. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p>QR Code Attendance Management System</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/script.js"></script>
    <script>
        $(document).ready(function() {
            $('.data-table').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
            });

            // Function to generate/regenerate QR code
            function generateQRCode(button, courseId, isRegenerate = false) {
                const originalText = button.html();
                button.html('<i class="fas fa-spinner fa-spin"></i> ' + (isRegenerate ? 'Regenerating...' : 'Generating...'));
                button.prop('disabled', true);

                // Call the API to generate QR code
                $.ajax({
                    url: '../generate_qr.php',
                    type: 'GET',
                    data: {
                        id: courseId,
                        regenerate: isRegenerate ? 1 : 0
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            // Reload the page to show the new QR code
                            location.reload();
                        } else {
                            alert('Error ' + (isRegenerate ? 'regenerating' : 'generating') + ' QR code: ' + (response.error || 'Unknown error'));
                            button.html(originalText);
                            button.prop('disabled', false);
                        }
                    },
                    error: function() {
                        alert('Error connecting to server. Please try again.');
                        button.html(originalText);
                        button.prop('disabled', false);
                    }
                });
            }

            // Handle QR code generation
            $('#generate-qr').on('click', function() {
                const courseId = $(this).data('course-id');
                generateQRCode($(this), courseId, false);
            });

            // Handle QR code regeneration
            $('#regenerate-qr').on('click', function() {
                const courseId = $(this).data('course-id');
                generateQRCode($(this), courseId, true);
            });
        });
    </script>
</body>
</html>
