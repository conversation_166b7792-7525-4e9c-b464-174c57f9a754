<?php
require_once '../config/functions.php';

// Check if student is logged in
if (!isStudentLoggedIn()) {
    redirect('../index.php');
}

// Get student details
$student_id = $_SESSION['student_id'];
$student = getStudentDetails($student_id);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scan QR Code - Ogbonnaya Onu Polytechnic</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <!-- QR Scanner JS -->
    <script src="https://unpkg.com/html5-qrcode"></script>
    <style>
        #qr-reader {
            width: 100%;
            max-width: 500px;
            margin: 0 auto;
        }
        #qr-reader__scan_region {
            position: relative;
        }
        #qr-reader__scan_region::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border: 2px solid #007bff;
            z-index: 1;
            pointer-events: none;
        }
        #result-container {
            display: none;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="../images/logo.jpg" alt="Logo" width="30" height="30" class="d-inline-block align-text-top me-2 rounded-circle">
                Student Dashboard
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="scan_qr.php">Scan QR Code</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="attendance_history.php">Attendance History</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-1"></i> <?php echo $_SESSION['username']; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col-md-12">
                <h2>Scan QR Code</h2>
                <p class="text-muted">Scan the QR code displayed by your lecturer to mark your attendance</p>
            </div>
        </div>

        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header bg-dark text-white">
                        <h5 class="mb-0">QR Code Scanner</h5>
                    </div>
                    <div class="card-body">
                        <div id="scanner-container">
                            <div id="qr-reader"></div>
                            <div class="mt-3 text-center">
                                <button id="start-button" class="btn btn-primary">
                                    <i class="fas fa-camera me-2"></i> Start Scanner
                                </button>
                                <button id="stop-button" class="btn btn-danger d-none">
                                    <i class="fas fa-stop-circle me-2"></i> Stop Scanner
                                </button>
                            </div>
                        </div>

                        <div id="result-container" class="mt-4">
                            <div class="alert alert-success">
                                <h5 class="alert-heading">Attendance Marked!</h5>
                                <p id="result-message"></p>
                                <hr>
                                <p class="mb-0">Course: <span id="result-course"></span></p>
                                <p class="mb-0">Date: <span id="result-date"></span></p>
                                <p class="mb-0">Time: <span id="result-time"></span></p>
                            </div>
                            <div class="text-center">
                                <button id="scan-again" class="btn btn-primary">
                                    <i class="fas fa-redo me-2"></i> Scan Another Code
                                </button>
                            </div>
                        </div>

                        <div id="error-container" class="mt-4 d-none">
                            <div class="alert alert-danger">
                                <h5 class="alert-heading">Error!</h5>
                                <p id="error-message"></p>
                            </div>
                            <div class="text-center">
                                <button id="try-again" class="btn btn-primary">
                                    <i class="fas fa-redo me-2"></i> Try Again
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-3 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; <?php echo date('Y'); ?> Ogbonnaya Onu Polytechnic. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p>QR Code Attendance Management System</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        $(document).ready(function() {
            let html5QrCode;
            
            // Start scanner button
            $('#start-button').click(function() {
                $(this).addClass('d-none');
                $('#stop-button').removeClass('d-none');
                startScanner();
            });
            
            // Stop scanner button
            $('#stop-button').click(function() {
                $(this).addClass('d-none');
                $('#start-button').removeClass('d-none');
                stopScanner();
            });
            
            // Scan again button
            $('#scan-again').click(function() {
                $('#result-container').hide();
                $('#scanner-container').show();
                $('#start-button').click();
            });
            
            // Try again button
            $('#try-again').click(function() {
                $('#error-container').addClass('d-none');
                $('#scanner-container').show();
                $('#start-button').click();
            });
            
            function startScanner() {
                html5QrCode = new Html5Qrcode("qr-reader");
                const qrCodeSuccessCallback = (decodedText, decodedResult) => {
                    // Stop scanner after successful scan
                    stopScanner();
                    
                    // Process the QR code data
                    processQrCode(decodedText);
                };
                
                const config = { fps: 10, qrbox: { width: 250, height: 250 } };
                
                // Start scanner with camera
                html5QrCode.start(
                    { facingMode: "environment" }, 
                    config, 
                    qrCodeSuccessCallback
                ).catch((err) => {
                    // Handle camera permission errors
                    $('#error-message').text("Camera permission denied or error: " + err);
                    $('#error-container').removeClass('d-none');
                    $('#scanner-container').hide();
                });
            }
            
            function stopScanner() {
                if (html5QrCode) {
                    html5QrCode.stop().then(() => {
                        console.log('QR Code scanner stopped');
                    }).catch((err) => {
                        console.error('Error stopping scanner:', err);
                    });
                }
            }
            
            function processQrCode(qrData) {
                // Send QR code data to server
                $.ajax({
                    url: '../mark_attendance.php',
                    type: 'POST',
                    data: { qr_data: qrData },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            // Show success message
                            $('#result-message').text(response.message);
                            $('#result-course').text(response.course);
                            $('#result-date').text(response.date);
                            $('#result-time').text(response.time);
                            $('#result-container').show();
                            $('#scanner-container').hide();
                        } else {
                            // Show error message
                            $('#error-message').text(response.message);
                            $('#error-container').removeClass('d-none');
                            $('#scanner-container').hide();
                        }
                    },
                    error: function(xhr, status, error) {
                        // Show error message
                        $('#error-message').text("Error processing QR code: " + error);
                        $('#error-container').removeClass('d-none');
                        $('#scanner-container').hide();
                    }
                });
            }
        });
    </script>
</body>
</html>
