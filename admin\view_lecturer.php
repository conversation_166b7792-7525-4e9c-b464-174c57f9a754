<?php
require_once '../config/database.php';
require_once '../config/functions.php';
require_once 'auth_check.php';

// Check if lecturer ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    redirect('lecturers.php');
}

$lecturer_id = sanitize($_GET['id']);

// Get lecturer details
$stmt = $conn->prepare("
    SELECT l.*
    FROM lecturers l
    WHERE l.lecturer_id = ?
");
$stmt->bind_param("i", $lecturer_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    redirect('lecturers.php');
}

$lecturer = $result->fetch_assoc();

// Get assigned courses for this lecturer
$assigned_courses = $conn->query("
    SELECT c.*, l.level_name, s.semester_name
    FROM lecturer_courses lc
    JOIN courses c ON lc.course_id = c.course_id
    JOIN levels l ON c.level_id = l.level_id
    JOIN semesters s ON c.semester_id = s.semester_id
    WHERE lc.lecturer_id = $lecturer_id
    ORDER BY c.course_code
");

// Count total assigned courses
$totalCourses = $assigned_courses->num_rows;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lecturer Details - Ogbonnaya Onu Polytechnic</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="../images/logo.jpg" alt="Logo" width="30" height="30" class="d-inline-block align-text-top me-2 rounded-circle">
                Admin Dashboard
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Students
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="students.php">All Students</a></li>
                            <li><a class="dropdown-item" href="pending_students.php">Pending Approvals</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Lecturers
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item active" href="lecturers.php">All Lecturers</a></li>
                            <li><a class="dropdown-item" href="pending_lecturers.php">Pending Approvals</a></li>
                            <li><a class="dropdown-item" href="assign_courses.php">Assign Courses</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Courses
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="courses.php">All Courses</a></li>
                            <li><a class="dropdown-item" href="add_course.php">Add New Course</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Departments
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="departments.php">All Departments</a></li>
                            <li><a class="dropdown-item" href="add_department.php">Add New Department</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">Reports</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-1"></i> <?php echo $_SESSION['username']; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profile</a></li>
                            <li><a class="dropdown-item" href="change_password.php">Change Password</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col-md-8">
                <h2>Lecturer Details</h2>
                <p class="text-muted">Viewing details for <?php echo $lecturer['name']; ?></p>
            </div>
            <div class="col-md-4 text-end">
                <a href="lecturers.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Back to Lecturers
                </a>
            </div>
        </div>

        <div class="row">
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Lecturer Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-3">
                            <i class="fas fa-chalkboard-teacher fa-5x text-primary"></i>
                        </div>
                        <table class="table table-borderless">
                            <tr>
                                <th>Name:</th>
                                <td><?php echo $lecturer['name']; ?></td>
                            </tr>
                            <tr>
                                <th>Staff ID:</th>
                                <td><?php echo $lecturer['staff_code']; ?></td>
                            </tr>
                            <tr>
                                <th>Email:</th>
                                <td><?php echo $lecturer['email']; ?></td>
                            </tr>
                            <tr>
                                <th>Phone:</th>
                                <td><?php echo $lecturer['phone']; ?></td>
                            </tr>
                            <!-- Department information not available -->

                            <tr>
                                <th>Status:</th>
                                <td>
                                    <?php if ($lecturer['status'] == 'approved'): ?>
                                        <span class="badge bg-success">Approved</span>
                                    <?php elseif ($lecturer['status'] == 'pending'): ?>
                                        <span class="badge bg-warning">Pending</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Rejected</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <th>Registered On:</th>
                                <td><?php echo date('d M Y, h:i A', strtotime($lecturer['created_at'])); ?></td>
                            </tr>
                        </table>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5>Course Summary</h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-3">
                            <h1 class="display-4"><?php echo $totalCourses; ?></h1>
                            <p class="text-muted">Total Assigned Courses</p>
                        </div>
                        <div class="d-grid gap-2">
                            <a href="assign_courses.php?lecturer_id=<?php echo $lecturer_id; ?>" class="btn btn-primary">
                                <i class="fas fa-plus-circle me-1"></i> Assign New Course
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5>Assigned Courses</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($assigned_courses->num_rows > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover data-table">
                                    <thead>
                                        <tr>
                                            <th>Course Code</th>
                                            <th>Course Title</th>
                                            <!-- Department column removed -->
                                            <th>Level</th>
                                            <th>Semester</th>
                                            <th>QR Code</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($course = $assigned_courses->fetch_assoc()): ?>
                                            <tr>
                                                <td><?php echo $course['course_code']; ?></td>
                                                <td><?php echo $course['course_title']; ?></td>
                                                <!-- Department column removed -->
                                                <td><?php echo $course['level_name']; ?></td>
                                                <td><?php echo $course['semester_name']; ?></td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#qrModal<?php echo $course['course_id']; ?>">
                                                        <i class="fas fa-qrcode"></i> View QR
                                                    </button>

                                                    <!-- QR Code Modal -->
                                                    <div class="modal fade" id="qrModal<?php echo $course['course_id']; ?>" tabindex="-1" aria-labelledby="qrModalLabel<?php echo $course['course_id']; ?>" aria-hidden="true">
                                                        <div class="modal-dialog modal-dialog-centered">
                                                            <div class="modal-content">
                                                                <div class="modal-header">
                                                                    <h5 class="modal-title" id="qrModalLabel<?php echo $course['course_id']; ?>">QR Code for <?php echo $course['course_code']; ?></h5>
                                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                </div>
                                                                <div class="modal-body text-center">
                                                                    <h6><?php echo $course['course_title']; ?></h6>
                                                                    <div class="qr-code my-3">
                                                                        <img src="<?php echo $course['qr_code']; ?>" alt="QR Code" class="img-fluid">
                                                                    </div>
                                                                    <p class="mb-0">Scan this QR code to mark attendance for this course.</p>
                                                                </div>
                                                                <div class="modal-footer">
                                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                                    <a href="<?php echo $course['qr_code']; ?>" download="<?php echo $course['course_code']; ?>_QR.png" class="btn btn-primary">
                                                                        <i class="fas fa-download"></i> Download
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">No courses assigned to this lecturer yet.</div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-3 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; <?php echo date('Y'); ?> Ogbonnaya Onu Polytechnic. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p>QR Code Attendance Management System</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/script.js"></script>
    <!-- Session Manager -->
    <script src="../assets/js/session_manager.js"></script>
    <script>
        $(document).ready(function() {
            $('.data-table').DataTable({
                "order": [[0, "asc"]]
            });
        });
    </script>
</body>
</html>
