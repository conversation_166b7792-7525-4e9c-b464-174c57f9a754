<?php
/**
 * Background QR Code Generation Script
 * This script can be run manually or via cron job to ensure all courses have QR codes
 */

// Include database configuration
if (file_exists('config/database.php')) {
    require_once 'config/database.php';
} else {
    die("Database configuration not found. Please run setup first.\n");
}

// QR Code generation function (same as in setup.php)
function generateQRCode($data) {
    // Create QR codes directory if it doesn't exist
    $qr_dir = __DIR__ . '/qr_codes';
    if (!is_dir($qr_dir)) {
        @mkdir($qr_dir, 0755, true);
    }
    
    // Generate unique filename based on data hash
    $filename = 'qr_' . md5($data) . '.png';
    $filepath = $qr_dir . '/' . $filename;
    
    // If file already exists, return the existing path
    if (file_exists($filepath)) {
        return 'qr_codes/' . $filename;
    }
    
    // Generate QR code using Google Charts API
    $encoded_data = urlencode($data);
    $qr_url = "https://chart.googleapis.com/chart?chs=300x300&cht=qr&chl=" . $encoded_data;
    
    // Download and save the QR code image with timeout and error handling
    $context = stream_context_create([
        'http' => [
            'timeout' => 10, // 10 second timeout
            'user_agent' => 'Attendance System QR Generator'
        ]
    ]);
    
    $qr_image = @file_get_contents($qr_url, false, $context);
    if ($qr_image !== false && strlen($qr_image) > 100) { // Basic validation
        if (@file_put_contents($filepath, $qr_image)) {
            // Verify the saved file
            if (file_exists($filepath) && filesize($filepath) > 100) {
                return 'qr_codes/' . $filename;
            }
        }
    }
    
    // Fallback: return the URL if file saving fails
    return $qr_url;
}

// Function to generate missing QR codes
function generateMissingQRCodes($conn) {
    $result = $conn->query("SELECT course_id, course_code, course_title FROM courses WHERE qr_code IS NULL OR qr_code = ''");
    $updated_count = 0;
    
    if ($result && $result->num_rows > 0) {
        while ($course = $result->fetch_assoc()) {
            $qr_data = "course_code=" . $course['course_code'] . "&course_title=" . $course['course_title'];
            $qr_code = generateQRCode($qr_data);
            
            $stmt = $conn->prepare("UPDATE courses SET qr_code = ? WHERE course_id = ?");
            $stmt->bind_param("si", $qr_code, $course['course_id']);
            if ($stmt->execute()) {
                $updated_count++;
                echo "Generated QR code for: " . $course['course_code'] . " - " . $course['course_title'] . "\n";
            }
        }
    }
    
    return $updated_count;
}

// Function to regenerate all QR codes
function regenerateAllQRCodes($conn) {
    $result = $conn->query("SELECT course_id, course_code, course_title FROM courses");
    $updated_count = 0;
    
    if ($result && $result->num_rows > 0) {
        while ($course = $result->fetch_assoc()) {
            $qr_data = "course_code=" . $course['course_code'] . "&course_title=" . $course['course_title'];
            $qr_code = generateQRCode($qr_data);
            
            $stmt = $conn->prepare("UPDATE courses SET qr_code = ? WHERE course_id = ?");
            $stmt->bind_param("si", $qr_code, $course['course_id']);
            if ($stmt->execute()) {
                $updated_count++;
                echo "Regenerated QR code for: " . $course['course_code'] . " - " . $course['course_title'] . "\n";
            }
        }
    }
    
    return $updated_count;
}

// Main execution
try {
    $conn = new mysqli($db_config['host'], $db_config['user'], $db_config['pass'], $db_config['name']);
    
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error . "\n");
    }
    
    echo "=== QR Code Generation Script ===\n";
    echo "Starting QR code generation process...\n\n";
    
    // Check command line arguments
    $regenerate_all = isset($argv[1]) && $argv[1] === '--regenerate-all';
    
    if ($regenerate_all) {
        echo "Regenerating ALL QR codes...\n";
        $count = regenerateAllQRCodes($conn);
        echo "\nRegenerated QR codes for $count courses.\n";
    } else {
        echo "Generating missing QR codes...\n";
        $count = generateMissingQRCodes($conn);
        
        if ($count > 0) {
            echo "\nGenerated QR codes for $count courses.\n";
        } else {
            echo "All courses already have QR codes.\n";
        }
    }
    
    echo "\nQR code generation completed successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
