<?php
/**
 * Background QR Code Generation Script
 * This script automatically generates QR codes for courses without them
 * Can be run manually, via cron job, or included in other scripts
 */

// Include database configuration
if (file_exists('config/database.php')) {
    require_once 'config/database.php';
} else {
    die("Database configuration not found. Please run setup first.\n");
}

// QR Code generation function - Returns URL for dynamic generation
function generateQRCode($data) {
    // Generate QR code using Google Charts API - returns URL for dynamic generation
    $encoded_data = urlencode($data);
    $qr_url = "https://chart.googleapis.com/chart?chs=300x300&cht=qr&chl=" . $encoded_data;
    return $qr_url;
}

// Background task function to auto-generate missing QR codes
function autoGenerateQRCodes($conn, $verbose = true) {
    $result = $conn->query("SELECT course_id, course_code, course_title FROM courses WHERE qr_code IS NULL OR qr_code = ''");
    $updated_count = 0;

    if ($result && $result->num_rows > 0) {
        while ($course = $result->fetch_assoc()) {
            $qr_data = "course_code=" . $course['course_code'] . "&course_title=" . $course['course_title'];
            $qr_code = generateQRCode($qr_data);

            $stmt = $conn->prepare("UPDATE courses SET qr_code = ? WHERE course_id = ?");
            $stmt->bind_param("si", $qr_code, $course['course_id']);
            if ($stmt->execute()) {
                $updated_count++;
                if ($verbose) {
                    echo "Auto-generated QR code for: " . $course['course_code'] . " - " . $course['course_title'] . "\n";
                }
            }
        }
    }

    return $updated_count;
}

// Function to update all QR codes (for maintenance)
function updateAllQRCodes($conn, $verbose = true) {
    $result = $conn->query("SELECT course_id, course_code, course_title FROM courses");
    $updated_count = 0;

    if ($result && $result->num_rows > 0) {
        while ($course = $result->fetch_assoc()) {
            $qr_data = "course_code=" . $course['course_code'] . "&course_title=" . $course['course_title'];
            $qr_code = generateQRCode($qr_data);

            $stmt = $conn->prepare("UPDATE courses SET qr_code = ? WHERE course_id = ?");
            $stmt->bind_param("si", $qr_code, $course['course_id']);
            if ($stmt->execute()) {
                $updated_count++;
                if ($verbose) {
                    echo "Updated QR code for: " . $course['course_code'] . " - " . $course['course_title'] . "\n";
                }
            }
        }
    }

    return $updated_count;
}

// Main execution
try {
    $conn = new mysqli($db_config['host'], $db_config['user'], $db_config['pass'], $db_config['name']);
    
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error . "\n");
    }
    
    echo "=== QR Code Generation Script ===\n";
    echo "Starting QR code generation process...\n\n";
    
    // Check command line arguments
    $update_all = isset($argv[1]) && $argv[1] === '--update-all';

    if ($update_all) {
        echo "Updating ALL QR codes...\n";
        $count = updateAllQRCodes($conn);
        echo "\nUpdated QR codes for $count courses.\n";
    } else {
        echo "Auto-generating missing QR codes...\n";
        $count = autoGenerateQRCodes($conn);

        if ($count > 0) {
            echo "\nAuto-generated QR codes for $count courses.\n";
        } else {
            echo "All courses already have QR codes.\n";
        }
    }
    
    echo "\nQR code generation completed successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
