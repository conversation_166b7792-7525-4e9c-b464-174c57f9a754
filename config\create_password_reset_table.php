<?php
require_once 'database.php';

// Create password_reset_requests table if it doesn't exist
$result = $conn->query("SHOW TABLES LIKE 'password_reset_requests'");
if ($result->num_rows == 0) {
    $sql = "CREATE TABLE password_reset_requests (
        request_id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        user_type ENU<PERSON>('student', 'lecturer') NOT NULL,
        request_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
        processed_by INT NULL,
        processed_date DATETIME NULL
    )";
    
    if ($conn->query($sql)) {
        echo "Password reset requests table created successfully";
    } else {
        echo "Error creating password reset requests table: " . $conn->error;
    }
} else {
    echo "Password reset requests table already exists";
}

// Create notifications table if it doesn't exist
$result = $conn->query("SHOW TABLES LIKE 'notifications'");
if ($result->num_rows == 0) {
    $sql = "CREATE TABLE notifications (
        notification_id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        user_type ENUM('student', 'lecturer', 'admin') NOT NULL,
        message TEXT NOT NULL,
        is_read TINYINT(1) DEFAULT 0,
        related_id INT NULL,
        related_type VARCHAR(50) NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($sql)) {
        echo "Notifications table created successfully";
    } else {
        echo "Error creating notifications table: " . $conn->error;
    }
} else {
    echo "Notifications table already exists";
}

// Create class_sessions table if it doesn't exist
$result = $conn->query("SHOW TABLES LIKE 'class_sessions'");
if ($result->num_rows == 0) {
    $sql = "CREATE TABLE class_sessions (
        session_id INT AUTO_INCREMENT PRIMARY KEY,
        course_id INT NOT NULL,
        lecturer_id INT NOT NULL,
        start_time DATETIME NOT NULL,
        end_time DATETIME NULL,
        status ENUM('active', 'ended') DEFAULT 'active',
        FOREIGN KEY (course_id) REFERENCES courses(course_id),
        FOREIGN KEY (lecturer_id) REFERENCES lecturers(lecturer_id)
    )";
    
    if ($conn->query($sql)) {
        echo "Class sessions table created successfully";
    } else {
        echo "Error creating class sessions table: " . $conn->error;
    }
} else {
    echo "Class sessions table already exists";
}

// Create lecturer_courses table if it doesn't exist
$result = $conn->query("SHOW TABLES LIKE 'lecturer_courses'");
if ($result->num_rows == 0) {
    $sql = "CREATE TABLE lecturer_courses (
        id INT AUTO_INCREMENT PRIMARY KEY,
        lecturer_id INT NOT NULL,
        course_id INT NOT NULL,
        FOREIGN KEY (lecturer_id) REFERENCES lecturers(lecturer_id),
        FOREIGN KEY (course_id) REFERENCES courses(course_id),
        UNIQUE KEY (lecturer_id, course_id)
    )";
    
    if ($conn->query($sql)) {
        echo "Lecturer courses table created successfully";
    } else {
        echo "Error creating lecturer courses table: " . $conn->error;
    }
} else {
    echo "Lecturer courses table already exists";
}

// Fix admin table name if needed
$result = $conn->query("SHOW TABLES LIKE 'admin'");
if ($result->num_rows > 0) {
    // Check if admins table exists
    $result2 = $conn->query("SHOW TABLES LIKE 'admins'");
    if ($result2->num_rows == 0) {
        // Rename admin table to admins
        $sql = "RENAME TABLE `admin` TO `admins`";
        if ($conn->query($sql)) {
            echo "Admin table renamed to admins successfully";
        } else {
            echo "Error renaming admin table: " . $conn->error;
        }
    }
}

echo "Database tables check completed";
?>
