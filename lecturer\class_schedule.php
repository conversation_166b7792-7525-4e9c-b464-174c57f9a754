<?php
require_once '../config/functions.php';

// Check if lecturer is logged in
if (!isLecturerLoggedIn()) {
    redirect('../index.php');
}

// Get lecturer details
$lecturer_id = $_SESSION['lecturer_id'];
$lecturer = getLecturerDetails($lecturer_id);

// Process form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['create_schedule'])) {
        $course_id = sanitize($_POST['course_id']);
        $week_number = sanitize($_POST['week_number']);
        $class_date = sanitize($_POST['class_date']);
        $start_time = sanitize($_POST['start_time']);
        $end_time = sanitize($_POST['end_time']);

        // Create class schedule
        $result = createClassSchedule($course_id, $lecturer_id, $week_number, $class_date, $start_time, $end_time);

        if ($result['success']) {
            $success_message = $result['message'];

            // Check if we can auto-fill the rest of the weeks
            if ($week_number == 2) {
                $pattern_result = detectSchedulePattern($course_id, $lecturer_id);
                if ($pattern_result) {
                    $auto_fill = autoFillSchedule($course_id, $lecturer_id);
                    if ($auto_fill['success']) {
                        $success_message .= '. ' . $auto_fill['message'];
                    }
                }
            }
        } else {
            $error_message = $result['message'];
        }
    } elseif (isset($_POST['auto_fill'])) {
        $course_id = sanitize($_POST['course_id']);

        // Auto-fill remaining weeks
        $result = autoFillSchedule($course_id, $lecturer_id);

        if ($result['success']) {
            $success_message = $result['message'];
        } else {
            $error_message = $result['message'];
        }
    } elseif (isset($_POST['reset_schedule'])) {
        // Reset all schedules for this lecturer

        // First, check if there are any active classes
        $active_check = $conn->prepare("
            SELECT COUNT(*) as count
            FROM class_schedules
            WHERE lecturer_id = ? AND status = 'started'
        ");
        $active_check->bind_param("i", $lecturer_id);
        $active_check->execute();
        $active_result = $active_check->get_result()->fetch_assoc();

        if ($active_result['count'] > 0) {
            $error_message = "Cannot reset schedule while classes are in progress. Please end all active classes first.";
        } else {
            // Delete all schedules for this lecturer
            $delete_schedules = $conn->prepare("
                DELETE FROM class_schedules
                WHERE lecturer_id = ? AND status != 'ended'
            ");
            $delete_schedules->bind_param("i", $lecturer_id);

            if ($delete_schedules->execute()) {
                $affected_rows = $delete_schedules->affected_rows;

                // Delete all patterns for this lecturer
                $delete_patterns = $conn->prepare("
                    DELETE FROM schedule_patterns
                    WHERE lecturer_id = ?
                ");
                $delete_patterns->bind_param("i", $lecturer_id);
                $delete_patterns->execute();

                // Delete all reminders for this lecturer
                $delete_reminders = $conn->prepare("
                    DELETE FROM class_reminders
                    WHERE lecturer_id = ?
                ");
                $delete_reminders->bind_param("i", $lecturer_id);
                $delete_reminders->execute();

                $success_message = "Successfully reset all schedules. Removed $affected_rows scheduled classes.";
            } else {
                $error_message = "Error resetting schedules: " . $conn->error;
            }
        }
    } elseif (isset($_POST['start_class'])) {
        $schedule_id = sanitize($_POST['schedule_id']);

        // Start class
        $result = startScheduledClass($schedule_id);

        if ($result['success']) {
            $success_message = $result['message'];
        } else {
            $error_message = $result['message'];
        }
    } elseif (isset($_POST['end_class'])) {
        $schedule_id = sanitize($_POST['schedule_id']);

        // End class
        $result = endScheduledClass($schedule_id);

        if ($result['success']) {
            $success_message = $result['message'];
        } else {
            $error_message = $result['message'];
        }
    } elseif (isset($_POST['cancel_class'])) {
        $schedule_id = sanitize($_POST['schedule_id']);

        // Cancel class
        $result = cancelScheduledClass($schedule_id);

        if ($result['success']) {
            $success_message = $result['message'];
        } else {
            $error_message = $result['message'];
        }
    }
}

// Get courses for this lecturer
$courses = getLecturerCourses($lecturer_id);

// Check if class_schedules table exists
$table_exists = false;
$result = $conn->query("SHOW TABLES LIKE 'class_schedules'");
if ($result->num_rows > 0) {
    $table_exists = true;
    // Get lecturer's schedules
    $schedules = getLecturerSchedules($lecturer_id);
} else {
    // Table doesn't exist, show setup message
    $error_message = "The class scheduling tables have not been set up yet. Please run the setup script first.";
    // Create an empty result set
    $schedules = $conn->query("SELECT 1 LIMIT 0");
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Class Schedule - Ogbonnaya Onu Polytechnic</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .schedule-table th, .schedule-table td {
            text-align: center;
            vertical-align: middle;
        }
        .schedule-table th {
            background-color: #f8f9fa;
        }
        .week-header {
            background-color: #e9ecef;
            font-weight: bold;
        }
        .status-scheduled {
            color: #0d6efd;
        }
        .status-started {
            color: #198754;
        }
        .status-ended {
            color: #6c757d;
        }
        .status-cancelled {
            color: #dc3545;
        }

        /* Custom button styles */
        .btn-reset-schedule {
            background-color: #ffc107;
            border-color: #ffc107;
            color: #212529;
        }

        .btn-reset-schedule:hover {
            background-color: #e0a800;
            border-color: #d39e00;
            color: #212529;
        }
    </style>
</head>
<body>
    <!-- Include Navbar -->
    <?php include 'includes/navbar.php'; ?>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col-md-12">
                <h2>Class Schedule</h2>
                <p class="text-muted">Manage your class schedules for the 9-week semester</p>
            </div>
        </div>

        <?php if (!empty($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i> <?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if (!empty($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-circle me-2"></i> <?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                <?php if (!$table_exists): ?>
                    <div class="mt-3">
                        <a href="../setup_scheduling.php" class="btn btn-primary">
                            <i class="fas fa-database me-2"></i> Run Setup Script
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <!-- Create Schedule Form -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-calendar-plus me-2"></i> Create Class Schedule</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="" id="scheduleForm">
                    <div class="row align-items-end">
                        <div class="col-md-3">
                            <label for="course_id" class="form-label">Course</label>
                            <select class="form-select" id="course_id" name="course_id" required>
                                <option value="">-- Select Course --</option>
                                <?php
                                if ($courses->num_rows > 0) {
                                    $courses->data_seek(0);
                                    while ($course = $courses->fetch_assoc()):
                                ?>
                                    <option value="<?php echo $course['course_id']; ?>">
                                        <?php echo $course['course_code'] . ' - ' . $course['course_title']; ?>
                                    </option>
                                <?php
                                    endwhile;
                                }
                                ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="week_number" class="form-label">Week</label>
                            <select class="form-select" id="week_number" name="week_number" required>
                                <option value="">-- Select Week --</option>
                                <?php for ($i = 1; $i <= 9; $i++): ?>
                                    <option value="<?php echo $i; ?>">Week <?php echo $i; ?></option>
                                <?php endfor; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="class_date" class="form-label">Date</label>
                            <input type="date" class="form-control" id="class_date" name="class_date" required>
                        </div>
                        <div class="col-md-2">
                            <label for="start_time" class="form-label">Start Time</label>
                            <input type="time" class="form-control" id="start_time" name="start_time" required>
                        </div>
                        <div class="col-md-2">
                            <label for="end_time" class="form-label">End Time</label>
                            <input type="time" class="form-control" id="end_time" name="end_time" required>
                        </div>
                        <div class="col-md-1">
                            <button type="submit" name="create_schedule" class="btn btn-primary w-100">
                                <i class="fas fa-save me-2"></i> Save Schedule
                            </button>
                        </div>
                    </div>
                    <div class="d-flex justify-content-end mt-3">
                        <button type="submit" name="auto_fill" class="btn btn-secondary me-2" id="autoFillBtn">
                            <i class="fas fa-magic me-2"></i> Auto-Fill Remaining Weeks
                        </button>
                        <button type="submit" name="reset_schedule" class="btn btn-reset-schedule" id="resetScheduleBtn" onclick="return confirm('Are you sure you want to reset all schedules? This will delete all scheduled classes that have not ended yet.')">
                            <i class="fas fa-trash-alt me-2"></i> Reset Schedule
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Course Schedules -->
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="fas fa-calendar-alt me-2"></i> My Class Schedules</h5>
            </div>
            <div class="card-body">
                <?php if ($schedules->num_rows > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover schedule-table">
                            <thead>
                                <tr>
                                    <th>Course</th>
                                    <th>Week</th>
                                    <th>Date</th>
                                    <th>Time</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $current_course_id = 0;
                                $current_course = '';

                                while ($schedule = $schedules->fetch_assoc()):
                                    $status_class = 'status-' . $schedule['status'];
                                    $formatted_date = date('D, M d, Y', strtotime($schedule['class_date']));
                                    $formatted_time = date('h:i A', strtotime($schedule['start_time'])) . ' - ' . date('h:i A', strtotime($schedule['end_time']));

                                    // Check if this is a new course
                                    if ($current_course_id != $schedule['course_id']) {
                                        $current_course_id = $schedule['course_id'];
                                        $current_course = $schedule['course_code'] . ' - ' . $schedule['course_title'] . ' (' . $schedule['level_name'] . ', ' . $schedule['semester_name'] . ')';
                                        echo '<tr class="week-header"><td colspan="6">' . $current_course . '</td></tr>';
                                    }
                                ?>
                                <tr>
                                    <td><?php echo $schedule['course_code']; ?></td>
                                    <td>Week <?php echo $schedule['week_number']; ?></td>
                                    <td><?php echo $formatted_date; ?></td>
                                    <td><?php echo $formatted_time; ?></td>
                                    <td>
                                        <span class="<?php echo $status_class; ?>">
                                            <?php echo ucfirst($schedule['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($schedule['status'] === 'scheduled'): ?>
                                            <form method="POST" action="" class="d-inline">
                                                <input type="hidden" name="schedule_id" value="<?php echo $schedule['schedule_id']; ?>">
                                                <button type="submit" name="start_class" class="btn btn-sm btn-success">
                                                    <i class="fas fa-play-circle"></i> Start
                                                </button>
                                            </form>
                                            <form method="POST" action="" class="d-inline">
                                                <input type="hidden" name="schedule_id" value="<?php echo $schedule['schedule_id']; ?>">
                                                <button type="submit" name="cancel_class" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to cancel this class?')">
                                                    <i class="fas fa-times-circle"></i> Cancel
                                                </button>
                                            </form>
                                        <?php elseif ($schedule['status'] === 'started'): ?>
                                            <form method="POST" action="" class="d-inline">
                                                <input type="hidden" name="schedule_id" value="<?php echo $schedule['schedule_id']; ?>">
                                                <button type="submit" name="end_class" class="btn btn-sm btn-warning">
                                                    <i class="fas fa-stop-circle"></i> End
                                                </button>
                                            </form>
                                        <?php else: ?>
                                            <span class="text-muted">No actions available</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> No class schedules found. Create your first schedule above.
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-3 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; <?php echo date('Y'); ?> Ogbonnaya Onu Polytechnic. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="../about.php" class="text-white me-3">About</a>
                    <a href="../contact.php" class="text-white">Contact</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/script.js"></script>
    <!-- Session Manager -->
    <script src="../assets/js/session_manager.js"></script>
    <script>
        $(document).ready(function() {
            // Handle auto-fill button click
            $('#autoFillBtn').click(function(e) {
                if (!$('#course_id').val()) {
                    e.preventDefault();
                    alert('Please select a course first');
                }
            });

            // Set minimum date for class_date to today
            var today = new Date().toISOString().split('T')[0];
            $('#class_date').attr('min', today);

            // Validate end time is after start time
            $('#scheduleForm').submit(function(e) {
                // Only validate if this is a create schedule submission
                if (e.originalEvent && e.originalEvent.submitter && e.originalEvent.submitter.name === 'create_schedule') {
                    var startTime = $('#start_time').val();
                    var endTime = $('#end_time').val();

                    if (startTime && endTime && startTime >= endTime) {
                        alert('End time must be after start time');
                        e.preventDefault();
                    }

                    // Validate required fields
                    if (!$('#course_id').val() || !$('#week_number').val() || !$('#class_date').val() ||
                        !$('#start_time').val() || !$('#end_time').val()) {
                        alert('Please fill in all required fields');
                        e.preventDefault();
                    }
                }
            });
        });
    </script>
</body>
</html>
