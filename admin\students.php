<?php
require_once '../config/functions.php';
require_once 'auth_check.php';

// Get all students with department and level names
$students = $conn->query("
    SELECT s.*, d.dept_name, l.level_name
    FROM students s
    JOIN departments d ON s.dept_id = d.dept_id
    JOIN levels l ON s.level_id = l.level_id
    ORDER BY s.matric_number
");

// Run update_students_table.php if needed to ensure phone field exists
$checkPhone = $conn->query("SHOW COLUMNS FROM students LIKE 'phone'");
if ($checkPhone->num_rows == 0) {
    // Add phone column if it doesn't exist
    $conn->query("ALTER TABLE students ADD COLUMN phone VARCHAR(20) DEFAULT NULL AFTER email");
    // Update existing students with placeholder phone numbers
    $conn->query("UPDATE students SET phone = CONCAT('080', FLOOR(RAND() * 100000000)) WHERE phone IS NULL OR phone = ''");
    // Refresh students list
    $students = $conn->query("
        SELECT s.*, d.dept_name, l.level_name
        FROM students s
        JOIN departments d ON s.dept_id = d.dept_id
        JOIN levels l ON s.level_id = l.level_id
        ORDER BY s.matric_number
    ");
}

// Process delete student if requested
$message = '';
if (isset($_GET['delete']) && !empty($_GET['delete'])) {
    $student_id = sanitize($_GET['delete']);

    // Check if student exists
    $stmt = $conn->prepare("SELECT student_id FROM students WHERE student_id = ?");
    $stmt->bind_param("i", $student_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        // Delete student
        $stmt = $conn->prepare("DELETE FROM students WHERE student_id = ?");
        $stmt->bind_param("i", $student_id);

        if ($stmt->execute()) {
            $message = displaySuccess("Student deleted successfully!");
            // Refresh students list
            $students = $conn->query("
                SELECT s.*, d.dept_name, l.level_name
                FROM students s
                JOIN departments d ON s.dept_id = d.dept_id
                JOIN levels l ON s.level_id = l.level_id
                ORDER BY s.matric_number
            ");
        } else {
            $message = displayError("Error deleting student: " . $conn->error);
        }
    } else {
        $message = displayError("Student not found");
    }
}

// Process approve student if requested
if (isset($_GET['approve']) && !empty($_GET['approve'])) {
    $student_id = sanitize($_GET['approve']);

    // Check if student exists
    $stmt = $conn->prepare("SELECT student_id FROM students WHERE student_id = ?");
    $stmt->bind_param("i", $student_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        // Approve student
        $stmt = $conn->prepare("UPDATE students SET status = 'approved' WHERE student_id = ?");
        $stmt->bind_param("i", $student_id);

        if ($stmt->execute()) {
            $message = displaySuccess("Student approved successfully!");
            // Refresh students list
            $students = $conn->query("
                SELECT s.*, d.dept_name, l.level_name
                FROM students s
                JOIN departments d ON s.dept_id = d.dept_id
                JOIN levels l ON s.level_id = l.level_id
                ORDER BY s.matric_number
            ");
        } else {
            $message = displayError("Error approving student: " . $conn->error);
        }
    } else {
        $message = displayError("Student not found");
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All Students - Ogbonnaya Onu Polytechnic</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }

        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .card-header {
            background-color: #343a40;
            color: white;
            border-radius: 10px 10px 0 0 !important;
        }

        .status-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="../images/logo.jpg" alt="Logo" width="30" height="30" class="d-inline-block align-text-top me-2 rounded-circle">
                Admin Dashboard
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Students
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item active" href="students.php">All Students</a></li>
                            <li><a class="dropdown-item" href="pending_students.php">Pending Approvals</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Lecturers
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="lecturers.php">All Lecturers</a></li>
                            <li><a class="dropdown-item" href="pending_lecturers.php">Pending Approvals</a></li>
                            <li><a class="dropdown-item" href="assign_courses.php">Assign Courses</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Courses
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="courses.php">All Courses</a></li>
                            <li><a class="dropdown-item" href="add_course.php">Add New Course</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Departments
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="departments.php">All Departments</a></li>
                            <li><a class="dropdown-item" href="add_department.php">Add New Department</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">Reports</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-1"></i> <?php echo $_SESSION['username']; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profile</a></li>
                            <li><a class="dropdown-item" href="change_password.php">Change Password</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col-md-12">
                <h2>All Students</h2>
                <p class="text-muted">Manage students registered in the system</p>
            </div>
        </div>

        <?php echo $message; ?>

        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Students List</h5>
            </div>
            <div class="card-body">
                <?php if ($students->num_rows > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover data-table">
                            <thead>
                                <tr>
                                    <th>Matric Number</th>
                                    <th>Name</th>
                                    <th>Department</th>
                                    <th>Level</th>
                                    <th>Email</th>
                                    <th>Phone</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($student = $students->fetch_assoc()): ?>
                                    <tr>
                                        <td><?php echo $student['matric_number']; ?></td>
                                        <td><?php echo $student['name']; ?></td>
                                        <td><?php echo $student['dept_name']; ?></td>
                                        <td><?php echo $student['level_name']; ?></td>
                                        <td><?php echo $student['email']; ?></td>
                                        <td><?php echo $student['phone']; ?></td>
                                        <td>
                                            <?php if ($student['status'] == 'approved'): ?>
                                                <span class="badge bg-success status-badge">Approved</span>
                                            <?php else: ?>
                                                <span class="badge bg-warning status-badge">Pending</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <a href="view_student.php?id=<?php echo $student['student_id']; ?>" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php if ($student['status'] == 'pending'): ?>
                                                <a href="students.php?approve=<?php echo $student['student_id']; ?>" class="btn btn-sm btn-success" onclick="return confirm('Are you sure you want to approve this student?')">
                                                    <i class="fas fa-check"></i>
                                                </a>
                                            <?php endif; ?>
                                            <a href="students.php?delete=<?php echo $student['student_id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this student?')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info">No students found in the system.</div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Add extra space before footer -->
    <div class="container">
        <div class="row">
            <div class="col-12" style="height: 100px;"></div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-3 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; <?php echo date('Y'); ?> Ogbonnaya Onu Polytechnic. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="../about.php" class="text-white me-3">About</a>
                    <a href="../support.php" class="text-white">Support</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script>
        $(document).ready(function() {
            $('.data-table').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
            });
        });
    </script>
</body>
</html>
