<?php
require_once '../config/functions.php';

// Check if student is logged in
if (!isStudentLoggedIn()) {
    redirect('../index.php');
}

// Get student details
$student_id = $_SESSION['student_id'];
$student = getStudentDetails($student_id);

// Get department, level, and semester names
$dept_name = getDepartmentName($student['dept_id']);
$level_name = getLevelName($student['level_id']);
$semester_name = getSemesterName($student['semester_id']);

// Get attendance records for this student
$attendance = $conn->query("
    SELECT a.*, c.course_code, c.course_title, cs.start_time, cs.end_time
    FROM attendance a
    JOIN courses c ON a.course_id = c.course_id
    LEFT JOIN class_sessions cs ON a.session_id = cs.session_id
    WHERE a.student_id = $student_id
    ORDER BY a.attendance_date DESC, a.attendance_time DESC
");

// Get attendance statistics
$stats = $conn->query("
    SELECT
        COUNT(*) as total_records,
        SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,
        SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count
    FROM attendance
    WHERE student_id = $student_id
")->fetch_assoc();

$total_records = $stats['total_records'];
$present_count = $stats['present_count'];
$absent_count = $stats['absent_count'];
$attendance_percentage = $total_records > 0 ? round(($present_count / $total_records) * 100, 1) : 0;

// Get all courses for this student's department, level, and semester
$courses = $conn->query("
    SELECT c.*
    FROM courses c
    WHERE c.dept_id = {$student['dept_id']}
    AND c.level_id = {$student['level_id']}
    AND c.semester_id = {$student['semester_id']}
");

// No courses found - admin needs to add courses for this department/level/semester

// Count total courses
$totalCourses = $courses->num_rows;

// Get all courses with their attendance data
$courseAttendanceData = [];
$totalConductedClasses = 0;
$totalAttendedClasses = 0;

// Reset courses result pointer
$courses->data_seek(0);

while ($course = $courses->fetch_assoc()) {
    // Count conducted classes for this course based on ended sessions
    $conductedClassesQuery = $conn->query("
        SELECT COUNT(*) as count
        FROM class_sessions
        WHERE course_id = {$course['course_id']}
        AND status = 'ended'
    ");
    $conductedClasses = $conductedClassesQuery->fetch_assoc()['count'];

    // Count attended classes for this course
    $attendedClassesQuery = $conn->query("
        SELECT COUNT(*) as count
        FROM attendance
        WHERE student_id = $student_id
        AND course_id = {$course['course_id']}
    ");
    $attendedClasses = $attendedClassesQuery->fetch_assoc()['count'];

    // Calculate attendance rate for this course
    $courseAttendanceRate = ($conductedClasses > 0) ? round(($attendedClasses / $conductedClasses) * 100) : 0;

    // Store course data
    $courseAttendanceData[$course['course_id']] = [
        'course' => $course,
        'conducted_classes' => $conductedClasses,
        'attended_classes' => $attendedClasses,
        'attendance_rate' => $courseAttendanceRate
    ];

    // Add to totals
    $totalConductedClasses += $conductedClasses;
    $totalAttendedClasses += $attendedClasses;
}

// Count attended courses (at least one attendance record)
$attendedCoursesQuery = $conn->query("
    SELECT COUNT(DISTINCT course_id) as count
    FROM attendance
    WHERE student_id = $student_id
");
$attendedCourses = $attendedCoursesQuery->fetch_assoc()['count'];

// Calculate overall attendance percentage based on courses
$courseAttendancePercentage = ($totalCourses > 0) ? round(($attendedCourses / $totalCourses) * 100) : 0;

// Calculate overall attendance percentage based on classes
$attendancePercentage = ($totalConductedClasses > 0) ? round(($totalAttendedClasses / $totalConductedClasses) * 100) : 0;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Attendance - Ogbonnaya Onu Polytechnic</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="../images/logo.jpg" alt="Logo" width="30" height="30" class="d-inline-block align-text-top me-2 rounded-circle">
                Student Dashboard
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">My Courses</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="attendance.php">My Attendance</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="scan.php">Scan QR Code</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-1"></i> <?php echo $student['name']; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profile</a></li>
                            <li><a class="dropdown-item" href="change_password.php">Change Password</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col-md-12">
                <h2>My Attendance</h2>
                <p class="text-muted">View your attendance records for all courses</p>
            </div>
        </div>

        <!-- Attendance Summary -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-book dashboard-icon"></i>
                        <h5 class="card-title">Total Courses</h5>
                        <h2 class="card-text"><?php echo $totalCourses; ?></h2>
                        <p class="text-muted">For this semester</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-calendar-check dashboard-icon"></i>
                        <h5 class="card-title">Classes Conducted</h5>
                        <h2 class="card-text"><?php echo $totalConductedClasses; ?></h2>
                        <p class="text-muted">Total classes held</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-clipboard-check dashboard-icon"></i>
                        <h5 class="card-title">Classes Attended</h5>
                        <h2 class="card-text"><?php echo $totalAttendedClasses; ?></h2>
                        <p class="text-muted">Out of <?php echo $totalConductedClasses; ?> classes</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-chart-pie dashboard-icon"></i>
                        <h5 class="card-title">Attendance Rate</h5>
                        <h2 class="card-text"><?php echo $attendancePercentage; ?>%</h2>
                        <div class="progress mt-2">
                            <div class="progress-bar bg-<?php echo ($attendancePercentage >= 75) ? 'success' : (($attendancePercentage >= 50) ? 'warning' : 'danger'); ?>"
                                 role="progressbar"
                                 style="width: <?php echo $attendancePercentage; ?>%"
                                 aria-valuenow="<?php echo $attendancePercentage; ?>"
                                 aria-valuemin="0"
                                 aria-valuemax="100"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Attendance Summary -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-clipboard-list text-primary fa-2x mb-2"></i>
                        <h4><?php echo $total_records; ?></h4>
                        <p class="text-muted">Total Records</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-check-circle text-success fa-2x mb-2"></i>
                        <h4><?php echo $present_count; ?></h4>
                        <p class="text-muted">Present</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-times-circle text-danger fa-2x mb-2"></i>
                        <h4><?php echo $absent_count; ?></h4>
                        <p class="text-muted">Absent</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-percentage text-info fa-2x mb-2"></i>
                        <h4><?php echo $attendance_percentage; ?>%</h4>
                        <p class="text-muted">Attendance Rate</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Attendance Records -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Detailed Attendance Records</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($attendance->num_rows > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover data-table">
                                    <thead>
                                        <tr>
                                            <th>Course Code</th>
                                            <th>Course Title</th>
                                            <th>Date</th>
                                            <th>Time</th>
                                            <th>Status</th>
                                            <th>Marked By</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($record = $attendance->fetch_assoc()): ?>
                                            <tr>
                                                <td><?php echo $record['course_code']; ?></td>
                                                <td><?php echo $record['course_title']; ?></td>
                                                <td><?php echo date('d M Y', strtotime($record['attendance_date'])); ?></td>
                                                <td><?php echo date('h:i A', strtotime($record['attendance_time'])); ?></td>
                                                <td>
                                                    <?php if ($record['status'] == 'present'): ?>
                                                        <span class="badge bg-success">Present</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">Absent</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php
                                                    $marked_by = ucfirst($record['marked_by'] ?? 'student');
                                                    echo $marked_by;
                                                    ?>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> No attendance records found yet. Scan a course QR code to mark your attendance.
                            </div>
                            <a href="scan.php" class="btn btn-primary">
                                <i class="fas fa-qrcode me-2"></i> Scan QR Code
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Course Attendance Status -->
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Detailed Course Attendance</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($totalCourses > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>Course Code</th>
                                            <th>Course Title</th>
                                            <th>Classes Conducted</th>
                                            <th>Classes Attended</th>
                                            <th>Attendance Rate</th>
                                            <th>Status</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($courseAttendanceData as $courseId => $data):
                                            $course = $data['course'];
                                            $conductedClasses = $data['conducted_classes'];
                                            $attendedClasses = $data['attended_classes'];
                                            $attendanceRate = $data['attendance_rate'];
                                            $attended = $attendedClasses > 0;

                                            // Determine status color based on attendance rate
                                            if ($conductedClasses == 0) {
                                                $statusClass = 'secondary';
                                                $statusText = 'No Classes Yet';
                                            } else if ($attendanceRate >= 75) {
                                                $statusClass = 'success';
                                                $statusText = 'Good';
                                            } else if ($attendanceRate >= 50) {
                                                $statusClass = 'warning';
                                                $statusText = 'Average';
                                            } else {
                                                $statusClass = 'danger';
                                                $statusText = 'Poor';
                                            }
                                        ?>
                                            <tr>
                                                <td><?php echo $course['course_code']; ?></td>
                                                <td><?php echo $course['course_title']; ?></td>
                                                <td><?php echo $conductedClasses; ?></td>
                                                <td><?php echo $attendedClasses; ?></td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                                            <div class="progress-bar bg-<?php echo $statusClass; ?>"
                                                                role="progressbar"
                                                                style="width: <?php echo $attendanceRate; ?>%"
                                                                aria-valuenow="<?php echo $attendanceRate; ?>"
                                                                aria-valuemin="0"
                                                                aria-valuemax="100"></div>
                                                        </div>
                                                        <span><?php echo $attendanceRate; ?>%</span>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge bg-<?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                                                </td>
                                                <td>
                                                    <?php if ($conductedClasses > 0 && $attendedClasses < $conductedClasses): ?>
                                                        <a href="scan.php" class="btn btn-sm btn-primary">
                                                            <i class="fas fa-qrcode"></i> Mark Attendance
                                                        </a>
                                                    <?php elseif ($conductedClasses == 0): ?>
                                                        <button class="btn btn-sm btn-secondary" disabled>
                                                            <i class="fas fa-clock"></i> Waiting
                                                        </button>
                                                    <?php else: ?>
                                                        <button class="btn btn-sm btn-success" disabled>
                                                            <i class="fas fa-check"></i> All Attended
                                                        </button>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> No courses found for your department, level, and semester.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-3 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; <?php echo date('Y'); ?> Ogbonnaya Onu Polytechnic. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="../about.php" class="text-white me-3">About</a>
                    <a href="../support.php" class="text-white">Support</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/script.js"></script>
    <script>
        $(document).ready(function() {
            $('.data-table').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
            });
        });
    </script>
</body>
</html>
