<?php
require_once '../config/functions.php';

// Check if lecturer is logged in
if (!isLecturerLoggedIn()) {
    redirect('../index.php');
}

// Get lecturer details
$lecturer_id = $_SESSION['lecturer_id'];
$lecturer = getLecturerDetails($lecturer_id);

// Get departments for dropdowns
$departments = $conn->query("SELECT * FROM departments ORDER BY dept_name");

$error = '';
$success = '';

// Handle profile update
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitize($_POST['name']);
    $email = sanitize($_POST['email']);
    $phone = sanitize($_POST['phone']);

    // Validate input
    if (empty($name) || empty($email) || empty($phone)) {
        $error = "Name, email, and phone are required";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = "Invalid email format";
    } elseif (!preg_match('/^[0-9]{11}$/', $phone)) {
        $error = "Phone number must be exactly 11 digits";
    } else {
        // Check if email is already used by another lecturer
        $stmt = $conn->prepare("SELECT lecturer_id FROM lecturers WHERE email = ? AND lecturer_id != ?");
        $stmt->bind_param("si", $email, $lecturer_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $error = "Email is already used by another lecturer";
        } else {
            // Update profile
            $stmt = $conn->prepare("UPDATE lecturers SET name = ?, email = ?, phone = ? WHERE lecturer_id = ?");
            $stmt->bind_param("sssi", $name, $email, $phone, $lecturer_id);

            if ($stmt->execute()) {
                $success = "Profile updated successfully";

                // Refresh lecturer data
                $lecturer = getLecturerDetails($lecturer_id);

                // Update session username
                $_SESSION['username'] = $name;
            } else {
                $error = "Failed to update profile: " . $conn->error;
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Profile - Ogbonnaya Onu Polytechnic</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="../images/logo.jpg" alt="Logo" width="30" height="30" class="d-inline-block align-text-top me-2 rounded-circle">
                Lecturer Dashboard
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">My Courses</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="start_class.php">Start Class</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="attendance.php">Attendance</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">Reports</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">Students</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-1"></i> <?php echo $lecturer['name']; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item active" href="profile.php">Profile</a></li>
                            <li><a class="dropdown-item" href="change_password.php">Change Password</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col-md-12">
                <h2>My Profile</h2>
                <p class="text-muted">View and update your profile information</p>
            </div>
        </div>

        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Profile Summary</h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="fas fa-user-circle fa-5x text-primary"></i>
                        </div>
                        <h5><?php echo $lecturer['name']; ?></h5>
                        <p class="text-muted mb-1"><?php echo $lecturer['staff_code']; ?></p>
                        <p class="text-muted mb-1"><?php echo $lecturer['email']; ?></p>
                        <p class="text-muted mb-1"><?php echo $lecturer['phone']; ?></p>

                        <div class="mt-3">
                            <a href="change_password.php" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-key me-1"></i> Change Password
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Edit Profile</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($error)): ?>
                            <div class="alert alert-danger"><?php echo $error; ?></div>
                        <?php endif; ?>

                        <?php if (!empty($success)): ?>
                            <div class="alert alert-success"><?php echo $success; ?></div>
                        <?php endif; ?>

                        <form method="POST" action="">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">Full Name</label>
                                    <input type="text" class="form-control" id="name" name="name" value="<?php echo $lecturer['name']; ?>" required>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="staff_code" class="form-label">Staff Code</label>
                                    <input type="text" class="form-control" id="staff_code" value="<?php echo $lecturer['staff_code']; ?>" readonly>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="email" name="email" value="<?php echo $lecturer['email']; ?>" required>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control" id="phone" name="phone" value="<?php echo $lecturer['phone']; ?>" pattern="[0-9]{11}" maxlength="11" placeholder="Enter 11-digit phone number" required>
                                    <div class="form-text">Please enter exactly 11 digits (e.g., 08012345678)</div>
                                    <div id="phone-feedback" class="invalid-feedback" style="display: none;">
                                        Phone number must be exactly 11 digits
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="department" class="form-label">Department</label>
                                    <input type="text" class="form-control" id="department" value="<?php
                                        $dept = $conn->query("SELECT dept_name FROM departments WHERE dept_id = {$lecturer['dept_id']}")->fetch_assoc();
                                        echo $dept['dept_name'];
                                    ?>" readonly>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="status" class="form-label">Account Status</label>
                                    <input type="text" class="form-control" id="status" value="<?php echo ucfirst($lecturer['status']); ?>" readonly>
                                </div>
                            </div>

                            <div class="d-grid mt-3">
                                <button type="submit" class="btn btn-primary">Update Profile</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-3 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; <?php echo date('Y'); ?> Ogbonnaya Onu Polytechnic. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="../about.php" class="text-white me-3">About</a>
                    <a href="../support.php" class="text-white">Support</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/script.js"></script>

    <script>
    // Phone number validation
    document.getElementById('phone').addEventListener('input', function(e) {
        // Remove any non-digit characters
        let value = e.target.value.replace(/\D/g, '');

        // Limit to 11 digits
        if (value.length > 11) {
            value = value.slice(0, 11);
        }

        e.target.value = value;

        // Show validation feedback
        const feedback = document.getElementById('phone-feedback');
        if (value.length === 11) {
            e.target.classList.remove('is-invalid');
            e.target.classList.add('is-valid');
            if (feedback) feedback.style.display = 'none';
        } else {
            e.target.classList.remove('is-valid');
            e.target.classList.add('is-invalid');
            if (feedback) feedback.style.display = 'block';
        }
    });

    // Form submission validation
    document.querySelector('form').addEventListener('submit', function(e) {
        const phone = document.getElementById('phone').value;

        if (phone.length !== 11) {
            e.preventDefault();
            alert('Please enter exactly 11 digits for phone number');
            return false;
        }
    });
    </script>
</body>
</html>
