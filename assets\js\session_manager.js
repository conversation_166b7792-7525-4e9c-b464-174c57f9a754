// Session management with different timeouts for different user types
document.addEventListener('DOMContentLoaded', function() {
    var inactivityTimeout;

    // Determine user type and set appropriate timeout
    var isAdmin = window.location.pathname.includes('/admin/');
    var isLecturer = window.location.pathname.includes('/lecturer/');
    var isStudent = window.location.pathname.includes('/student/');

    var timeoutDuration;
    if (isAdmin) {
        timeoutDuration = 2 * 60 * 60 * 1000; // 2 hours for admin
    } else if (isLecturer || isStudent) {
        timeoutDuration = 30 * 60 * 1000; // 30 minutes for lecturers and students
    } else {
        timeoutDuration = 30 * 60 * 1000; // Default 30 minutes
    }

    // Function to reset the inactivity timer
    function resetInactivityTimer() {
        clearTimeout(inactivityTimeout);
        inactivityTimeout = setTimeout(function() {
            // Redirect to logout page after inactivity timeout
            window.location.href = 'logout.php';
        }, timeoutDuration);
    }

    // Reset timer on user activity
    ['mousemove', 'mousedown', 'keypress', 'scroll', 'touchstart'].forEach(function(event) {
        document.addEventListener(event, resetInactivityTimer, true);
    });

    // Start the timer when the page loads
    resetInactivityTimer();
});
