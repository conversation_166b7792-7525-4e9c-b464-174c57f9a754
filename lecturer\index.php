<?php
require_once '../config/functions.php';

// Check if lecturer is logged in
if (!isLecturerLoggedIn()) {
    redirect('../index.php');
}

// Get lecturer details
$lecturer_id = $_SESSION['lecturer_id'];
$lecturer = getLecturerDetails($lecturer_id);

// Get current academic session
$current_session = getCurrentAcademicSession();

// Get assigned courses
$courses = $conn->query("
    SELECT c.*, d.dept_name, l.level_name, s.semester_name
    FROM lecturer_courses lc
    JOIN courses c ON lc.course_id = c.course_id
    JOIN departments d ON c.dept_id = d.dept_id
    JOIN levels l ON c.level_id = l.level_id
    JOIN semesters s ON c.semester_id = s.semester_id
    WHERE lc.lecturer_id = $lecturer_id
    ORDER BY c.course_order ASC, c.course_code ASC
");

// Get statistics
$totalCourses = $conn->query("SELECT COUNT(*) as count FROM lecturer_courses WHERE lecturer_id = $lecturer_id")->fetch_assoc()['count'];

// Get total students across all courses
$totalStudents = 0;
$courseStudentCounts = [];
$courseAttendanceCounts = [];
$totalAbsentStudents = 0;

// Reset courses result pointer
$courses->data_seek(0);
while ($course = $courses->fetch_assoc()) {
    // Count students in this course's department, level, and semester
    $query = "
        SELECT COUNT(*) as count
        FROM students
        WHERE dept_id = {$course['dept_id']}
        AND level_id = {$course['level_id']}
        AND semester_id = {$course['semester_id']}
        AND status = 'approved'
    ";
    $studentCount = $conn->query($query)->fetch_assoc()['count'];
    $totalStudents += $studentCount;
    $courseStudentCounts[$course['course_id']] = $studentCount;

    // Count attendance for this course
    $query = "
        SELECT COUNT(DISTINCT student_id) as count
        FROM attendance
        WHERE course_id = {$course['course_id']}
    ";
    $attendanceCount = $conn->query($query)->fetch_assoc()['count'];
    $courseAttendanceCounts[$course['course_id']] = $attendanceCount;
}

// Calculate absent students only from completed class sessions
$absentStudentsQuery = "
    SELECT COUNT(DISTINCT a.student_id) as count
    FROM attendance a
    JOIN class_sessions cs ON a.session_id = cs.session_id
    JOIN lecturer_courses lc ON a.course_id = lc.course_id
    WHERE lc.lecturer_id = $lecturer_id
    AND cs.status = 'ended'
    AND a.status = 'absent'
";
$totalAbsentStudents = $conn->query($absentStudentsQuery)->fetch_assoc()['count'];

// Get recent attendance
$recentAttendance = $conn->query("
    SELECT a.*, s.name as student_name, s.matric_number, c.course_code, c.course_title
    FROM attendance a
    JOIN students s ON a.student_id = s.student_id
    JOIN courses c ON a.course_id = c.course_id
    JOIN lecturer_courses lc ON c.course_id = lc.course_id
    WHERE lc.lecturer_id = $lecturer_id
    ORDER BY a.created_at DESC
    LIMIT 10
");
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lecturer Dashboard - Ogbonnaya Onu Polytechnic</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <!-- Include Navbar -->
    <?php include 'includes/navbar.php'; ?>

    <!-- Main Content -->
    <div class="container mt-4">
        <!-- Header Section with Current Session -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-1">Lecturer Dashboard</h2>
                        <p class="text-muted mb-0">Welcome to the Ogbonnaya Onu Polytechnic Attendance System</p>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="text-end">
                            <small class="text-muted d-block">Current Academic Session</small>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-calendar-alt text-primary me-2"></i>
                                <span class="fw-bold text-dark"><?php echo $current_session['academic_year']; ?></span>
                                <span class="mx-2">•</span>
                                <span class="text-primary"><?php echo $current_session['semester_name']; ?></span>
                            </div>
                        </div>
                    </div>
                </div>
                <hr class="mt-3 mb-0">
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="row dashboard-stats mb-4">
            <div class="col-md-3 col-sm-6 mb-3">
                <a href="courses.php" class="text-decoration-none">
                    <div class="card text-center h-100 card-hover">
                        <div class="card-body">
                            <i class="fas fa-book dashboard-icon text-primary"></i>
                            <h5 class="card-title">Total Courses</h5>
                            <h2 class="card-text"><?php echo $totalCourses; ?></h2>
                            <p class="text-muted">Assigned to you</p>
                        </div>
                        <div class="card-footer">
                            <span class="btn btn-sm btn-primary">View Courses</span>
                        </div>
                    </div>
                </a>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <a href="students.php" class="text-decoration-none">
                    <div class="card text-center h-100 card-hover">
                        <div class="card-body">
                            <i class="fas fa-user-graduate dashboard-icon text-success"></i>
                            <h5 class="card-title">Total Students</h5>
                            <h2 class="card-text"><?php echo $totalStudents; ?></h2>
                            <p class="text-muted">Across all your courses</p>
                        </div>
                        <div class="card-footer">
                            <span class="btn btn-sm btn-success">View Students</span>
                        </div>
                    </div>
                </a>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <a href="attendance.php" class="text-decoration-none">
                    <div class="card text-center h-100 card-hover">
                        <div class="card-body">
                            <i class="fas fa-qrcode dashboard-icon text-info"></i>
                            <h5 class="card-title">QR Attendance</h5>
                            <h2 class="card-text"><?php echo array_sum($courseAttendanceCounts); ?></h2>
                            <p class="text-muted">Total attendance records</p>
                        </div>
                        <div class="card-footer">
                            <span class="btn btn-sm btn-info">View Attendance</span>
                        </div>
                    </div>
                </a>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <a href="absent_students.php" class="text-decoration-none">
                    <div class="card text-center h-100 card-hover">
                        <div class="card-body">
                            <i class="fas fa-user-times dashboard-icon text-danger"></i>
                            <h5 class="card-title">Absent Students</h5>
                            <h2 class="card-text"><?php echo $totalAbsentStudents; ?></h2>
                            <p class="text-muted">Students who missed classes</p>
                        </div>
                        <div class="card-footer">
                            <span class="btn btn-sm btn-danger">View Absent</span>
                        </div>
                    </div>
                </a>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 col-sm-6 mb-2">
                                <a href="courses.php" class="btn btn-outline-primary d-block">
                                    <i class="fas fa-book me-2"></i> View My Courses
                                </a>
                            </div>
                            <div class="col-md-4 col-sm-6 mb-2">
                                <a href="start_class.php" class="btn btn-success d-block">
                                    <i class="fas fa-play-circle me-2"></i> Start Class
                                </a>
                            </div>
                            <div class="col-md-4 col-sm-6 mb-2">
                                <a href="absent_students.php" class="btn btn-outline-danger d-block">
                                    <i class="fas fa-user-times me-2"></i> View Absent Students
                                </a>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-6 col-sm-6 mb-2">
                                <a href="attendance.php" class="btn btn-outline-primary d-block">
                                    <i class="fas fa-clipboard-check me-2"></i> Check Attendance
                                </a>
                            </div>
                            <div class="col-md-6 col-sm-6 mb-2">
                                <a href="reports.php" class="btn btn-outline-primary d-block">
                                    <i class="fas fa-chart-bar me-2"></i> Generate Reports
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- My Courses -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5>My Courses</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($courses->num_rows > 0): ?>
                            <div class="row">
                                <?php
                                // Reset courses result pointer
                                $courses->data_seek(0);
                                while ($course = $courses->fetch_assoc()):
                                ?>
                                    <div class="col-md-6 col-lg-4 mb-3">
                                        <div class="card h-100">
                                            <div class="card-header">
                                                <h6 class="mb-0"><?php echo $course['course_code']; ?></h6>
                                            </div>
                                            <div class="card-body">
                                                <h5 class="card-title"><?php echo $course['course_title']; ?></h5>
                                                <p class="card-text">
                                                    <strong>Department:</strong> <?php echo $course['dept_name']; ?><br>
                                                    <strong>Level:</strong> <?php echo $course['level_name']; ?><br>
                                                    <strong>Semester:</strong> <?php echo $course['semester_name']; ?><br>
                                                    <strong>Students:</strong> <?php echo $courseStudentCounts[$course['course_id']] ?? 0; ?><br>
                                                    <strong>Attendance:</strong> <?php echo $courseAttendanceCounts[$course['course_id']] ?? 0; ?> students
                                                </p>
                                            </div>
                                            <div class="card-footer">
                                                <a href="course_details.php?id=<?php echo $course['course_id']; ?>" class="btn btn-sm btn-primary">View Details</a>
                                                <button type="button" class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#qrModal<?php echo $course['course_id']; ?>">
                                                    <i class="fas fa-qrcode"></i> QR Code
                                                </button>
                                                <?php if (!empty($course['qr_code']) && file_exists($course['qr_code'])): ?>
                                                <button type="button" class="btn btn-sm btn-warning regenerate-qr-btn" data-course-id="<?php echo $course['course_id']; ?>">
                                                    <i class="fas fa-sync-alt"></i> Regenerate
                                                </button>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- QR Code Modal -->
                                    <div class="modal fade" id="qrModal<?php echo $course['course_id']; ?>" tabindex="-1" aria-labelledby="qrModalLabel<?php echo $course['course_id']; ?>" aria-hidden="true">
                                        <div class="modal-dialog modal-dialog-centered">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="qrModalLabel<?php echo $course['course_id']; ?>">QR Code for <?php echo $course['course_code']; ?></h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body text-center">
                                                    <h6><?php echo $course['course_title']; ?></h6>
                                                    <div class="qr-code my-3">
                                                        <?php if (!empty($course['qr_code']) && file_exists($course['qr_code'])): ?>
                                                            <img src="<?php echo $course['qr_code']; ?>" alt="QR Code" class="img-fluid">
                                                        <?php else: ?>
                                                            <div id="qr-loading-<?php echo $course['course_id']; ?>" class="text-center">
                                                                <div class="spinner-border text-primary" role="status">
                                                                    <span class="visually-hidden">Loading...</span>
                                                                </div>
                                                                <p class="mt-2">Generating QR code...</p>
                                                            </div>
                                                            <div id="qr-container-<?php echo $course['course_id']; ?>" class="d-none">
                                                                <!-- QR code will be loaded here -->
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                    <p class="mb-0">Students can scan this QR code to mark their attendance for this course.</p>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                    <?php if (!empty($course['qr_code']) && file_exists($course['qr_code'])): ?>
                                                    <a href="<?php echo $course['qr_code']; ?>" download="<?php echo $course['course_code']; ?>_QR.png" class="btn btn-primary">
                                                        <i class="fas fa-download"></i> Download
                                                    </a>
                                                    <?php else: ?>
                                                    <a href="#" download="<?php echo $course['course_code']; ?>_QR.png" class="btn btn-primary" id="download-btn-<?php echo $course['course_id']; ?>" disabled>
                                                        <i class="fas fa-download"></i> Download
                                                    </a>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endwhile; ?>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <p>You don't have any courses assigned to you yet. Please contact the administrator.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Attendance -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Recent Attendance</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($recentAttendance->num_rows > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>Student</th>
                                            <th>Matric Number</th>
                                            <th>Course</th>
                                            <th>Date</th>
                                            <th>Time</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($attendance = $recentAttendance->fetch_assoc()): ?>
                                            <tr>
                                                <td><?php echo $attendance['student_name']; ?></td>
                                                <td><?php echo $attendance['matric_number']; ?></td>
                                                <td><?php echo $attendance['course_code']; ?> - <?php echo $attendance['course_title']; ?></td>
                                                <td><?php echo date('d M Y', strtotime($attendance['attendance_date'])); ?></td>
                                                <td><?php echo date('h:i A', strtotime($attendance['attendance_time'])); ?></td>
                                            </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-center mt-3">
                                <a href="attendance.php" class="btn btn-primary">View All Attendance</a>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">No attendance records found yet.</div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-3 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; <?php echo date('Y'); ?> Ogbonnaya Onu Polytechnic. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="../about.php" class="text-white me-3">About</a>
                    <a href="../support.php" class="text-white">Support</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/script.js"></script>

    <script>
        $(document).ready(function() {
            // Generate QR code when modal is shown
            $('.modal').on('shown.bs.modal', function (e) {
                const modalId = $(this).attr('id');
                const courseId = modalId.replace('qrModal', '');

                // Check if QR code needs to be generated
                if ($('#qr-loading-' + courseId).length > 0) {
                    generateCourseQR(courseId);
                }
            });

            // Function to generate course QR code
            function generateCourseQR(courseId, regenerate = false) {
                const loadingElement = $('#qr-loading-' + courseId);
                const containerElement = $('#qr-container-' + courseId);
                const modalBody = $('#qrModal' + courseId + ' .modal-body');

                // If regenerating, show loading state
                if (regenerate) {
                    // Create loading element if it doesn't exist
                    if (loadingElement.length === 0) {
                        const loadingHtml = `
                            <div id="qr-loading-${courseId}" class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2">Regenerating QR code...</p>
                            </div>
                        `;

                        // Hide existing QR code and show loading
                        modalBody.find('.qr-code img').hide();
                        modalBody.find('.qr-code').append(loadingHtml);
                    } else {
                        loadingElement.removeClass('d-none');
                        containerElement.addClass('d-none');
                    }
                }

                // Call the API to generate QR code
                $.ajax({
                    url: '../generate_qr.php',
                    type: 'GET',
                    data: {
                        id: courseId,
                        regenerate: regenerate ? 1 : 0
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            if (regenerate) {
                                // For regeneration, reload the page to show the new QR code
                                location.reload();
                            } else {
                                // Create image element
                                const img = $('<img>')
                                    .attr('src', response.qr_code)
                                    .attr('alt', 'QR Code')
                                    .addClass('img-fluid');

                                // Add image to container
                                containerElement.html(img);

                                // Hide loading, show container
                                loadingElement.addClass('d-none');
                                containerElement.removeClass('d-none');

                                // Update download button href
                                const downloadBtn = $('#download-btn-' + courseId);
                                if (downloadBtn.length > 0) {
                                    downloadBtn.attr('href', response.qr_code);
                                    downloadBtn.removeAttr('disabled');
                                }
                            }
                        } else {
                            // Show error message
                            loadingElement.html('<div class="alert alert-danger">' +
                                (response.error || 'Failed to generate QR code') + '</div>');
                        }
                    },
                    error: function() {
                        // Show error message
                        loadingElement.html('<div class="alert alert-danger">Error connecting to server</div>');
                    }
                });
            }

            // Handle regenerate button click
            $('.regenerate-qr-btn').on('click', function() {
                const courseId = $(this).data('course-id');

                // Confirm regeneration
                if (confirm('Are you sure you want to regenerate the QR code? Students will need to scan the new code.')) {
                    // Disable the button and show loading state
                    $(this).prop('disabled', true);
                    $(this).html('<i class="fas fa-spinner fa-spin"></i> Regenerating...');

                    // Call regenerate function
                    generateCourseQR(courseId, true);
                }
            });
        });
    </script>
</body>
</html>
