<?php
require_once '../config/functions.php';
require_once 'auth_check.php';

// Set content type to JSON
header('Content-Type: application/json');

try {
    // Get admin notification count
    $unreadCount = getAdminNotificationCount();
    
    // Get pending counts for additional info
    $pendingStudents = $conn->query("SELECT COUNT(*) as count FROM students WHERE status = 'pending'")->fetch_assoc()['count'];
    $pendingLecturers = $conn->query("SELECT COUNT(*) as count FROM lecturers WHERE status = 'pending'")->fetch_assoc()['count'];
    
    // Return JSON response
    echo json_encode([
        'success' => true,
        'unread_notifications' => $unreadCount,
        'pending_students' => $pendingStudents,
        'pending_lecturers' => $pendingLecturers,
        'total_pending' => $pendingStudents + $pendingLecturers
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
