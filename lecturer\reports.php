<?php
require_once '../config/functions.php';

// Check if lecturer is logged in
if (!isLecturerLoggedIn()) {
    redirect('../index.php');
}

// Get lecturer details
$lecturer_id = $_SESSION['lecturer_id'];
$lecturer = getLecturerDetails($lecturer_id);

// Get courses taught by this lecturer
$courses = getLecturerCourses($lecturer_id);

// Process form submission for generating reports
$report_data = [];
$show_report = false;
$course_details = null;
$date_range = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['generate_report'])) {
    $course_id = isset($_POST['course_id']) ? sanitize($_POST['course_id']) : '';
    $start_date = isset($_POST['start_date']) ? sanitize($_POST['start_date']) : '';
    $end_date = isset($_POST['end_date']) ? sanitize($_POST['end_date']) : '';
    $status_filter = isset($_POST['status_filter']) ? sanitize($_POST['status_filter']) : '';

    // Validate inputs
    if (empty($course_id)) {
        $error_message = 'Please select a course.';
    } else {
        // Get course details
        $stmt = $conn->prepare("
            SELECT c.*, d.dept_name, l.level_name, s.semester_name
            FROM courses c
            JOIN departments d ON c.dept_id = d.dept_id
            JOIN levels l ON c.level_id = l.level_id
            JOIN semesters s ON c.semester_id = s.semester_id
            WHERE c.course_id = ?
        ");
        $stmt->bind_param("i", $course_id);
        $stmt->execute();
        $course_details = $stmt->get_result()->fetch_assoc();

        // Build query for attendance records
        $query = "
            SELECT a.*, s.name, s.matric_number, cs.start_time, cs.end_time
            FROM attendance a
            JOIN students s ON a.student_id = s.student_id
            LEFT JOIN class_sessions cs ON a.session_id = cs.session_id
            WHERE a.course_id = $course_id
        ";

        // Build filter description
        $filter_info = [];

        // Add date range if provided
        if (!empty($start_date) && !empty($end_date)) {
            $query .= " AND a.attendance_date BETWEEN '$start_date' AND '$end_date'";
            $filter_info[] = "From $start_date to $end_date";
        }

        // Add status filter if provided
        if (!empty($status_filter)) {
            $query .= " AND a.status = '$status_filter'";
            $filter_info[] = "Status: " . ucfirst($status_filter);
        }

        $date_range = !empty($filter_info) ? implode(' | ', $filter_info) : '';

        $query .= " ORDER BY a.attendance_date DESC, a.attendance_time DESC";

        // Get attendance records
        $report_data = $conn->query($query);
        $show_report = true;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Attendance Reports - Ogbonnaya Onu Polytechnic</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <!-- DataTables Buttons CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.bootstrap5.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .report-header {
            margin-bottom: 20px;
        }
        .report-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        .report-subtitle {
            font-size: 1.1rem;
            color: #6c757d;
            margin-bottom: 0.25rem;
        }
        .report-date {
            font-style: italic;
            color: #6c757d;
        }
        .filter-card {
            margin-bottom: 20px;
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .card-header {
            padding: 0.75rem 1.25rem;
        }
        .card-header h5 {
            margin: 0;
            display: flex;
            align-items: center;
        }
        .card-header.bg-primary {
            background-color: #0d6efd !important;
        }
        .card-header.bg-success {
            background-color: #198754 !important;
        }
        .btn-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }
        .btn-success {
            background-color: #198754;
            border-color: #198754;
        }
        .input-group-text {
            background-color: #f8f9fa;
        }
        .table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        #exportExcelBtn {
            padding: 0.375rem 1rem;
        }
    </style>
</head>
<body>
    <!-- Include Navbar -->
    <?php include 'includes/navbar.php'; ?>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col-md-12">
                <h2>Attendance Reports</h2>
                <p class="text-muted">Generate and export attendance reports for your courses</p>
            </div>
        </div>

        <!-- Filter Options -->
        <div class="card filter-card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-filter me-2"></i> Filter Options</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <div class="row align-items-end">
                        <div class="col-md-3 mb-3">
                            <label for="course_id" class="form-label">Course</label>
                            <select class="form-select" id="course_id" name="course_id" required>
                                <option value="">-- Select Course --</option>
                                <?php
                                // Reset courses result pointer
                                if ($courses->num_rows > 0) {
                                    $courses->data_seek(0);
                                    while ($course = $courses->fetch_assoc()):
                                ?>
                                    <option value="<?php echo $course['course_id']; ?>" <?php echo (isset($_POST['course_id']) && $_POST['course_id'] == $course['course_id']) ? 'selected' : ''; ?>>
                                        <?php echo $course['course_code'] . ' - ' . $course['course_title']; ?>
                                    </option>
                                <?php
                                    endwhile;
                                }
                                ?>
                            </select>
                        </div>
                        <div class="col-md-2 mb-3">
                            <label for="start_date" class="form-label">Start Date</label>
                            <div class="input-group">
                                <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo isset($_POST['start_date']) ? $_POST['start_date'] : ''; ?>">
                                <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                            </div>
                        </div>
                        <div class="col-md-2 mb-3">
                            <label for="end_date" class="form-label">End Date</label>
                            <div class="input-group">
                                <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo isset($_POST['end_date']) ? $_POST['end_date'] : ''; ?>">
                                <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="status_filter" class="form-label">Status</label>
                            <div class="input-group">
                                <select class="form-select" id="status_filter" name="status_filter">
                                    <option value="">-- All Status --</option>
                                    <option value="present" <?php echo (isset($_POST['status_filter']) && $_POST['status_filter'] == 'present') ? 'selected' : ''; ?>>Present Only</option>
                                    <option value="absent" <?php echo (isset($_POST['status_filter']) && $_POST['status_filter'] == 'absent') ? 'selected' : ''; ?>>Absent Only</option>
                                </select>
                                <span class="input-group-text"><i class="fas fa-filter"></i></span>
                            </div>
                        </div>
                        <div class="col-md-2 mb-3">
                            <button type="submit" name="generate_report" class="btn btn-primary w-100">
                                <i class="fas fa-search me-2"></i> Generate Report
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Report Results -->
        <?php if ($show_report && $course_details): ?>
            <div class="card mt-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i> Report Results</h5>
                </div>
                <div class="card-body">
                    <div class="report-header mb-4">
                        <h4 class="report-title"><?php echo $course_details['course_code'] . ' - ' . $course_details['course_title']; ?></h4>
                        <p class="report-subtitle mb-1"><?php echo $course_details['dept_name'] . ' | ' . $course_details['level_name'] . ' | ' . $course_details['semester_name']; ?></p>
                        <?php if (!empty($date_range)): ?>
                            <p class="report-date mb-0"><strong>Filters Applied:</strong> <?php echo $date_range; ?></p>
                        <?php endif; ?>
                    </div>

                    <?php if ($report_data->num_rows > 0): ?>
                        <?php
                        // Calculate statistics
                        $total_records = $report_data->num_rows;
                        $present_count = 0;
                        $absent_count = 0;

                        // Reset pointer and count statuses
                        $report_data->data_seek(0);
                        while ($row = $report_data->fetch_assoc()) {
                            if ($row['status'] == 'present') {
                                $present_count++;
                            } else {
                                $absent_count++;
                            }
                        }
                        $report_data->data_seek(0); // Reset pointer for table display

                        $attendance_rate = $total_records > 0 ? round(($present_count / $total_records) * 100, 1) : 0;
                        ?>

                        <!-- Summary Statistics -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="fas fa-clipboard-list text-primary fa-2x mb-2"></i>
                                        <h4><?php echo $total_records; ?></h4>
                                        <p class="text-muted">Total Records</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="fas fa-check-circle text-success fa-2x mb-2"></i>
                                        <h4><?php echo $present_count; ?></h4>
                                        <p class="text-muted">Present</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="fas fa-times-circle text-danger fa-2x mb-2"></i>
                                        <h4><?php echo $absent_count; ?></h4>
                                        <p class="text-muted">Absent</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="fas fa-percentage text-info fa-2x mb-2"></i>
                                        <h4><?php echo $attendance_rate; ?>%</h4>
                                        <p class="text-muted">Attendance Rate</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="mb-0"><i class="fas fa-table me-2"></i>Detailed Attendance Records</h5>
                            <button id="exportExcelBtn" class="btn btn-success">
                                <i class="fas fa-file-excel me-2"></i> Export to Excel
                            </button>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="reportTable">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Time</th>
                                        <th>Matric Number</th>
                                        <th>Student Name</th>
                                        <th>Status</th>
                                        <th>Marked By</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while ($record = $report_data->fetch_assoc()): ?>
                                        <tr>
                                            <td><?php echo date('d M Y', strtotime($record['attendance_date'])); ?></td>
                                            <td><?php echo date('h:i A', strtotime($record['attendance_time'])); ?></td>
                                            <td><?php echo $record['matric_number']; ?></td>
                                            <td><?php echo $record['name']; ?></td>
                                            <td>
                                                <?php if ($record['status'] == 'present'): ?>
                                                    <span class="badge bg-success">Present</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">Absent</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                $marked_by = ucfirst($record['marked_by'] ?? 'student');
                                                echo $marked_by;
                                                ?>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> No attendance records found for the selected criteria.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-3 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; <?php echo date('Y'); ?> Ogbonnaya Onu Polytechnic. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="../about.php" class="text-white me-3">About</a>
                    <a href="../support.php" class="text-white">Support</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <!-- DataTables Buttons JS -->
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.print.min.js"></script>

    <script>
        $(document).ready(function() {
            // Initialize DataTable
            var reportTable = $('#reportTable').DataTable({
                paging: false,
                searching: false,
                info: false,
                ordering: true,
                autoWidth: false
            });

            // Handle Export to Excel button click
            $('#exportExcelBtn').on('click', function() {
                // Create a new Excel export button instance
                var excelBtn = new $.fn.dataTable.Buttons(reportTable, {
                    buttons: [
                        {
                            extend: 'excel',
                            text: 'Export to Excel',
                            title: function() {
                                var courseTitle = $('.report-title').text();
                                var courseInfo = $('.report-subtitle').text();
                                var dateRange = $('.report-date').text();
                                return 'Attendance Report - ' + courseTitle + ' - ' + courseInfo + ' - ' + dateRange;
                            },
                            filename: function() {
                                var courseCode = $('.report-title').text().split(' - ')[0].trim();
                                var date = new Date().toISOString().slice(0, 10);
                                return 'Attendance_Report_' + courseCode + '_' + date;
                            },
                            exportOptions: {
                                columns: ':visible'
                            },
                            customize: function(xlsx) {
                                // You can customize the Excel file here if needed
                            }
                        }
                    ]
                });

                // Trigger the Excel export
                excelBtn.buttons().trigger();
            });

            // Set min/max dates for date inputs
            $('#start_date').on('change', function() {
                $('#end_date').attr('min', $(this).val());
            });

            $('#end_date').on('change', function() {
                $('#start_date').attr('max', $(this).val());
            });

            // Format date inputs to show mm/dd/yyyy format
            $('.input-group .form-control[type="date"]').each(function() {
                $(this).on('change', function() {
                    if ($(this).val()) {
                        var date = new Date($(this).val());
                        var formattedDate = (date.getMonth() + 1) + '/' + date.getDate() + '/' + date.getFullYear();
                        $(this).attr('title', formattedDate);
                    }
                });
            });
        });
    </script>
</body>
</html>
