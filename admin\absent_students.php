<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once '../config/functions.php';
require_once 'auth_check.php';

// Get all courses for filtering
$courses = $conn->query("SELECT course_id, course_code, course_title FROM courses ORDER BY course_code");

// Default query to get all class sessions
$sessionsQuery = "
    SELECT cs.*, 
           c.course_code, c.course_title,
           l.name as lecturer_name, l.staff_code,
           d.dept_name, lev.level_name
    FROM class_schedules cs
    JOIN courses c ON cs.course_id = c.course_id
    JOIN lecturers l ON cs.lecturer_id = l.lecturer_id
    JOIN departments d ON c.dept_id = d.dept_id
    JOIN levels lev ON c.level_id = lev.level_id
    WHERE cs.status = 'ended'
    ORDER BY cs.class_date DESC, cs.start_time DESC
";

$sessions = $conn->query($sessionsQuery);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Absent Students Report - Ogbonnaya Onu Polytechnic</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <!-- DataTables Buttons CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.bootstrap5.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }

        .card {
            border-radius: 0.5rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .card-header {
            padding: 0.75rem 1.25rem;
        }

        .card-header h5 {
            margin: 0;
            display: flex;
            align-items: center;
        }

        .card-header.bg-primary {
            background-color: #0d6efd !important;
        }

        .card-header.bg-danger {
            background-color: #dc3545 !important;
        }

        .btn-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }

        .btn-success {
            background-color: #198754;
            border-color: #198754;
        }

        .input-group-text {
            background-color: #f8f9fa;
        }

        .table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }

        #exportExcelBtn {
            padding: 0.375rem 1rem;
        }

        .absent-badge {
            background-color: #dc3545;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="../images/logo.jpg" alt="Logo" width="30" height="30" class="d-inline-block align-text-top me-2 rounded-circle">
                Admin Dashboard
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Students
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="students.php">All Students</a></li>
                            <li><a class="dropdown-item" href="pending_students.php">Pending Approvals</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Lecturers
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="lecturers.php">All Lecturers</a></li>
                            <li><a class="dropdown-item" href="pending_lecturers.php">Pending Approvals</a></li>
                            <li><a class="dropdown-item" href="assign_courses.php">Assign Courses</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Courses
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="courses.php">All Courses</a></li>
                            <li><a class="dropdown-item" href="add_course.php">Add New Course</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Departments
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="departments.php">All Departments</a></li>
                            <li><a class="dropdown-item" href="add_department.php">Add New Department</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Reports
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="reports.php">Attendance Reports</a></li>
                            <li><a class="dropdown-item active" href="absent_students.php">Absent Students</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="password_reset_requests.php">Password Resets</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-1"></i> <?php echo $_SESSION['username']; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profile</a></li>
                            <li><a class="dropdown-item" href="change_password.php">Change Password</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col-md-12">
                <h2>Absent Students Report</h2>
                <p class="text-muted">View students who missed classes</p>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-filter me-2"></i> Filter Options</h5>
            </div>
            <div class="card-body">
                <form id="filterForm">
                    <div class="row align-items-end">
                        <div class="col-md-4 mb-3">
                            <label for="courseFilter" class="form-label">Course</label>
                            <select class="form-select" id="courseFilter" name="courseFilter">
                                <option value="">-- Select Course --</option>
                                <?php
                                while ($course = $courses->fetch_assoc()) {
                                    echo "<option value='{$course['course_id']}'>{$course['course_code']} - {$course['course_title']}</option>";
                                }
                                ?>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="startDate" class="form-label">Start Date</label>
                            <div class="input-group">
                                <input type="date" class="form-control" id="startDate" name="startDate">
                                <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="endDate" class="form-label">End Date</label>
                            <div class="input-group">
                                <input type="date" class="form-control" id="endDate" name="endDate">
                                <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                            </div>
                        </div>
                        <div class="col-md-2 mb-3">
                            <button type="button" id="viewReportBtn" class="btn btn-primary w-100">
                                <i class="fas fa-search me-2"></i> Generate Report
                            </button>
                        </div>
                    </div>

                    <!-- Advanced filters (collapsed by default) -->
                    <div class="collapse" id="advancedFilters">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="departmentFilter" class="form-label">Department</label>
                                <select class="form-select" id="departmentFilter" name="departmentFilter">
                                    <option value="">All Departments</option>
                                    <?php
                                    $departments = $conn->query("SELECT dept_id, dept_name FROM departments ORDER BY dept_name");
                                    while ($department = $departments->fetch_assoc()) {
                                        echo "<option value='{$department['dept_id']}'>{$department['dept_name']}</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="levelFilter" class="form-label">Level</label>
                                <select class="form-select" id="levelFilter" name="levelFilter">
                                    <option value="">All Levels</option>
                                    <?php
                                    $levels = $conn->query("SELECT level_id, level_name FROM levels ORDER BY level_name");
                                    while ($level = $levels->fetch_assoc()) {
                                        echo "<option value='{$level['level_id']}'>{$level['level_name']}</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="lecturerFilter" class="form-label">Lecturer</label>
                                <select class="form-select" id="lecturerFilter" name="lecturerFilter">
                                    <option value="">All Lecturers</option>
                                    <?php
                                    $lecturers = $conn->query("SELECT lecturer_id, name, staff_code FROM lecturers ORDER BY name");
                                    while ($lecturer = $lecturers->fetch_assoc()) {
                                        echo "<option value='{$lecturer['lecturer_id']}'>{$lecturer['staff_code']} - {$lecturer['name']}</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="d-flex justify-content-between">
                                <button class="btn btn-link text-decoration-none p-0" type="button" data-bs-toggle="collapse" data-bs-target="#advancedFilters">
                                    <i class="fas fa-sliders-h me-1"></i> Advanced Filters
                                </button>
                                <button type="button" id="resetFilterBtn" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-undo me-1"></i> Reset Filters
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="card">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0"><i class="fas fa-user-times me-2"></i> Absent Students</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-end mb-3">
                    <button id="exportExcelBtn" class="btn btn-success">
                        <i class="fas fa-file-excel me-2"></i> Export to Excel
                    </button>
                </div>

                <?php if ($sessions->num_rows > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="absentStudentsTable">
                            <thead>
                                <tr>
                                    <th>Class Date</th>
                                    <th>Course</th>
                                    <th>Lecturer</th>
                                    <th>Department</th>
                                    <th>Level</th>
                                    <th>Absent Students</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($session = $sessions->fetch_assoc()): 
                                    // Get all students enrolled in this course
                                    $enrolled_query = $conn->prepare("
                                        SELECT s.student_id, s.name, s.matric_number, s.phone
                                        FROM students s
                                        JOIN student_courses sc ON s.student_id = sc.student_id
                                        WHERE sc.course_id = ? AND s.status = 'active'
                                    ");
                                    $enrolled_query->bind_param("i", $session['course_id']);
                                    $enrolled_query->execute();
                                    $enrolled_students = $enrolled_query->get_result();
                                    
                                    // Get students who attended this session
                                    $attended_query = $conn->prepare("
                                        SELECT DISTINCT student_id
                                        FROM attendance
                                        WHERE course_id = ? AND attendance_date = ?
                                    ");
                                    $attended_query->bind_param("is", $session['course_id'], $session['class_date']);
                                    $attended_query->execute();
                                    $attended_result = $attended_query->get_result();
                                    
                                    // Create an array of student IDs who attended
                                    $attended_ids = [];
                                    while ($attended = $attended_result->fetch_assoc()) {
                                        $attended_ids[] = $attended['student_id'];
                                    }
                                    
                                    // Find absent students
                                    $absent_students = [];
                                    while ($student = $enrolled_students->fetch_assoc()) {
                                        if (!in_array($student['student_id'], $attended_ids)) {
                                            $absent_students[] = $student;
                                        }
                                    }
                                    
                                    // Only show sessions with absent students
                                    if (count($absent_students) > 0):
                                ?>
                                    <tr>
                                        <td><?php echo date('d M Y', strtotime($session['class_date'])) . '<br><small>' . 
                                                 date('h:i A', strtotime($session['start_time'])) . ' - ' . 
                                                 date('h:i A', strtotime($session['end_time'])) . '</small>'; ?></td>
                                        <td><?php echo $session['course_code'] . '<br><small>' . $session['course_title'] . '</small>'; ?></td>
                                        <td><?php echo $session['lecturer_name'] . '<br><small>' . $session['staff_code'] . '</small>'; ?></td>
                                        <td><?php echo $session['dept_name']; ?></td>
                                        <td><?php echo $session['level_name']; ?></td>
                                        <td>
                                            <div class="absent-students-list">
                                                <span class="absent-badge"><?php echo count($absent_students); ?> absent</span>
                                                <ul class="list-unstyled mt-2">
                                                    <?php foreach ($absent_students as $absent): ?>
                                                        <li>
                                                            <strong><?php echo $absent['matric_number']; ?></strong> - 
                                                            <?php echo $absent['name']; ?>
                                                            <small class="text-muted d-block"><?php echo $absent['phone']; ?></small>
                                                        </li>
                                                    <?php endforeach; ?>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> No class sessions found.
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-3 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; <?php echo date('Y'); ?> Ogbonnaya Onu Polytechnic. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="../about.php" class="text-white me-3">About</a>
                    <a href="../support.php" class="text-white">Support</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <!-- DataTables Buttons JS -->
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.print.min.js"></script>
    <script>
        // Global variable for the DataTable
        var dataTable;

        // Format date for input fields
        function formatDateForInput(date) {
            var d = new Date(date),
                month = '' + (d.getMonth() + 1),
                day = '' + d.getDate(),
                year = d.getFullYear();

            if (month.length < 2) month = '0' + month;
            if (day.length < 2) day = '0' + day;

            return [year, month, day].join('-');
        }

        $(document).ready(function() {
            // Initialize DataTable
            dataTable = $('#absentStudentsTable').DataTable({
                paging: true,
                searching: true,
                info: true,
                ordering: true,
                autoWidth: false,
                pageLength: 25,
                lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
                columnDefs: [
                    { className: "text-center", targets: [0, 3, 4] }
                ],
                // After DataTable is initialized
                initComplete: function() {
                    // Show initial record count
                    var totalCount = dataTable.rows().count();
                    $('.card-header h5').text('Absent Students (' + totalCount + ' records)');
                }
            });

            // Set default date values (last 30 days)
            var today = new Date();
            var thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(today.getDate() - 30);

            // Set default values
            $('#startDate').val(formatDateForInput(thirtyDaysAgo));
            $('#endDate').val(formatDateForInput(today));

            // VIEW REPORT BUTTON - Apply filters
            $('#viewReportBtn').on('click', function() {
                console.log('View Report button clicked');

                var startDate = $('#startDate').val();
                var endDate = $('#endDate').val();
                var departmentId = $('#departmentFilter').val();
                var levelId = $('#levelFilter').val();
                var courseId = $('#courseFilter').val();
                var lecturerId = $('#lecturerFilter').val();

                console.log('Applying filters:', { startDate, endDate, departmentId, levelId, courseId, lecturerId });

                // Clear any existing search/filter
                dataTable.search('').columns().search('').draw();

                // Remove any existing custom filters
                $.fn.dataTable.ext.search.pop();

                // Add custom filter function
                $.fn.dataTable.ext.search.push(function(settings, data, dataIndex) {
                    var rowDate = new Date(data[0].split('<br>')[0]);
                    var filterStartDate = startDate ? new Date(startDate) : null;
                    var filterEndDate = endDate ? new Date(endDate) : null;
                    
                    // Date filter
                    if (filterStartDate && rowDate < filterStartDate) return false;
                    if (filterEndDate) {
                        // Set end date to end of day
                        filterEndDate.setHours(23, 59, 59, 999);
                        if (rowDate > filterEndDate) return false;
                    }
                    
                    // Course filter
                    if (courseId && !data[1].includes(courseId)) return false;
                    
                    // Department filter
                    if (departmentId && !data[3].includes(departmentId)) return false;
                    
                    // Level filter
                    if (levelId && !data[4].includes(levelId)) return false;
                    
                    // Lecturer filter
                    if (lecturerId && !data[2].includes(lecturerId)) return false;
                    
                    return true;
                });

                // Apply all filters at once
                dataTable.draw();

                // Show count of filtered records
                var filteredCount = dataTable.rows({search:'applied'}).count();
                var totalCount = dataTable.rows().count();

                // Update the card header to show the count
                $('.card-header h5').text('Absent Students (' + filteredCount + ' of ' + totalCount + ' records)');

                // Show notification
                var notification = $('<div class="alert alert-success alert-dismissible fade show" role="alert">' +
                    'Report generated successfully. Showing ' + filteredCount + ' of ' + totalCount + ' records.' +
                    '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
                    '</div>');

                // Add the notification at the top of the table
                $('.card-body').prepend(notification);

                // Auto-dismiss after 3 seconds
                setTimeout(function() {
                    notification.alert('close');
                }, 3000);
            });

            // RESET FILTERS BUTTON
            $('#resetFilterBtn').on('click', function() {
                // Reset form fields
                $('#filterForm')[0].reset();
                
                // Reset date fields to default values
                $('#startDate').val(formatDateForInput(thirtyDaysAgo));
                $('#endDate').val(formatDateForInput(today));
                
                // Clear DataTable filters
                dataTable.search('').columns().search('').draw();
                
                // Remove any custom filters
                $.fn.dataTable.ext.search.pop();
                
                // Redraw the table
                dataTable.draw();
                
                // Update the count
                var totalCount = dataTable.rows().count();
                $('.card-header h5').text('Absent Students (' + totalCount + ' records)');
            });

            // EXPORT TO EXCEL BUTTON
            $('#exportExcelBtn').on('click', function() {
                // Create a new Excel export button and trigger it
                var excelButton = new $.fn.dataTable.Buttons(dataTable, {
                    buttons: [
                        {
                            extend: 'excel',
                            text: 'Export to Excel',
                            title: 'Absent Students Report - ' + new Date().toLocaleDateString(),
                            exportOptions: {
                                columns: [0, 1, 2, 3, 4, 5],
                                format: {
                                    body: function(data, row, column) {
                                        // Clean HTML from export data
                                        if (column === 0 || column === 1 || column === 2) {
                                            return data.replace(/<br\s*\/?>/gi, ' - ')
                                                      .replace(/<small>(.*?)<\/small>/gi, '($1)')
                                                      .replace(/<(?:.|\n)*?>/gm, '');
                                        }
                                        if (column === 5) {
                                            // Extract student names from the absent students list
                                            var tempDiv = document.createElement('div');
                                            tempDiv.innerHTML = data;
                                            var students = [];
                                            var listItems = tempDiv.querySelectorAll('li');
                                            listItems.forEach(function(item) {
                                                students.push(item.textContent.trim().replace(/\s+/g, ' '));
                                            });
                                            return students.join('; ');
                                        }
                                        return data;
                                    }
                                }
                            },
                            className: 'hidden-button'
                        }
                    ]
                }).container().appendTo($('body'));
                
                // Trigger the Excel export
                $('.hidden-button .buttons-excel').click();
                
                // Remove the temporary button
                $('.hidden-button').remove();
            });
        });
    </script>
</body>
</html>
