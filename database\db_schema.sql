-- Database schema for Ogbonnaya Onu Polytechnic Attendance System

-- Create database
CREATE DATABASE IF NOT EXISTS attendance_system;
USE attendance_system;

-- Admin table
CREATE TABLE IF NOT EXISTS admin (
    admin_id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Departments table
CREATE TABLE IF NOT EXISTS departments (
    dept_id INT AUTO_INCREMENT PRIMARY KEY,
    dept_name VARCHAR(100) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Levels table
CREATE TABLE IF NOT EXISTS levels (
    level_id INT AUTO_INCREMENT PRIMARY KEY,
    level_name ENUM('ND1', 'ND2', 'HND1', 'HND2') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Semesters table
CREATE TABLE IF NOT EXISTS semesters (
    semester_id INT AUTO_INCREMENT PRIMARY KEY,
    semester_name ENUM('First Semester', 'Second Semester') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Lecturers table
CREATE TABLE IF NOT EXISTS lecturers (
    lecturer_id INT AUTO_INCREMENT PRIMARY KEY,
    staff_code VARCHAR(20) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    password VARCHAR(255) NOT NULL,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Courses table
CREATE TABLE IF NOT EXISTS courses (
    course_id INT AUTO_INCREMENT PRIMARY KEY,
    course_code VARCHAR(20) NOT NULL UNIQUE,
    course_title VARCHAR(100) NOT NULL,
    dept_id INT NOT NULL,
    level_id INT NOT NULL,
    semester_id INT NOT NULL,
    qr_code VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (dept_id) REFERENCES departments(dept_id),
    FOREIGN KEY (level_id) REFERENCES levels(level_id),
    FOREIGN KEY (semester_id) REFERENCES semesters(semester_id)
);

-- Lecturer-Course assignments
CREATE TABLE IF NOT EXISTS lecturer_courses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    lecturer_id INT NOT NULL,
    course_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (lecturer_id) REFERENCES lecturers(lecturer_id),
    FOREIGN KEY (course_id) REFERENCES courses(course_id),
    UNIQUE KEY (lecturer_id, course_id)
);

-- Students table
CREATE TABLE IF NOT EXISTS students (
    student_id INT AUTO_INCREMENT PRIMARY KEY,
    matric_number VARCHAR(20) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    password VARCHAR(255) NOT NULL,
    dept_id INT NOT NULL,
    level_id INT NOT NULL,
    semester_id INT NOT NULL,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (dept_id) REFERENCES departments(dept_id),
    FOREIGN KEY (level_id) REFERENCES levels(level_id),
    FOREIGN KEY (semester_id) REFERENCES semesters(semester_id)
);

-- Attendance records table
CREATE TABLE IF NOT EXISTS attendance (
    attendance_id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    course_id INT NOT NULL,
    attendance_date DATE NOT NULL,
    attendance_time TIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(student_id),
    FOREIGN KEY (course_id) REFERENCES courses(course_id),
    UNIQUE KEY (student_id, course_id, attendance_date)
);

-- Security Questions Tables
CREATE TABLE IF NOT EXISTS security_questions (
    question_id INT AUTO_INCREMENT PRIMARY KEY,
    question_text VARCHAR(255) NOT NULL,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS student_security_answers (
    answer_id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    question_id INT NOT NULL,
    answer_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(student_id) ON DELETE CASCADE,
    FOREIGN KEY (question_id) REFERENCES security_questions(question_id),
    UNIQUE KEY unique_student_question (student_id, question_id)
);

CREATE TABLE IF NOT EXISTS lecturer_security_answers (
    answer_id INT AUTO_INCREMENT PRIMARY KEY,
    lecturer_id INT NOT NULL,
    question_id INT NOT NULL,
    answer_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (lecturer_id) REFERENCES lecturers(lecturer_id) ON DELETE CASCADE,
    FOREIGN KEY (question_id) REFERENCES security_questions(question_id),
    UNIQUE KEY unique_lecturer_question (lecturer_id, question_id)
);

-- Insert default admin (username: admin, password: admin@2023)
INSERT INTO admin (username, password, email) VALUES ('admin', '$2y$10$Ot/QvXLgBQrCMp3zgP0Ugu9RHOFg9wKyQzEjgQiVjZQnT0cUqM.Hy', '<EMAIL>');

-- Insert essential academic structure data only
INSERT INTO levels (level_name) VALUES ('ND1'), ('ND2'), ('HND1'), ('HND2');
INSERT INTO semesters (semester_name) VALUES ('First Semester'), ('Second Semester');

-- Insert default security questions
INSERT INTO security_questions (question_text) VALUES
('What was the name of your first pet?'),
('What is your mother\'s maiden name?'),
('What was the name of your first school?'),
('What is the name of the city where you were born?'),
('What was your childhood nickname?'),
('What is the name of your favorite childhood friend?'),
('What was the make of your first car?'),
('What is your father\'s middle name?'),
('What was the name of the street you grew up on?'),
('What is your favorite book?'),
('What was the name of your first employer?'),
('What is your favorite movie?'),
('What was your favorite food as a child?'),
('What is the name of your favorite teacher?'),
('What was the name of your first stuffed animal?');
