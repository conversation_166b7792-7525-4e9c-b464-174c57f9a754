<?php
require_once '../config/functions.php';

// Check if lecturer is logged in
if (!isLecturerLoggedIn()) {
    redirect('../index.php');
}

// Get lecturer details
$lecturer_id = $_SESSION['lecturer_id'];
$lecturer = getLecturerDetails($lecturer_id);

// Process form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['start_class'])) {
        $course_id = sanitize($_POST['course_id']);

        // Start class session
        $result = startClassSession($course_id, $lecturer_id);

        if ($result['success']) {
            $success_message = 'Class session started successfully. Students have been notified.';
            // Redirect to show QR code immediately
            $session_id = $result['session_id'];
            header("Location: start_class.php?show_qr=" . $session_id);
            exit();
        } else {
            $error_message = $result['message'];
        }
    } elseif (isset($_POST['end_class'])) {
        $session_id = sanitize($_POST['session_id']);

        // End class session
        $result = endClassSession($session_id);

        if ($result['success']) {
            $success_message = 'Class session ended successfully.';
        } else {
            $error_message = $result['message'];
        }
    }
}

// Get active class sessions for this lecturer
$active_sessions = getActiveClassSessionsForLecturer($lecturer_id);

// Get courses for this lecturer
$courses = getLecturerCourses($lecturer_id);

// Check if we need to show QR code immediately
$show_qr_session = null;
if (isset($_GET['show_qr'])) {
    $session_id = sanitize($_GET['show_qr']);
    // Get session details including QR code
    $stmt = $conn->prepare("
        SELECT cs.*, c.course_code, c.course_title, c.qr_code
        FROM class_sessions cs
        JOIN courses c ON cs.course_id = c.course_id
        WHERE cs.session_id = ? AND cs.lecturer_id = ? AND cs.status = 'active'
    ");
    $stmt->bind_param("ii", $session_id, $lecturer_id);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($result->num_rows > 0) {
        $show_qr_session = $result->fetch_assoc();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Start Class - Ogbonnaya Onu Polytechnic</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="../images/logo.jpg" alt="Logo" width="30" height="30" class="d-inline-block align-text-top me-2 rounded-circle">
                Lecturer Dashboard
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">My Courses</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="start_class.php">Start Class</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="attendance.php">Attendance Records</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-1"></i> <?php echo $lecturer['name']; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profile</a></li>
                            <li><a class="dropdown-item" href="change_password.php">Change Password</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col-md-12">
                <h2>Start Class Session</h2>
                <p class="text-muted">Start a class session to allow students to mark their attendance</p>
            </div>
        </div>

        <?php if (!empty($success_message)): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i> <?php echo $success_message; ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($error_message)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i> <?php echo $error_message; ?>
            </div>
        <?php endif; ?>

        <!-- QR Code Display for Active Session -->
        <?php if ($show_qr_session): ?>
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="card border-success">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-qrcode me-2"></i>
                                QR Code for <?php echo $show_qr_session['course_code']; ?> - <?php echo $show_qr_session['course_title']; ?>
                            </h5>
                        </div>
                        <div class="card-body text-center">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Class is now active!</strong> Students can scan this QR code to mark their attendance.
                            </div>
                            <div id="qr-display-<?php echo $show_qr_session['session_id']; ?>" class="mb-3">
                                <?php if (!empty($show_qr_session['qr_code']) && file_exists('../' . $show_qr_session['qr_code'])): ?>
                                    <img src="../<?php echo $show_qr_session['qr_code']; ?>" alt="QR Code" class="img-fluid" style="max-width: 300px;">
                                <?php else: ?>
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading QR Code...</span>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="d-flex justify-content-center gap-3">
                                <button class="btn btn-primary" onclick="downloadQR(<?php echo $show_qr_session['session_id']; ?>)">
                                    <i class="fas fa-download me-2"></i> Download QR Code
                                </button>
                                <form method="POST" action="" class="d-inline">
                                    <input type="hidden" name="session_id" value="<?php echo $show_qr_session['session_id']; ?>">
                                    <button type="submit" name="end_class" class="btn btn-danger">
                                        <i class="fas fa-stop-circle me-2"></i> End Class
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <script>
                // Auto-generate QR code when page loads (only if not already displayed)
                $(document).ready(function() {
                    <?php if (empty($show_qr_session['qr_code']) || !file_exists('../' . $show_qr_session['qr_code'])): ?>
                        generateSessionQR(<?php echo $show_qr_session['session_id']; ?>);
                    <?php else: ?>
                        // QR code already displayed, enable download button
                        window['qr_path_<?php echo $show_qr_session['session_id']; ?>'] = '<?php echo $show_qr_session['qr_code']; ?>';
                    <?php endif; ?>
                });
            </script>
        <?php endif; ?>

        <!-- Active Class Sessions -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-chalkboard-teacher me-2"></i> Active Class Sessions</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($active_sessions->num_rows > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>Course Code</th>
                                            <th>Course Title</th>
                                            <th>Started At</th>
                                            <th>Duration</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($session = $active_sessions->fetch_assoc()): ?>
                                            <tr>
                                                <td><?php echo $session['course_code']; ?></td>
                                                <td><?php echo $session['course_title']; ?></td>
                                                <td><?php echo date('d M Y, h:i A', strtotime($session['start_time'])); ?></td>
                                                <td>
                                                    <?php
                                                    $start_time = new DateTime($session['start_time']);
                                                    $current_time = new DateTime();
                                                    $interval = $start_time->diff($current_time);

                                                    if ($interval->h > 0) {
                                                        echo $interval->format('%h hr %i min');
                                                    } else {
                                                        echo $interval->format('%i min');
                                                    }
                                                    ?>
                                                </td>
                                                <td>
                                                    <div class="d-flex gap-2">
                                                        <button type="button" class="btn btn-primary btn-sm view-qr" data-bs-toggle="modal" data-bs-target="#qrModal<?php echo $session['session_id']; ?>">
                                                            <i class="fas fa-qrcode me-1"></i> View QR
                                                        </button>
                                                        <form method="POST" action="">
                                                            <input type="hidden" name="session_id" value="<?php echo $session['session_id']; ?>">
                                                            <button type="submit" name="end_class" class="btn btn-danger btn-sm">
                                                                <i class="fas fa-stop-circle me-1"></i> End Class
                                                            </button>
                                                        </form>
                                                    </div>

                                                    <!-- QR Code Modal -->
                                                    <div class="modal fade" id="qrModal<?php echo $session['session_id']; ?>" tabindex="-1" aria-labelledby="qrModalLabel<?php echo $session['session_id']; ?>" aria-hidden="true">
                                                        <div class="modal-dialog modal-dialog-centered">
                                                            <div class="modal-content">
                                                                <div class="modal-header">
                                                                    <h5 class="modal-title" id="qrModalLabel<?php echo $session['session_id']; ?>">Class QR Code: <?php echo $session['course_code']; ?></h5>
                                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                </div>
                                                                <div class="modal-body text-center">
                                                                    <h6><?php echo $session['course_title']; ?></h6>
                                                                    <p class="text-muted">Started: <?php echo date('d M Y, h:i A', strtotime($session['start_time'])); ?></p>
                                                                    <div class="qr-code my-3" id="qr-container-<?php echo $session['session_id']; ?>">
                                                                        <div class="spinner-border text-primary" role="status">
                                                                            <span class="visually-hidden">Loading...</span>
                                                                        </div>
                                                                        <p class="mt-2">Generating QR code...</p>
                                                                    </div>
                                                                    <p>Students can scan this QR code to mark their attendance for this class session.</p>
                                                                </div>
                                                                <div class="modal-footer">
                                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                                    <button type="button" class="btn btn-primary download-qr" data-session-id="<?php echo $session['session_id']; ?>" disabled>
                                                                        <i class="fas fa-download"></i> Download QR Code
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> No active class sessions found. Start a new class session below.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Start New Class Session -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-play-circle me-2"></i> Start New Class Session</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($courses->num_rows > 0): ?>
                            <form method="POST" action="">
                                <div class="mb-3">
                                    <label for="course_id" class="form-label">Select Course</label>
                                    <select class="form-select" id="course_id" name="course_id" required>
                                        <option value="">-- Select Course --</option>
                                        <?php while ($course = $courses->fetch_assoc()): ?>
                                            <option value="<?php echo $course['course_id']; ?>">
                                                <?php echo $course['course_code'] . ' - ' . $course['course_title'] . ' (' . $course['level_name'] . ', ' . $course['semester_name'] . ')'; ?>
                                            </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                                <div class="d-grid">
                                    <button type="submit" name="start_class" class="btn btn-success">
                                        <i class="fas fa-play-circle me-2"></i> Start Class Session
                                    </button>
                                </div>
                            </form>
                        <?php else: ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i> No courses found for you. Please contact the administrator to assign courses to you.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-3 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; <?php echo date('Y'); ?> Ogbonnaya Onu Polytechnic. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="../about.php" class="text-white me-3">About</a>
                    <a href="../contact.php" class="text-white">Contact</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/script.js"></script>

    <script>
        $(document).ready(function() {
            // Generate QR code when modal is shown
            $('.modal').on('shown.bs.modal', function (e) {
                const modalId = $(this).attr('id');
                const sessionId = modalId.replace('qrModal', '');
                generateSessionQR(sessionId);
            });

            // Function to generate session QR code
            function generateSessionQR(sessionId) {
                // Try both container types (modal and direct display)
                const modalContainer = $('#qr-container-' + sessionId);
                const displayContainer = $('#qr-display-' + sessionId);
                const container = modalContainer.length > 0 ? modalContainer : displayContainer;

                $.ajax({
                    url: '../generate_session_qr.php',
                    type: 'GET',
                    data: { session_id: sessionId },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            // Display the QR code
                            container.html('<img src="../' + response.qr_code + '" alt="QR Code" class="img-fluid" style="max-width: 300px;">');

                            // Enable download button
                            $('.download-qr[data-session-id="' + sessionId + '"]').prop('disabled', false);
                            $('.download-qr[data-session-id="' + sessionId + '"]').attr('onclick', 'window.open("../' + response.qr_code + '", "_blank")');

                            // Store QR path for download function
                            window['qr_path_' + sessionId] = response.qr_code;
                        } else {
                            container.html('<div class="alert alert-danger">Error generating QR code: ' + (response.error || 'Unknown error') + '</div>');
                        }
                    },
                    error: function() {
                        container.html('<div class="alert alert-danger">Error connecting to server. Please try again.</div>');
                    }
                });
            }

            // Global function for downloading QR code
            window.downloadQR = function(sessionId) {
                const qrPath = window['qr_path_' + sessionId];
                if (qrPath) {
                    window.open('../' + qrPath, '_blank');
                } else {
                    alert('QR code not ready yet. Please wait a moment and try again.');
                }
            };
        });
    </script>
</body>
</html>
