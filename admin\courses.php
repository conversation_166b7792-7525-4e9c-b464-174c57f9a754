<?php
require_once '../config/functions.php';
require_once 'auth_check.php';

// Get all courses with department, level, and semester names
$courses = $conn->query("
    SELECT c.*, d.dept_name, l.level_name, s.semester_name
    FROM courses c
    JOIN departments d ON c.dept_id = d.dept_id
    JOIN levels l ON c.level_id = l.level_id
    JOIN semesters s ON c.semester_id = s.semester_id
    ORDER BY c.course_code
");

// Process delete course if requested
$message = '';
if (isset($_GET['delete']) && !empty($_GET['delete'])) {
    $course_id = sanitize($_GET['delete']);
    $force_delete = isset($_GET['force']) && $_GET['force'] == '1';

    // Check if course exists
    $stmt = $conn->prepare("SELECT course_id FROM courses WHERE course_id = ?");
    $stmt->bind_param("i", $course_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        // Check if course has related data that would prevent deletion
        $related_data = [];

        // Check for lecturer assignments
        $lecturer_check = $conn->prepare("SELECT COUNT(*) as count FROM lecturer_courses WHERE course_id = ?");
        $lecturer_check->bind_param("i", $course_id);
        $lecturer_check->execute();
        $lecturer_count = $lecturer_check->get_result()->fetch_assoc()['count'];
        if ($lecturer_count > 0) {
            $related_data[] = "$lecturer_count lecturer assignment(s)";
        }

        // Check for class sessions
        $session_check = $conn->prepare("SELECT COUNT(*) as count FROM class_sessions WHERE course_id = ?");
        $session_check->bind_param("i", $course_id);
        $session_check->execute();
        $session_count = $session_check->get_result()->fetch_assoc()['count'];
        if ($session_count > 0) {
            $related_data[] = "$session_count class session(s)";
        }

        // Check for attendance records
        $attendance_check = $conn->prepare("SELECT COUNT(*) as count FROM attendance WHERE course_id = ?");
        $attendance_check->bind_param("i", $course_id);
        $attendance_check->execute();
        $attendance_count = $attendance_check->get_result()->fetch_assoc()['count'];
        if ($attendance_count > 0) {
            $related_data[] = "$attendance_count attendance record(s)";
        }

        if (!empty($related_data) && !$force_delete) {
            $related_list = implode(", ", $related_data);
            $message = displayError("Cannot delete course. It has related data: $related_list. <br><br>
                <strong>Warning:</strong> If you force delete, all related data will be permanently removed.<br>
                <a href='courses.php?delete=$course_id&force=1' class='btn btn-danger btn-sm' onclick='return confirm(\"Are you absolutely sure? This will permanently delete the course and ALL related data including lecturer assignments, class sessions, and attendance records. This action cannot be undone!\")'>
                    <i class='fas fa-exclamation-triangle'></i> Force Delete Course & All Related Data
                </a>");
        } else if (!empty($related_data) && $force_delete) {
            // Force delete - remove all related data first
            $conn->begin_transaction();
            try {
                // Delete attendance records
                $conn->prepare("DELETE FROM attendance WHERE course_id = ?")->execute([$course_id]);

                // Delete class sessions
                $conn->prepare("DELETE FROM class_sessions WHERE course_id = ?")->execute([$course_id]);

                // Delete lecturer assignments
                $conn->prepare("DELETE FROM lecturer_courses WHERE course_id = ?")->execute([$course_id]);

                // Delete the course
                $stmt = $conn->prepare("DELETE FROM courses WHERE course_id = ?");
                $stmt->bind_param("i", $course_id);
                $stmt->execute();

                $conn->commit();
                $message = displaySuccess("Course and all related data deleted successfully!");

                // Refresh courses list
                $courses = $conn->query("
                    SELECT c.*, d.dept_name, l.level_name, s.semester_name
                    FROM courses c
                    JOIN departments d ON c.dept_id = d.dept_id
                    JOIN levels l ON c.level_id = l.level_id
                    JOIN semesters s ON c.semester_id = s.semester_id
                    ORDER BY c.course_code
                ");
            } catch (Exception $e) {
                $conn->rollback();
                $message = displayError("Error force deleting course: " . $e->getMessage());
            }
        } else {
            // Safe to delete course
            $stmt = $conn->prepare("DELETE FROM courses WHERE course_id = ?");
            $stmt->bind_param("i", $course_id);

            if ($stmt->execute()) {
                $message = displaySuccess("Course deleted successfully!");
                // Refresh courses list
                $courses = $conn->query("
                    SELECT c.*, d.dept_name, l.level_name, s.semester_name
                    FROM courses c
                    JOIN departments d ON c.dept_id = d.dept_id
                    JOIN levels l ON c.level_id = l.level_id
                    JOIN semesters s ON c.semester_id = s.semester_id
                    ORDER BY c.course_code
                ");
            } else {
                $message = displayError("Error deleting course: " . $conn->error);
            }
        }
    } else {
        $message = displayError("Course not found");
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All Courses - Ogbonnaya Onu Polytechnic</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }

        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .card-header {
            background-color: #343a40;
            color: white;
            border-radius: 10px 10px 0 0 !important;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="../images/logo.jpg" alt="Logo" width="30" height="30" class="d-inline-block align-text-top me-2 rounded-circle">
                Admin Dashboard
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Students
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="students.php">All Students</a></li>
                            <li><a class="dropdown-item" href="pending_students.php">Pending Approvals</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Lecturers
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="lecturers.php">All Lecturers</a></li>
                            <li><a class="dropdown-item" href="pending_lecturers.php">Pending Approvals</a></li>
                            <li><a class="dropdown-item" href="assign_courses.php">Assign Courses</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Courses
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item active" href="courses.php">All Courses</a></li>
                            <li><a class="dropdown-item" href="add_course.php">Add New Course</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Departments
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="departments.php">All Departments</a></li>
                            <li><a class="dropdown-item" href="add_department.php">Add New Department</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">Reports</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-1"></i> <?php echo $_SESSION['username']; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profile</a></li>
                            <li><a class="dropdown-item" href="change_password.php">Change Password</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col-md-12">
                <h2>All Courses</h2>
                <p class="text-muted">Manage courses for the polytechnic</p>

                <?php if (isset($_SESSION['success_message'])): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($_SESSION['error_message'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <?php echo $message; ?>

        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Courses List</h5>
                <div>
                    <a href="regenerate_all_qr.php" class="btn btn-warning btn-sm me-2" onclick="return confirm('Are you sure you want to regenerate QR codes for all courses?')">
                        <i class="fas fa-sync-alt"></i> Regenerate All QR Codes
                    </a>
                    <a href="add_course.php" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus-circle"></i> Add New Course
                    </a>
                </div>
            </div>
            <div class="card-body">
                <?php if ($courses->num_rows > 0): ?>


                    <div class="table-responsive">
                        <table class="table table-striped table-hover data-table">
                            <thead>
                                <tr>
                                    <th>Code</th>
                                    <th>Title</th>
                                    <th>Department</th>
                                    <th>Level</th>
                                    <th>Semester</th>
                                    <th>QR Code</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($course = $courses->fetch_assoc()): ?>
                                    <tr>
                                        <td><?php echo $course['course_code']; ?></td>
                                        <td><?php echo $course['course_title']; ?></td>
                                        <td><?php echo $course['dept_name']; ?></td>
                                        <td><?php echo $course['level_name']; ?></td>
                                        <td><?php echo $course['semester_name']; ?></td>
                                        <td>
                                            <?php if (!empty($course['qr_code'])): ?>
                                                <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#qrModal<?php echo $course['course_id']; ?>">
                                                    <i class="fas fa-qrcode"></i> View QR
                                                </button>
                                            <?php else: ?>
                                                <a href="regenerate_qr.php?id=<?php echo $course['course_id']; ?>" class="btn btn-sm btn-warning">
                                                    <i class="fas fa-plus"></i> Generate QR
                                                </a>
                                            <?php endif; ?>

                                            <!-- QR Code Modal -->
                                            <div class="modal fade" id="qrModal<?php echo $course['course_id']; ?>" tabindex="-1" aria-labelledby="qrModalLabel<?php echo $course['course_id']; ?>" aria-hidden="true">
                                                <div class="modal-dialog modal-dialog-centered">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title" id="qrModalLabel<?php echo $course['course_id']; ?>">QR Code for <?php echo $course['course_code']; ?></h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                        </div>
                                                        <div class="modal-body text-center">
                                                            <h6><?php echo $course['course_title']; ?></h6>
                                                            <div class="qr-code my-3">
                                                                <?php if (!empty($course['qr_code']) && file_exists("../" . $course['qr_code'])): ?>
                                                                    <img src="../<?php echo $course['qr_code']; ?>" alt="QR Code" class="img-fluid" style="max-width: 250px;">
                                                                    <p class="mb-0 mt-2">Scan this QR code to mark attendance for this course.</p>
                                                                <?php else: ?>
                                                                    <div class="text-muted">
                                                                        <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                                                                        <p>QR code not available for this course.</p>
                                                                        <a href="regenerate_qr.php?id=<?php echo $course['course_id']; ?>" class="btn btn-warning">
                                                                            <i class="fas fa-sync-alt"></i> Generate QR Code
                                                                        </a>
                                                                    </div>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                            <?php if (!empty($course['qr_code']) && file_exists("../" . $course['qr_code'])): ?>
                                                                <a href="regenerate_qr.php?id=<?php echo $course['course_id']; ?>" class="btn btn-warning">
                                                                    <i class="fas fa-sync-alt"></i> Regenerate
                                                                </a>
                                                                <a href="../<?php echo $course['qr_code']; ?>" download="<?php echo $course['course_code']; ?>_QR.png" class="btn btn-primary">
                                                                    <i class="fas fa-download"></i> Download
                                                                </a>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <a href="edit_course.php?id=<?php echo $course['course_id']; ?>" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="courses.php?delete=<?php echo $course['course_id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this course?')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info">No courses found. <a href="add_course.php">Add your first course</a>.</div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-3 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; <?php echo date('Y'); ?> Ogbonnaya Onu Polytechnic. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="../about.php" class="text-white me-3">About</a>
                    <a href="../support.php" class="text-white">Support</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script>
        $(document).ready(function() {
            $('.data-table').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
            });
        });
    </script>
</body>
</html>
