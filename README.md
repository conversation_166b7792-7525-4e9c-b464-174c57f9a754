# Attendance Management System

A comprehensive web-based attendance management system designed for educational institutions, featuring QR code-based attendance marking, real-time tracking, and detailed reporting.

## 🚀 Features

### For Administrators
- **Complete System Management**: Manage departments, courses, lecturers, and students
- **User Approval System**: Approve or reject lecturer and student registrations
- **Academic Session Management**: Configure academic years and semesters
- **System-wide Reporting**: Generate comprehensive attendance reports
- **Real-time Notifications**: Get notified of new registrations and system events

### For Lecturers
- **Course Management**: View assigned courses and enrolled students
- **QR Code Generation**: Start class sessions with auto-generated QR codes
- **Attendance Tracking**: Real-time attendance monitoring during classes
- **Detailed Reports**: Generate attendance reports with filtering options
- **Absent Student Management**: Track and contact students who miss classes

### For Students
- **QR Code Scanning**: Mark attendance by scanning lecturer-generated QR codes
- **Course Overview**: View available courses and assigned lecturers
- **Attendance History**: Track personal attendance records
- **Profile Management**: Update personal information and change passwords

## 🛠️ Technologies Used

- **Frontend**: HTML5, CSS3, Bootstrap 5.3, JavaScript, jQuery
- **Backend**: PHP 7.4+, MySQL 5.7+
- **Libraries**: Font Awesome, DataTables, Google Charts API
- **Security**: Password hashing, prepared statements, session management

## 📋 Requirements

- Web server (Apache/Nginx) with PHP 7.4 or higher
- MySQL 5.7 or higher (or MariaDB 10.2+)
- PHP extensions: mysqli, session, json, curl
- Internet connection for external libraries and QR code generation

## 🚀 Quick Start

1. **Download and Extract**
   ```
   Extract files to your web server directory:
   - XAMPP: C:\xampp\htdocs\ATTENDANCE\
   - WAMP: C:\wamp64\www\ATTENDANCE\
   - Linux: /var/www/html/ATTENDANCE/
   ```

2. **Set Permissions**
   ```bash
   chmod 755 config/
   chmod 755 qr_codes/
   chmod 755 lecturer/qr_codes/
   ```

3. **Run Setup**
   - Start your web server and MySQL
   - Navigate to: `http://localhost/ATTENDANCE/setup.php`
   - Follow the 5-step setup wizard

4. **First Login**
   - Go to: `http://localhost/ATTENDANCE/`
   - Login with the admin credentials you created
   - Start configuring your system!

## 📖 Documentation

- **[HOW_TO_DEPLOY.txt](HOW_TO_DEPLOY.txt)**: Complete deployment guide
- **[CODE_EXPLANATION.txt](CODE_EXPLANATION.txt)**: Technical documentation and code structure

## Admin Access

The system includes an admin account that is created during the initial setup process. Admin credentials are configured during installation.

## How It Works

### Admin
1. Log in with admin credentials
2. Add departments, courses, and approve lecturer/student registrations
3. Assign courses to lecturers
4. Generate and view reports

### Lecturer
1. Register with your staff code and details
2. Wait for admin approval
3. Log in to view assigned courses
4. Click "Start Class Session" for the course you're teaching
5. Display the generated QR code for students to scan
6. End the class session when done
7. View attendance reports

### Student
1. Register with your matriculation number and details
2. Wait for admin approval
3. Log in to view your courses
4. Scan the QR code displayed by the lecturer
5. View your attendance history

## 🏗️ System Architecture

```
├── Root Directory
│   ├── setup.php              # Installation wizard
│   ├── index.php              # Main entry point
│   └── config/                # Configuration files
├── admin/                     # Administrator interface
├── lecturer/                  # Lecturer interface
├── student/                   # Student interface
└── qr_codes/                  # Generated QR codes
```

## 🔐 Security Features

- **Password Security**: Bcrypt hashing for all passwords
- **SQL Injection Protection**: Prepared statements throughout
- **Session Management**: Secure session handling with timeouts
- **Access Control**: Role-based permissions and authentication
- **Input Validation**: Comprehensive data sanitization

## 📊 Key Workflows

### Attendance Marking Process
1. Lecturer starts a class session
2. System generates unique QR code
3. Students scan QR code to mark attendance
4. Lecturer ends session (auto-marks absent students)
5. Reports generated with complete attendance data

### User Management
1. Users register through respective portals
2. Admin receives notification of new registrations
3. Admin approves or rejects applications
4. Approved users can access their dashboards

## 🚨 Troubleshooting

### Common Issues
1. **Database Connection Failed**: Check MySQL service and credentials
2. **Permission Denied**: Verify folder permissions for config/ and qr_codes/
3. **QR Codes Not Working**: Check internet connection and folder permissions
4. **Session Timeout**: Adjust timeout settings in config/session.php

## 📈 Reporting Features

- **Real-time Attendance**: Live tracking during class sessions
- **Detailed Reports**: Filter by course, date range, and attendance status
- **Export Functionality**: Excel export for further analysis
- **Summary Statistics**: Attendance rates and trends
- **Absent Student Tracking**: Identify and contact missing students

## 🌟 Why Choose This System?

- **Easy Setup**: 5-minute installation wizard
- **User Friendly**: Intuitive interface for all user types
- **Secure**: Enterprise-level security features
- **Scalable**: Handles multiple departments and courses
- **Modern**: Built with latest web technologies
- **Comprehensive**: Complete attendance management solution

---

**Ready to streamline your attendance management?** Follow the deployment guide and get started today!
