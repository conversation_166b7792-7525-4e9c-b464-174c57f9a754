<?php
require_once '../config/database.php';
require_once '../config/functions.php';

// Note: session is already started in functions.php which includes session.php

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    // Clear any existing session data
    $_SESSION = array();
    session_destroy();

    // Start a new session for the redirect
    session_start();
    $_SESSION['error_message'] = "You must login to access the admin area.";

    // Redirect to login page
    header("Location: ../index.php");
    exit();
}

// Check for session timeout (2 hours of inactivity for admin)
$max_idle_time = 7200; // 2 hours in seconds
if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > $max_idle_time)) {
    // Session has expired due to inactivity
    $_SESSION = array();
    session_destroy();

    // Start a new session for the redirect
    session_start();
    $_SESSION['error_message'] = "Your session has expired due to inactivity. Please login again.";

    // Redirect to login page
    header("Location: ../index.php");
    exit();
}

// Additional security: Check if session is valid
if (!isset($_SESSION['last_regeneration'])) {
    $_SESSION['last_regeneration'] = time();
}

// Update last activity time
$_SESSION['last_activity'] = time();
?>
