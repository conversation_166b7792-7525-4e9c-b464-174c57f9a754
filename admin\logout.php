<?php
require_once '../config/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Unset all session variables
$_SESSION = array();

// Destroy the session
session_destroy();

// Check if this is a silent logout (AJAX request)
$is_ajax = isset($_SERVER['HTTP_X_REQUESTED_WITH']) &&
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';

if ($is_ajax) {
    // For AJAX requests, just return a 204 No Content status
    http_response_code(204);
    exit;
} else {
    // For normal requests, redirect with a success message
    session_start();
    $_SESSION['success_message'] = "You have been successfully logged out.";
    redirect('../index.php');
}
?>
