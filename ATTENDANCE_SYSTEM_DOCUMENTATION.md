# QR CODE ATTENDANCE MANAGEMENT SYSTEM DOCUMENTATION
## Ogbonnaya Onu Polytechnic

## Table of Contents
1. [System Overview](#system-overview)
2. [User Interfaces](#user-interfaces)
3. [Attendance Marking Process](#attendance-marking-process)
4. [Technical Implementation](#technical-implementation)
5. [Security Features](#security-features)
6. [System Requirements](#system-requirements)
7. [Installation Guide](#installation-guide)
8. [Troubleshooting](#troubleshooting)

## System Overview

The QR Code Attendance Management System is a web-based platform designed for Ogbonnaya Onu Polytechnic to streamline the attendance tracking process. The system replaces traditional paper-based attendance methods with a digital solution using QR code technology.

### Key Features
- QR code generation for each course
- Real-time attendance tracking
- Student registration and approval system
- Lecturer management
- Course management
- Comprehensive reporting
- User-friendly interfaces for all stakeholders

## User Interfaces

The system consists of three main interfaces:

### 1. Admin Interface
- Dashboard with system overview
- Student management (view, approve, delete)
- Lecturer management (view, approve, delete)
- Course management
- Department management
- Comprehensive reporting
- System settings

### 2. Lecturer Interface
- Dashboard with course overview
- QR code display for their courses
- Attendance records for their courses
- Student list for each course
- Attendance reports

### 3. Student Interface
- Dashboard with attendance summary
- QR code scanner
- Attendance history
- Course list
- Profile management

## Attendance Marking Process

### QR Code Generation
1. Each course in the system has a unique QR code
2. QR codes are automatically generated when courses are added
3. QR codes contain encoded information about the specific course
4. Lecturers can access and display these QR codes during their classes

### Student Attendance Process
1. Students log into their accounts using their matriculation number and password
2. They navigate to the "Scan QR" section in their student dashboard
3. The system activates their device's camera
4. Students scan the QR code displayed by the lecturer
5. The system processes the QR code and records the attendance

### Verification and Recording
1. When a student scans a QR code, the system:
   - Identifies the course from the QR code
   - Verifies the student is registered for that course
   - Records the attendance with a timestamp
   - Prevents duplicate attendance entries for the same day
2. The student receives immediate confirmation that their attendance has been recorded
3. The attendance record includes:
   - Student information (ID, name, phone number)
   - Course information
   - Date and time
   - Attendance status (present)

## Technical Implementation

### Database Structure
The system uses a MySQL database with the following key tables:
- `students`: Stores student information
- `lecturers`: Stores lecturer information
- `courses`: Stores course information
- `attendance`: Records attendance data
- `departments`: Stores department information
- `levels`: Stores academic levels
- `semesters`: Stores semester information
- `admin`: Stores administrator credentials

### Technologies Used
- Frontend: HTML, CSS, JavaScript, Bootstrap
- Backend: PHP
- Database: MySQL
- QR Code Generation: PHP QR Code library
- QR Code Scanning: HTML5 QR Code Scanner library

### API Endpoints
- `/mark_attendance.php`: Records attendance when a QR code is scanned
- `/get_course_qr.php`: Retrieves QR code for a specific course
- Various other endpoints for user authentication and data management

## Security Features

### User Authentication
- Secure password hashing using bcrypt
- Session management with timeout (10 minutes for admin)
- Role-based access control

### Data Validation
- Input sanitization to prevent SQL injection
- Form validation to ensure data integrity
- CSRF protection

### Registration Approval
- Student registrations require admin approval
- Lecturer registrations require admin approval
- Email verification for password resets

## System Requirements

### Server Requirements
- Web server (Apache/Nginx)
- PHP 7.4 or higher
- MySQL 5.7 or higher
- SSL certificate (recommended for production)

### Client Requirements
- Modern web browser with JavaScript enabled
- Camera access for QR code scanning
- Internet connection

## Installation Guide

1. Clone or download the system files to your web server's document root
2. Create a MySQL database named `attendance_system`
3. Import the database schema from `database/db_schema.sql`
4. Configure database connection in `config/database.php`
5. Run the setup script by accessing `setup.php` in your browser
6. Access the system through your web browser

### Admin Access
Admin credentials are configured during the initial setup process.

## Troubleshooting

### Common Issues

#### QR Code Scanning Issues
- Ensure camera permissions are granted in the browser
- Check for adequate lighting when scanning QR codes
- Make sure the QR code is displayed clearly and fully visible

#### Attendance Not Recording
- Verify the student is registered for the course
- Check if attendance has already been marked for the day
- Ensure the student account is approved by the admin

#### Login Problems
- Confirm credentials are correct
- Verify the account has been approved by the admin
- Check for any system messages regarding account status

### Support Contact
For technical support, please contact the system <NAME_EMAIL>
