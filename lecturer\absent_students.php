<?php
require_once '../config/functions.php';

// Check if lecturer is logged in
if (!isLecturerLoggedIn()) {
    redirect('../index.php');
}

// Get lecturer details
$lecturer_id = $_SESSION['lecturer_id'];
$lecturer = getLecturerDetails($lecturer_id);

// Get assigned courses
$courses = $conn->query("
    SELECT c.*, d.dept_name, l.level_name, s.semester_name
    FROM lecturer_courses lc
    JOIN courses c ON lc.course_id = c.course_id
    JOIN departments d ON c.dept_id = d.dept_id
    JOIN levels l ON c.level_id = l.level_id
    JOIN semesters s ON c.semester_id = s.semester_id
    WHERE lc.lecturer_id = $lecturer_id
    ORDER BY c.course_code
");

// Get course filter if set
$course_filter = isset($_GET['course_id']) ? intval($_GET['course_id']) : 0;

// Get absent students - only those who missed actual conducted classes
if ($course_filter > 0) {
    // Get course details
    $course_query = $conn->prepare("
        SELECT c.*, d.dept_name, l.level_name, s.semester_name
        FROM courses c
        JOIN departments d ON c.dept_id = d.dept_id
        JOIN levels l ON c.level_id = l.level_id
        JOIN semesters s ON c.semester_id = s.semester_id
        WHERE c.course_id = ?
    ");
    $course_query->bind_param("i", $course_filter);
    $course_query->execute();
    $course_result = $course_query->get_result();
    $course = $course_result->fetch_assoc();

    // Get students who were marked absent in completed class sessions for this course
    $students_query = $conn->prepare("
        SELECT DISTINCT s.*, a.attendance_date, cs.start_time, cs.end_time
        FROM students s
        JOIN attendance a ON s.student_id = a.student_id
        JOIN class_sessions cs ON a.session_id = cs.session_id
        WHERE a.course_id = ? AND a.status = 'absent' AND cs.status = 'ended'
        AND s.status = 'approved'
        ORDER BY a.attendance_date DESC, s.name
    ");
    $students_query->bind_param("i", $course_filter);
    $students_query->execute();
    $absent_students = $students_query->get_result();
} else {
    // Get all students who were marked absent in completed class sessions across all lecturer's courses
    $absent_students = $conn->query("
        SELECT DISTINCT s.*, c.course_code, c.course_title, d.dept_name, l.level_name,
               a.attendance_date, cs.start_time, cs.end_time
        FROM students s
        JOIN attendance a ON s.student_id = a.student_id
        JOIN courses c ON a.course_id = c.course_id
        JOIN class_sessions cs ON a.session_id = cs.session_id
        JOIN departments d ON s.dept_id = d.dept_id
        JOIN levels l ON s.level_id = l.level_id
        JOIN lecturer_courses lc ON c.course_id = lc.course_id
        WHERE lc.lecturer_id = $lecturer_id
        AND s.status = 'approved'
        AND a.status = 'absent'
        AND cs.status = 'ended'
        ORDER BY a.attendance_date DESC, c.course_code, s.name
    ");
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Absent Students - Lecturer Dashboard</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <!-- Include Navbar -->
    <?php include 'includes/navbar.php'; ?>

    <div class="container mt-4">
        <div class="row mb-3">
            <div class="col-md-8">
                <h2><i class="fas fa-user-times text-danger me-2"></i> Absent Students</h2>
                <p class="text-muted">Students who missed completed class sessions</p>
            </div>
            <div class="col-md-4">
                <form method="GET" action="" class="d-flex">
                    <select name="course_id" class="form-select me-2">
                        <option value="0">All Courses</option>
                        <?php
                        $courses->data_seek(0);
                        while ($course_option = $courses->fetch_assoc()):
                        ?>
                            <option value="<?php echo $course_option['course_id']; ?>" <?php echo ($course_filter == $course_option['course_id']) ? 'selected' : ''; ?>>
                                <?php echo $course_option['course_code'] . ' - ' . $course_option['course_title']; ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                    <button type="submit" class="btn btn-primary">Filter</button>
                </form>
            </div>
        </div>

        <?php if ($course_filter > 0 && isset($course)): ?>
            <div class="alert alert-info">
                <strong>Filtered by Course:</strong> <?php echo $course['course_code'] . ' - ' . $course['course_title']; ?> |
                <strong>Department:</strong> <?php echo $course['dept_name']; ?> |
                <strong>Level:</strong> <?php echo $course['level_name']; ?> |
                <strong>Semester:</strong> <?php echo $course['semester_name']; ?>
            </div>
        <?php endif; ?>

        <div class="card">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0"><i class="fas fa-list-alt me-2"></i> Absent Students List</h5>
            </div>
            <div class="card-body">
                <?php if (isset($absent_students) && $absent_students->num_rows > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover data-table">
                            <thead>
                                <tr>
                                    <th>S/N</th>
                                    <th>Name</th>
                                    <th>Matric Number</th>
                                    <th>Phone</th>
                                    <?php if ($course_filter == 0): ?>
                                        <th>Course</th>
                                        <th>Department</th>
                                        <th>Level</th>
                                    <?php endif; ?>
                                    <th>Date Missed</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $sn = 1;
                                while ($student = $absent_students->fetch_assoc()):
                                ?>
                                    <tr>
                                        <td><?php echo $sn++; ?></td>
                                        <td><?php echo $student['name']; ?></td>
                                        <td><?php echo $student['matric_number']; ?></td>
                                        <td><?php echo $student['phone'] ?? 'N/A'; ?></td>
                                        <?php if ($course_filter == 0): ?>
                                            <td><?php echo $student['course_code']; ?></td>
                                            <td><?php echo $student['dept_name']; ?></td>
                                            <td><?php echo $student['level_name']; ?></td>
                                        <?php endif; ?>
                                        <td>
                                            <span class="badge bg-danger">
                                                <?php
                                                if (!empty($student['attendance_date'])) {
                                                    echo date('d M Y', strtotime($student['attendance_date']));
                                                } else {
                                                    echo 'No Date';
                                                }
                                                ?>
                                            </span>
                                        </td>
                                        <td>
                                            <a href="mailto:<?php echo $student['email']; ?>" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-envelope"></i> Email
                                            </a>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>No absent students found.</strong><br>
                        This means either:
                        <ul class="mb-0 mt-2">
                            <li>All students attended their classes</li>
                            <li>No classes have been completed yet</li>
                            <li>You need to start and end a class session to generate attendance records</li>
                        </ul>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-3 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; <?php echo date('Y'); ?> Ogbonnaya Onu Polytechnic. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="../about.php" class="text-white me-3">About</a>
                    <a href="../contact.php" class="text-white">Contact</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

    <script>
        $(document).ready(function() {
            $('.data-table').DataTable({
                "pageLength": 25,
                "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]]
            });
        });
    </script>
</body>
</html>
