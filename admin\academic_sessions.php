<?php
require_once 'auth_check.php';
require_once '../config/functions.php';

$success = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['set_current_session'])) {
        $academic_year = sanitize($_POST['academic_year']);
        $semester_id = sanitize($_POST['semester_id']);
        
        if (!empty($academic_year) && !empty($semester_id)) {
            // Start transaction
            $conn->begin_transaction();
            
            try {
                // Set all sessions to not current
                $conn->query("UPDATE academic_sessions SET is_current = 0");
                
                // Check if this session already exists
                $check_stmt = $conn->prepare("SELECT session_id FROM academic_sessions WHERE academic_year = ? AND semester_id = ?");
                $check_stmt->bind_param("si", $academic_year, $semester_id);
                $check_stmt->execute();
                $result = $check_stmt->get_result();
                
                if ($result->num_rows > 0) {
                    // Update existing session to current
                    $session = $result->fetch_assoc();
                    $update_stmt = $conn->prepare("UPDATE academic_sessions SET is_current = 1 WHERE session_id = ?");
                    $update_stmt->bind_param("i", $session['session_id']);
                    $update_stmt->execute();
                } else {
                    // Insert new session as current
                    $insert_stmt = $conn->prepare("INSERT INTO academic_sessions (academic_year, semester_id, is_current) VALUES (?, ?, 1)");
                    $insert_stmt->bind_param("si", $academic_year, $semester_id);
                    $insert_stmt->execute();
                }
                
                $conn->commit();
                $success = "Academic session updated successfully!";
            } catch (Exception $e) {
                $conn->rollback();
                $error = "Error updating academic session: " . $e->getMessage();
            }
        } else {
            $error = "Please fill in all fields";
        }
    }
}

// Get current session
$current_session = $conn->query("
    SELECT a.*, s.semester_name 
    FROM academic_sessions a 
    JOIN semesters s ON a.semester_id = s.semester_id 
    WHERE a.is_current = 1 
    LIMIT 1
");

// Get all sessions
$all_sessions = $conn->query("
    SELECT a.*, s.semester_name 
    FROM academic_sessions a 
    JOIN semesters s ON a.semester_id = s.semester_id 
    ORDER BY a.created_at DESC
");

// Get semesters for dropdown
$semesters = $conn->query("SELECT * FROM semesters ORDER BY semester_id");
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Academic Sessions - Admin Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-3">
                        <img src="../images/logo.jpg" alt="Logo" class="img-fluid rounded-circle" style="width: 60px; height: 60px;">
                        <h6 class="mt-2">Admin Panel</h6>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="index.php">
                                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="academic_sessions.php">
                                <i class="fas fa-calendar-alt me-2"></i>Academic Sessions
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="departments.php">
                                <i class="fas fa-building me-2"></i>Departments
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="courses.php">
                                <i class="fas fa-book me-2"></i>Courses
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="students.php">
                                <i class="fas fa-user-graduate me-2"></i>Students
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="lecturers.php">
                                <i class="fas fa-chalkboard-teacher me-2"></i>Lecturers
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Academic Sessions Management</h1>
                </div>

                <?php if ($success): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $success; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Current Session Display -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="fas fa-calendar-check me-2"></i>Current Academic Session</h5>
                            </div>
                            <div class="card-body">
                                <?php if ($current_session && $current_session->num_rows > 0): ?>
                                    <?php $current = $current_session->fetch_assoc(); ?>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h4 class="text-primary"><?php echo htmlspecialchars($current['academic_year']); ?></h4>
                                            <p class="mb-0"><strong>Semester:</strong> <?php echo htmlspecialchars($current['semester_name']); ?></p>
                                        </div>
                                        <div class="col-md-6 text-md-end">
                                            <span class="badge bg-success fs-6">ACTIVE</span>
                                            <p class="small text-muted mt-2">Set on: <?php echo date('M d, Y', strtotime($current['created_at'])); ?></p>
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <div class="text-center text-muted">
                                        <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                                        <h5>No Current Academic Session Set</h5>
                                        <p>Please set the current academic session below.</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Set Current Session Form -->
                <div class="row mb-4">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-cog me-2"></i>Set Current Academic Session</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="academic_year" class="form-label">Academic Year</label>
                                            <input type="text" class="form-control" id="academic_year" name="academic_year" 
                                                   placeholder="e.g., 2024/2025" required>
                                            <div class="form-text">Format: YYYY/YYYY (e.g., 2024/2025)</div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="semester_id" class="form-label">Semester</label>
                                            <select class="form-select" id="semester_id" name="semester_id" required>
                                                <option value="">Select Semester</option>
                                                <?php while ($semester = $semesters->fetch_assoc()): ?>
                                                    <option value="<?php echo $semester['semester_id']; ?>">
                                                        <?php echo htmlspecialchars($semester['semester_name']); ?>
                                                    </option>
                                                <?php endwhile; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <button type="submit" name="set_current_session" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Set as Current Session
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- All Sessions History -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-history me-2"></i>Session History</h5>
                            </div>
                            <div class="card-body">
                                <?php if ($all_sessions && $all_sessions->num_rows > 0): ?>
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Academic Year</th>
                                                    <th>Semester</th>
                                                    <th>Status</th>
                                                    <th>Created</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php while ($session = $all_sessions->fetch_assoc()): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($session['academic_year']); ?></td>
                                                        <td><?php echo htmlspecialchars($session['semester_name']); ?></td>
                                                        <td>
                                                            <?php if ($session['is_current']): ?>
                                                                <span class="badge bg-success">Current</span>
                                                            <?php else: ?>
                                                                <span class="badge bg-secondary">Inactive</span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td><?php echo date('M d, Y H:i', strtotime($session['created_at'])); ?></td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else: ?>
                                    <div class="text-center text-muted">
                                        <p>No academic sessions found.</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
