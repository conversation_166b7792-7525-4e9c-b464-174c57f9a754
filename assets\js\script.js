// Main JavaScript for Ogbonnaya Onu Polytechnic Attendance System

document.addEventListener('DOMContentLoaded', function() {
    // Password toggle visibility
    const passwordToggles = document.querySelectorAll('.password-toggle');
    
    if (passwordToggles) {
        passwordToggles.forEach(toggle => {
            toggle.addEventListener('click', function() {
                const passwordField = this.previousElementSibling;
                const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordField.setAttribute('type', type);
                
                // Toggle icon
                this.classList.toggle('fa-eye');
                this.classList.toggle('fa-eye-slash');
            });
        });
    }
    
    // Initialize QR Scanner if element exists
    const qrReader = document.getElementById('qr-reader');
    
    if (qrReader) {
        const html5QrCode = new Html5Qrcode("qr-reader");
        const qrCodeSuccessCallback = (decodedText, decodedResult) => {
            // Stop scanning
            html5QrCode.stop().then(() => {
                console.log('QR Code scanning stopped.');
                
                // Show loading indicator
                document.getElementById('scan-result').innerHTML = '<div class="alert alert-info">Processing attendance...</div>';
                
                // Send the data to the server
                const formData = new FormData();
                formData.append('qr_data', decodedText);
                
                fetch('mark_attendance.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('scan-result').innerHTML = 
                            `<div class="alert alert-success">
                                <h4>Attendance Marked Successfully!</h4>
                                <p>Course: ${data.course}</p>
                                <p>Student: ${data.student}</p>
                                <p>Date: ${data.date}</p>
                                <p>Time: ${data.time}</p>
                            </div>`;
                    } else {
                        document.getElementById('scan-result').innerHTML = 
                            `<div class="alert alert-danger">
                                <h4>Error!</h4>
                                <p>${data.message}</p>
                            </div>`;
                    }
                    
                    // Add restart button
                    document.getElementById('scan-result').innerHTML += 
                        `<button class="btn btn-primary mt-3" id="restart-scan">Scan Another QR Code</button>`;
                    
                    // Add event listener to restart button
                    document.getElementById('restart-scan').addEventListener('click', function() {
                        document.getElementById('scan-result').innerHTML = '';
                        startScanner();
                    });
                })
                .catch(error => {
                    document.getElementById('scan-result').innerHTML = 
                        `<div class="alert alert-danger">
                            <h4>Error!</h4>
                            <p>An error occurred while processing the QR code.</p>
                        </div>
                        <button class="btn btn-primary mt-3" id="restart-scan">Try Again</button>`;
                    
                    document.getElementById('restart-scan').addEventListener('click', function() {
                        document.getElementById('scan-result').innerHTML = '';
                        startScanner();
                    });
                    
                    console.error('Error:', error);
                });
            }).catch(err => {
                console.error('Error stopping QR Code scanner:', err);
            });
        };
        
        const config = { fps: 10, qrbox: { width: 250, height: 250 } };
        
        // Start scanner function
        function startScanner() {
            html5QrCode.start(
                { facingMode: "environment" }, 
                config, 
                qrCodeSuccessCallback
            ).catch(err => {
                document.getElementById('scan-result').innerHTML = 
                    `<div class="alert alert-danger">
                        <h4>Camera Error!</h4>
                        <p>Could not access the camera. Please make sure you have granted camera permissions.</p>
                        <p>Technical details: ${err}</p>
                    </div>
                    <button class="btn btn-primary mt-3" id="restart-scan">Try Again</button>`;
                
                document.getElementById('restart-scan').addEventListener('click', function() {
                    document.getElementById('scan-result').innerHTML = '';
                    startScanner();
                });
            });
        }
        
        // Start the scanner
        startScanner();
    }
    
    // Auto-hide alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
    
    if (alerts) {
        alerts.forEach(alert => {
            setTimeout(() => {
                alert.style.opacity = '0';
                setTimeout(() => {
                    alert.remove();
                }, 500);
            }, 5000);
        });
    }
    
    // Form validation for registration
    const registrationForm = document.getElementById('registration-form');
    
    if (registrationForm) {
        registrationForm.addEventListener('submit', function(event) {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            
            if (password !== confirmPassword) {
                event.preventDefault();
                document.getElementById('password-error').textContent = 'Passwords do not match!';
                document.getElementById('password-error').style.display = 'block';
            }
        });
    }
    
    // Data tables initialization
    if ($.fn.DataTable) {
        $('.data-table').DataTable({
            responsive: true,
            dom: 'Bfrtip',
            buttons: [
                'copy', 'csv', 'excel', 'pdf', 'print'
            ]
        });
    }
});
