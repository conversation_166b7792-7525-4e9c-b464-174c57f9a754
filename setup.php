<?php
session_start();

// Helper: simple flash messages
function flash($key, $msg = null) {
    if ($msg !== null) { $_SESSION['flash'][$key] = $msg; return; }
    if (!empty($_SESSION['flash'][$key])) { $m = $_SESSION['flash'][$key]; unset($_SESSION['flash'][$key]); return $m; }
    return null;
}

// QR Code generation function
function generateQRCode($data) {
    // Simple QR code generation using Google Charts API
    $encoded_data = urlencode($data);
    $qr_url = "https://chart.googleapis.com/chart?chs=200x200&cht=qr&chl=" . $encoded_data;

    // For setup purposes, we'll just return a placeholder or the URL
    // In production, you might want to save the actual QR code image
    return $qr_url;
}

// Determine current step
$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
if ($step < 1 || $step > 5) $step = 1;

// Defaults (you can change during setup)
if (!isset($_SESSION['db'])) {
    $_SESSION['db'] = [
        'host' => 'localhost',
        'user' => 'root',
        'pass' => '',
        'name' => 'attendance_system'
    ];
}

// Actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($step === 2 && isset($_POST['db_submit'])) {
        $_SESSION['db']['host'] = trim($_POST['host'] ?? 'localhost');
        $_SESSION['db']['user'] = trim($_POST['user'] ?? 'root');
        $_SESSION['db']['pass'] = $_POST['pass'] ?? '';
        $_SESSION['db']['name'] = trim($_POST['name'] ?? 'attendance_system');

        // Test connection and create DB
        $ok = false; $err = '';
        try {
            $mysqli = @new mysqli($_SESSION['db']['host'], $_SESSION['db']['user'], $_SESSION['db']['pass']);
            if ($mysqli->connect_error) throw new Exception($mysqli->connect_error);
            $db = $mysqli->real_escape_string($_SESSION['db']['name']);
            if (!$mysqli->query("CREATE DATABASE IF NOT EXISTS `$db` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"))
                throw new Exception($mysqli->error);
            if (!$mysqli->select_db($db)) throw new Exception($mysqli->error);
            $ok = true;
        } catch (Exception $e) { $err = $e->getMessage(); }

        if ($ok) {
            // Write config/database.php
            $cfg = "<?php\n".
                   "// Database configuration (generated by setup.php)\n".
                   "\$host = '".addslashes($_SESSION['db']['host'])."';\n".
                   "\$username = '".addslashes($_SESSION['db']['user'])."';\n".
                   "\$password = '".addslashes($_SESSION['db']['pass'])."';\n".
                   "\$database = '".addslashes($_SESSION['db']['name'])."';\n\n".
                   "try {\n".
                   "    \$conn = new mysqli(\$host, \$username, \$password);\n".
                   "    if (\$conn->connect_error) { die('Connection failed: '.\$conn->connect_error); }\n".
                   "    \$result = \$conn->query(\"SHOW DATABASES LIKE '".addslashes($_SESSION['db']['name'])."'\");\n".
                   "    if (\$result->num_rows == 0) { \$conn->query(\"CREATE DATABASE `".addslashes($_SESSION['db']['name'])."` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci\"); }\n".
                   "    \$conn->select_db(\$database);\n".
                   "    \$conn->set_charset('utf8mb4');\n".
                   "} catch (Exception \$e) { die('Database connection error: '.\$e->getMessage()); }\n".
                   "?>\n";
            if (!is_dir(__DIR__.DIRECTORY_SEPARATOR.'config')) { @mkdir(__DIR__.DIRECTORY_SEPARATOR.'config', 0755, true); }
            $written = @file_put_contents(__DIR__.DIRECTORY_SEPARATOR.'config'.DIRECTORY_SEPARATOR.'database.php', $cfg);
            if ($written === false) { flash('error', 'Could not write config/database.php. Please check folder permissions.'); }
            else { header('Location: setup.php?step=3'); exit; }
        } else { flash('error', 'Database connection failed: '.$err); }
    }

    if ($step === 3 && isset($_POST['tables_submit'])) {
        try {
            require_once __DIR__.'/config/database.php';
            if (!isset($conn)) {
                throw new Exception('Database connection not established');
            }
        } catch (Exception $e) {
            flash('error', 'Database configuration error: '.$e->getMessage());
            $step = 2; // Go back to step 2
        }

        if (isset($conn)) {
            $okAll = true; $log = [];
        $stmts = [];
        $stmts[] = "CREATE TABLE IF NOT EXISTS admin (admin_id INT AUTO_INCREMENT PRIMARY KEY, username VARCHAR(50) NOT NULL UNIQUE, password VARCHAR(255) NOT NULL, email VARCHAR(100) NOT NULL, created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        $stmts[] = "CREATE TABLE IF NOT EXISTS departments (dept_id INT AUTO_INCREMENT PRIMARY KEY, dept_name VARCHAR(100) NOT NULL UNIQUE, created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        $stmts[] = "CREATE TABLE IF NOT EXISTS levels (level_id INT AUTO_INCREMENT PRIMARY KEY, level_name VARCHAR(50) NOT NULL UNIQUE, created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        $stmts[] = "CREATE TABLE IF NOT EXISTS semesters (semester_id INT AUTO_INCREMENT PRIMARY KEY, semester_name VARCHAR(50) NOT NULL UNIQUE, created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        $stmts[] = "CREATE TABLE IF NOT EXISTS courses (course_id INT AUTO_INCREMENT PRIMARY KEY, course_code VARCHAR(20) NOT NULL UNIQUE, course_title VARCHAR(100) NOT NULL, dept_id INT NOT NULL, level_id INT NOT NULL, semester_id INT NOT NULL, qr_code VARCHAR(255) NULL, created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, FOREIGN KEY (dept_id) REFERENCES departments(dept_id), FOREIGN KEY (level_id) REFERENCES levels(level_id), FOREIGN KEY (semester_id) REFERENCES semesters(semester_id)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        $stmts[] = "CREATE TABLE IF NOT EXISTS lecturers (lecturer_id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(100) NOT NULL, staff_code VARCHAR(50) NOT NULL UNIQUE, email VARCHAR(100) NOT NULL UNIQUE, phone VARCHAR(11) NOT NULL, password VARCHAR(255) NOT NULL, dept_id INT NOT NULL, status ENUM('pending','approved','rejected') DEFAULT 'pending', created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, FOREIGN KEY (dept_id) REFERENCES departments(dept_id)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        $stmts[] = "CREATE TABLE IF NOT EXISTS lecturer_courses (id INT AUTO_INCREMENT PRIMARY KEY, lecturer_id INT NOT NULL, course_id INT NOT NULL, UNIQUE KEY (lecturer_id, course_id), FOREIGN KEY (lecturer_id) REFERENCES lecturers(lecturer_id), FOREIGN KEY (course_id) REFERENCES courses(course_id)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        $stmts[] = "CREATE TABLE IF NOT EXISTS students (student_id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(100) NOT NULL, matric_number VARCHAR(50) NOT NULL UNIQUE, email VARCHAR(100) NOT NULL UNIQUE, phone VARCHAR(11) NOT NULL, password VARCHAR(255) NOT NULL, dept_id INT NOT NULL, level_id INT NOT NULL, semester_id INT NOT NULL, status ENUM('pending','approved','rejected') DEFAULT 'pending', created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, FOREIGN KEY (dept_id) REFERENCES departments(dept_id), FOREIGN KEY (level_id) REFERENCES levels(level_id), FOREIGN KEY (semester_id) REFERENCES semesters(semester_id)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        $stmts[] = "CREATE TABLE IF NOT EXISTS class_sessions (session_id INT AUTO_INCREMENT PRIMARY KEY, course_id INT NOT NULL, lecturer_id INT NOT NULL, start_time DATETIME NOT NULL, end_time DATETIME NULL, qr_code VARCHAR(255) NULL, status ENUM('active','ended') DEFAULT 'active', FOREIGN KEY (course_id) REFERENCES courses(course_id), FOREIGN KEY (lecturer_id) REFERENCES lecturers(lecturer_id)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        $stmts[] = "CREATE TABLE IF NOT EXISTS attendance (attendance_id INT AUTO_INCREMENT PRIMARY KEY, student_id INT NOT NULL, course_id INT NOT NULL, session_id INT NULL, attendance_date DATE NOT NULL, attendance_time TIME NOT NULL, status ENUM('present', 'absent') DEFAULT 'present', marked_by ENUM('student', 'lecturer', 'system') DEFAULT 'student', created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, UNIQUE KEY unique_student_session (student_id, session_id), FOREIGN KEY (student_id) REFERENCES students(student_id), FOREIGN KEY (course_id) REFERENCES courses(course_id), FOREIGN KEY (session_id) REFERENCES class_sessions(session_id)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        $stmts[] = "CREATE TABLE IF NOT EXISTS notifications (notification_id INT AUTO_INCREMENT PRIMARY KEY, user_id INT NOT NULL, user_type ENUM('student','lecturer','admin') NOT NULL, message TEXT NOT NULL, is_read TINYINT(1) DEFAULT 0, related_id INT NULL, related_type VARCHAR(50) NULL, created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

        // Security Questions Tables
        $stmts[] = "CREATE TABLE IF NOT EXISTS security_questions (question_id INT AUTO_INCREMENT PRIMARY KEY, question_text VARCHAR(255) NOT NULL, is_active TINYINT(1) DEFAULT 1, created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        $stmts[] = "CREATE TABLE IF NOT EXISTS student_security_answers (answer_id INT AUTO_INCREMENT PRIMARY KEY, student_id INT NOT NULL, question_id INT NOT NULL, answer_hash VARCHAR(255) NOT NULL, created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, FOREIGN KEY (student_id) REFERENCES students(student_id) ON DELETE CASCADE, FOREIGN KEY (question_id) REFERENCES security_questions(question_id), UNIQUE KEY unique_student_question (student_id, question_id)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        $stmts[] = "CREATE TABLE IF NOT EXISTS lecturer_security_answers (answer_id INT AUTO_INCREMENT PRIMARY KEY, lecturer_id INT NOT NULL, question_id INT NOT NULL, answer_hash VARCHAR(255) NOT NULL, created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, FOREIGN KEY (lecturer_id) REFERENCES lecturers(lecturer_id) ON DELETE CASCADE, FOREIGN KEY (question_id) REFERENCES security_questions(question_id), UNIQUE KEY unique_lecturer_question (lecturer_id, question_id)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

        // Academic Sessions Table
        $stmts[] = "CREATE TABLE IF NOT EXISTS academic_sessions (session_id INT AUTO_INCREMENT PRIMARY KEY, academic_year VARCHAR(20) NOT NULL, semester_id INT NOT NULL, is_current TINYINT(1) DEFAULT 0, created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, FOREIGN KEY (semester_id) REFERENCES semesters(semester_id)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

        foreach ($stmts as $sql) {
            if (!$conn->query($sql)) { $okAll = false; $log[] = 'Error: '.htmlspecialchars($conn->error); } else { $log[] = 'OK'; }
        }

        // Add missing columns to existing tables (for upgrades)
        $column_updates = [
            "ALTER TABLE attendance ADD COLUMN IF NOT EXISTS status ENUM('present', 'absent') DEFAULT 'present'",
            "ALTER TABLE attendance ADD COLUMN IF NOT EXISTS marked_by ENUM('student', 'lecturer', 'system') DEFAULT 'student'",
            "ALTER TABLE attendance DROP INDEX IF EXISTS student_id", // Remove old unique key
            "ALTER TABLE attendance ADD UNIQUE KEY IF NOT EXISTS unique_student_session (student_id, session_id)"
        ];

        foreach ($column_updates as $sql) {
            $conn->query($sql); // Don't fail setup if these don't work (might be MySQL version differences)
        }

        // Update existing attendance records to have proper status and marked_by values
        $conn->query("UPDATE attendance SET status = 'present' WHERE status IS NULL OR status = ''");
        $conn->query("UPDATE attendance SET marked_by = 'student' WHERE marked_by IS NULL OR marked_by = ''");
        // Seed only essential academic structure data
        $conn->query("INSERT IGNORE INTO levels (level_name) VALUES ('ND1'),('ND2'),('HND1'),('HND2')");
        $conn->query("INSERT IGNORE INTO semesters (semester_name) VALUES ('First Semester'),('Second Semester')");

        // Insert default security questions for password reset functionality
        // These questions will be used by lecturers and students for self-service password reset
        $security_questions = [
            "What was the name of your first pet?",
            "What is your mother's maiden name?",
            "What was the name of your first school?",
            "What is the name of the city where you were born?",
            "What was your childhood nickname?",
            "What is the name of your favorite childhood friend?",
            "What was the make of your first car?",
            "What is your father's middle name?",
            "What was the name of the street you grew up on?",
            "What is your favorite book?",
            "What was the name of your first employer?",
            "What is your favorite movie?",
            "What was your favorite food as a child?",
            "What is the name of your favorite teacher?",
            "What was the name of your first stuffed animal?"
        ];

        foreach ($security_questions as $question) {
            $escaped_question = $conn->real_escape_string($question);
            $conn->query("INSERT IGNORE INTO security_questions (question_text) VALUES ('$escaped_question')");
        }

        // Insert default academic session (2024/2025 First Semester)
        $conn->query("INSERT IGNORE INTO academic_sessions (academic_year, semester_id, is_current) VALUES ('2024/2025', 1, 1)");

        // Insert Computer Science department if it doesn't exist
        $conn->query("INSERT IGNORE INTO departments (dept_name) VALUES ('Computer Science')");

        // Add Computer Science HND2 courses (hardcoded as per system requirements)
        $cs_dept_result = $conn->query("SELECT dept_id FROM departments WHERE dept_name = 'Computer Science'");
        $hnd2_level_result = $conn->query("SELECT level_id FROM levels WHERE level_name = 'HND2'");
        $first_sem_result = $conn->query("SELECT semester_id FROM semesters WHERE semester_name = 'First Semester'");
        $second_sem_result = $conn->query("SELECT semester_id FROM semesters WHERE semester_name = 'Second Semester'");

        if ($cs_dept_result && $hnd2_level_result && $first_sem_result && $second_sem_result) {
            $cs_dept_id = $cs_dept_result->fetch_assoc()['dept_id'];
            $hnd2_level_id = $hnd2_level_result->fetch_assoc()['level_id'];
            $first_sem_id = $first_sem_result->fetch_assoc()['semester_id'];
            $second_sem_id = $second_sem_result->fetch_assoc()['semester_id'];

            // First Semester HND2 Computer Science Courses
            $first_sem_courses = [
                ['COM405', 'RESEARCH METHODOLOGY'],
                ['COM411', 'WEB DEVELOPMENT (PHP)'],
                ['COM412', 'PROJECT MANAGEMENT'],
                ['COM413', 'COMPILER CONSTRUCTION'],
                ['COM414', 'DATA COMMUNICATION AND NETWORKING'],
                ['COM415', 'MULTIMEDIA'],
                ['GNS401', 'COMMUNICATION IN ENGLISH IV'],
                ['ENT416', 'ENTREPRENEURSHIP EDUCATION']
            ];

            // Second Semester HND2 Computer Science Courses
            $second_sem_courses = [
                ['COM423', 'EXPERT SYSTEM & MACHINE LEARNING'],
                ['COM422', 'COMPUTER GRAPHICS & ANIMATION'],
                ['COM426', 'COMPUTER SECURITY'],
                ['COM424', 'ETHICAL & PROFESSIONAL PRACTICE IN I.T.']
            ];

            // Insert first semester courses
            foreach ($first_sem_courses as $course) {
                $code = $conn->real_escape_string($course[0]);
                $title = $conn->real_escape_string($course[1]);
                $qr_data = "course_code=$code&course_title=$title";
                $qr_code = generateQRCode($qr_data);
                $conn->query("INSERT IGNORE INTO courses (course_code, course_title, dept_id, level_id, semester_id, qr_code) VALUES ('$code', '$title', $cs_dept_id, $hnd2_level_id, $first_sem_id, '$qr_code')");
            }

            // Insert second semester courses
            foreach ($second_sem_courses as $course) {
                $code = $conn->real_escape_string($course[0]);
                $title = $conn->real_escape_string($course[1]);
                $qr_data = "course_code=$code&course_title=$title";
                $qr_code = generateQRCode($qr_data);
                $conn->query("INSERT IGNORE INTO courses (course_code, course_title, dept_id, level_id, semester_id, qr_code) VALUES ('$code', '$title', $cs_dept_id, $hnd2_level_id, $second_sem_id, '$qr_code')");
            }

            $log[] = 'Computer Science HND2 courses added successfully';
        }

        // Create additional configuration files for enhanced features
        $log[] = 'Creating configuration files for enhanced features...';

        // Create session configuration if it doesn't exist
        $session_config = __DIR__.'/config/session.php';
        if (!file_exists($session_config)) {
            $session_content = "<?php\n".
                "// Session configuration for attendance system\n".
                "if (session_status() == PHP_SESSION_NONE) {\n".
                "    session_start();\n".
                "}\n".
                "\n".
                "// Session timeout settings\n".
                "define('SESSION_TIMEOUT', 600); // 10 minutes for admin\n".
                "define('STUDENT_SESSION_TIMEOUT', 1800); // 30 minutes for students\n".
                "define('LECTURER_SESSION_TIMEOUT', 1800); // 30 minutes for lecturers\n".
                "?>\n";
            @file_put_contents($session_config, $session_content);
            $log[] = 'Session configuration created';
        }

        // Ensure QR codes directory exists with proper permissions
        if (!is_dir(__DIR__.'/qr_codes')) {
            @mkdir(__DIR__.'/qr_codes', 0755, true);
            $log[] = 'QR codes directory created';
        }

        // Create .htaccess for QR codes directory security
        $htaccess_qr = __DIR__.'/qr_codes/.htaccess';
        if (!file_exists($htaccess_qr)) {
            $htaccess_content = "# Prevent direct access to QR code files\n".
                "Options -Indexes\n".
                "# Allow only image files\n".
                "<FilesMatch \"\\.(png|jpg|jpeg|gif|svg)$\">\n".
                "    Order allow,deny\n".
                "    Allow from all\n".
                "</FilesMatch>\n";
            @file_put_contents($htaccess_qr, $htaccess_content);
            $log[] = 'QR codes security configuration created';
        }

        // HND2 Computer Science courses have already been added above
        $log[] = 'HND2 Computer Science courses configured successfully';

            // Final setup completion
            if ($okAll) {
                $log[] = 'All tables and configurations created successfully!';
                $_SESSION['table_log'] = $log;
                header('Location: setup.php?step=4');
                exit;
            } else {
                flash('error', 'Some tables could not be created. Please scroll to see details.');
                $_SESSION['table_log'] = $log;
            }
        }
    }

    if ($step === 4 && isset($_POST['admin_submit'])) {
        try {
            require_once __DIR__.'/config/database.php';
            $username = trim($_POST['username'] ?? '');
            $email = trim($_POST['email'] ?? '');
            $password = $_POST['password'] ?? '';
            $confirm = $_POST['confirm'] ?? '';
            if ($username === '' || $email === '' || $password === '') { flash('error', 'Please fill in all fields.'); }
            elseif ($password !== $confirm) { flash('error', 'Passwords do not match.'); }
            else {
                // Check if an admin already exists
                $exists = $conn->query("SELECT admin_id FROM admin LIMIT 1");
                if ($exists && $exists->num_rows > 0) { header('Location: setup.php?step=5'); exit; }
                $hash = password_hash($password, PASSWORD_BCRYPT);
                $stmt = $conn->prepare("INSERT INTO admin (username, password, email) VALUES (?,?,?)");
                $stmt->bind_param('sss', $username, $hash, $email);
                if ($stmt->execute()) { header('Location: setup.php?step=5'); exit; }
                else { flash('error', 'Failed to create admin: '.$conn->error); }
            }
        } catch (Exception $e) {
            flash('error', 'Database error: '.$e->getMessage());
        }
    }
}

// Checks for step 1
function check_req() {
    $checks = [];
    $checks[] = ['PHP >= 7.4', version_compare(PHP_VERSION, '7.4.0', '>=')];
    $checks[] = ['mysqli extension', extension_loaded('mysqli')];
    $checks[] = ['json extension', extension_loaded('json')];
    $checks[] = ['config folder writable', is_writable(__DIR__.DIRECTORY_SEPARATOR.'config') || (!is_dir(__DIR__.DIRECTORY_SEPARATOR.'config') && is_writable(__DIR__))];
    $checks[] = ['qr_codes folder writable (will be created)', is_writable(__DIR__)];
    return $checks;
}

function already_installed() {
    $dbFile = __DIR__.DIRECTORY_SEPARATOR.'config'.DIRECTORY_SEPARATOR.'database.php';
    if (!file_exists($dbFile)) return false;
    try { require $dbFile; if ($conn) { $res = $conn->query("SHOW TABLES LIKE 'admin'"); if ($res && $res->num_rows) { $a = $conn->query("SELECT COUNT(*) c FROM admin"); if ($a && ($row=$a->fetch_assoc()) && (int)$row['c']>0) return true; } } } catch (Exception $e) {}
    return false;
}

$installed = already_installed();

?><!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1">
<title>System Setup Wizard | Attendance</title>
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
<style>
body{background:linear-gradient(135deg,#0d6efd22,#6610f222),url('images/image2.jpg') center/cover fixed;min-height:100vh}
.overlay{backdrop-filter:blur(3px);background:rgba(255,255,255,.85)}
.card-setup{border-radius:14px;box-shadow:0 10px 30px rgba(0,0,0,.1)}
.step{display:flex;align-items:center;gap:.5rem}
.step .bubble{width:34px;height:34px;border-radius:50%;display:inline-grid;place-items:center;background:#e9ecef;color:#6c757d;font-weight:700}
.step.active .bubble{background:#0d6efd;color:#fff}
.step.done .bubble{background:#198754;color:#fff}
.step .label{font-weight:600}
</style>
</head>
<body>
<div class="container py-5">
  <div class="row justify-content-center">
    <div class="col-lg-10">
      <div class="card card-setup overlay">
        <div class="card-body p-4 p-md-5">
          <div class="d-flex justify-content-between flex-wrap mb-4">
            <div class="step <?php echo $step>=1?'active':''; ?> <?php echo $step>1?'done':''; ?>"><span class="bubble">1</span><span class="label">Requirements</span></div>
            <div class="step <?php echo $step>=2?'active':''; ?> <?php echo $step>2?'done':''; ?>"><span class="bubble">2</span><span class="label">Database</span></div>
            <div class="step <?php echo $step>=3?'active':''; ?> <?php echo $step>3?'done':''; ?>"><span class="bubble">3</span><span class="label">Create Tables</span></div>
            <div class="step <?php echo $step>=4?'active':''; ?> <?php echo $step>4?'done':''; ?>"><span class="bubble">4</span><span class="label">Admin Account</span></div>
            <div class="step <?php echo $step>=5?'active':''; ?> <?php echo $step>5?'done':''; ?>"><span class="bubble">5</span><span class="label">Finish</span></div>
          </div>

          <?php if ($msg = flash('error')): ?>
            <div class="alert alert-danger"><i class="fa-solid fa-triangle-exclamation me-2"></i><?php echo htmlspecialchars($msg); ?></div>
          <?php endif; ?>

          <?php if ($installed && $step < 5): ?>
            <div class="alert alert-warning"><i class="fa-solid fa-circle-info me-2"></i>Setup appears to be completed already. You can still proceed to review steps or <a href="index.php" class="alert-link">go to login</a>.</div>
          <?php endif; ?>

          <?php if ($step === 1): $checks = check_req(); ?>
            <h4 class="mb-3"><i class="fa-solid fa-wand-magic-sparkles me-2 text-primary"></i>Welcome to Attendance System Setup</h4>
            <p class="text-muted">This wizard will guide you through database configuration, creating required tables, and setting the admin account.</p>
            <ul class="list-group mb-4">
              <?php foreach ($checks as $c): $ok=$c[1]; ?>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                  <span><i class="fa-solid <?php echo $ok?'fa-check-circle text-success':'fa-circle-xmark text-danger'; ?> me-2"></i><?php echo htmlspecialchars($c[0]); ?></span>
                  <span class="badge bg-<?php echo $ok?'success':'danger'; ?>"><?php echo $ok?'OK':'Missing'; ?></span>
                </li>
              <?php endforeach; ?>
            </ul>
            <div class="d-flex justify-content-end">
              <a class="btn btn-primary" href="setup.php?step=2"><i class="fa-solid fa-arrow-right me-2"></i>Continue</a>
            </div>
          <?php elseif ($step === 2): ?>
            <h4 class="mb-3"><i class="fa-solid fa-database me-2 text-primary"></i>Database Configuration</h4>
            <form method="post" class="row g-3">
              <div class="col-md-6"><label class="form-label">Host</label><input name="host" class="form-control" value="<?php echo htmlspecialchars($_SESSION['db']['host']); ?>" required></div>
              <div class="col-md-6"><label class="form-label">Database Name</label><input name="name" class="form-control" value="<?php echo htmlspecialchars($_SESSION['db']['name']); ?>" required></div>
              <div class="col-md-6"><label class="form-label">Username</label><input name="user" class="form-control" value="<?php echo htmlspecialchars($_SESSION['db']['user']); ?>" required></div>
              <div class="col-md-6">
                <label class="form-label">Password</label>
                <div class="input-group">
                  <input name="pass" type="password" class="form-control" id="db_password" value="<?php echo htmlspecialchars($_SESSION['db']['pass']); ?>">
                  <span class="input-group-text password-toggle" onclick="togglePassword('db_password')">
                    <i class="fas fa-eye" id="db_password_icon"></i>
                  </span>
                </div>
              </div>
              <div class="d-flex justify-content-between mt-3">
                <a class="btn btn-secondary" href="setup.php?step=1"><i class="fa-solid fa-arrow-left me-2"></i>Back</a>
                <button class="btn btn-primary" name="db_submit"><i class="fa-solid fa-plug me-2"></i>Save & Continue</button>
              </div>
            </form>
          <?php elseif ($step === 3): ?>
            <h4 class="mb-3"><i class="fa-solid fa-table-list me-2 text-primary"></i>Create Required Tables</h4>
            <p class="text-muted">This will create core tables for admin, users, courses, attendance, notifications, and class sessions.</p>
            <?php if (!empty($_SESSION['table_log'])): ?>
              <div class="mb-3"><pre class="bg-light p-3 border rounded" style="max-height:200px;overflow:auto;"><?php echo htmlspecialchars(implode("\n", $_SESSION['table_log'])); unset($_SESSION['table_log']); ?></pre></div>
            <?php endif; ?>
            <form method="post">
              <div class="alert alert-info"><i class="fa-solid fa-circle-info me-2"></i>Make sure your database configuration is saved in step 2.</div>
              <div class="d-flex justify-content-between">
                <a class="btn btn-secondary" href="setup.php?step=2"><i class="fa-solid fa-arrow-left me-2"></i>Back</a>
                <button class="btn btn-success" name="tables_submit"><i class="fa-solid fa-hammer me-2"></i>Create Tables</button>
              </div>
            </form>
          <?php elseif ($step === 4): ?>
            <h4 class="mb-3"><i class="fa-solid fa-user-shield me-2 text-primary"></i>Create Admin Account</h4>
            <?php
              $hasAdmin = false;
              try {
                if (file_exists(__DIR__.'/config/database.php')) {
                  require_once __DIR__.'/config/database.php';
                  if (isset($conn)) {
                    $r=$conn->query("SELECT COUNT(*) c FROM admin");
                    if ($r) {
                      $row=$r->fetch_assoc();
                      $hasAdmin = ((int)$row['c']>0);
                    }
                  }
                }
              } catch (Exception $e) {
                // Ignore errors, just assume no admin exists
              }
            ?>
            <?php if ($hasAdmin): ?>
              <div class="alert alert-success"><i class="fa-solid fa-check me-2"></i>An admin account already exists. You can continue.</div>
              <div class="d-flex justify-content-end"><a class="btn btn-primary" href="setup.php?step=5">Continue</a></div>
            <?php else: ?>
              <form method="post" class="row g-3">
                <div class="col-md-6"><label class="form-label">Username</label><input name="username" class="form-control" required></div>
                <div class="col-md-6"><label class="form-label">Email</label><input name="email" type="email" class="form-control" required></div>
                <div class="col-md-6">
                  <label class="form-label">Password</label>
                  <div class="input-group">
                    <input name="password" type="password" class="form-control" id="admin_password" required>
                    <span class="input-group-text password-toggle" onclick="togglePassword('admin_password')">
                      <i class="fas fa-eye" id="admin_password_icon"></i>
                    </span>
                  </div>
                </div>
                <div class="col-md-6">
                  <label class="form-label">Confirm Password</label>
                  <div class="input-group">
                    <input name="confirm" type="password" class="form-control" id="admin_confirm_password" required>
                    <span class="input-group-text password-toggle" onclick="togglePassword('admin_confirm_password')">
                      <i class="fas fa-eye" id="admin_confirm_password_icon"></i>
                    </span>
                  </div>
                </div>
                <div class="d-flex justify-content-between mt-3">
                  <a class="btn btn-secondary" href="setup.php?step=3"><i class="fa-solid fa-arrow-left me-2"></i>Back</a>
                  <button class="btn btn-primary" name="admin_submit"><i class="fa-solid fa-user-plus me-2"></i>Create Admin</button>
                </div>
              </form>
            <?php endif; ?>
          <?php elseif ($step === 5): ?>
            <div class="text-center py-4">
              <div class="display-6 text-success mb-3"><i class="fa-solid fa-circle-check me-2"></i>Setup Complete</div>
              <p class="lead">Your Enhanced Attendance System is ready with all new features!</p>

              <div class="row mt-4 mb-4">

                <div class="col-md-6">
                  <div class="card border-info">
                    <div class="card-header bg-info text-white">
                      <h6 class="mb-0"><i class="fas fa-cogs me-2"></i>System Features</h6>
                    </div>
                    <div class="card-body">
                      <ul class="list-unstyled mb-0">
                        <li><i class="fas fa-check text-success me-2"></i>QR code attendance system</li>
                        <li><i class="fas fa-check text-success me-2"></i>Department & course management</li>
                        <li><i class="fas fa-check text-success me-2"></i>User approval workflow</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              <div class="d-flex justify-content-center gap-3 mt-3">
                <a class="btn btn-success btn-lg" href="index.php"><i class="fa-solid fa-right-to-bracket me-2"></i>Go to Login</a>
                <a class="btn btn-outline-secondary btn-lg" href="admin_login.php"><i class="fa-solid fa-user-shield me-2"></i>Admin Login</a>
              </div>

              <div class="alert alert-info mt-4">
                <h6><i class="fas fa-info-circle me-2"></i>Next Steps:</h6>
                <ol class="mb-0 text-start">
                  <li>Log in as admin and add departments if needed</li>
                  <li>Approve lecturer and student registrations</li>
                  <li>Lecturers start classes to generate QR codes for attendance</li>
                </ol>
              </div>
            </div>
          <?php endif; ?>
        </div>
      </div>
    </div>
  </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
function togglePassword(fieldId) {
    const passwordField = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '_icon');

    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}
</script>
</body>
</html>

