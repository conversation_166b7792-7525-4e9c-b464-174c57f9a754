<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once '../config/functions.php';
require_once 'auth_check.php';

// Get all attendance records with student, course, lecturer, department, and level details
$attendanceRecords = $conn->query("
    SELECT a.*,
           s.matric_number, s.name as student_name,
           c.course_code, c.course_title,
           l.staff_code, l.name as lecturer_name,
           d.dept_id, d.dept_name,
           lev.level_id, lev.level_name,
           cs.start_time, cs.end_time
    FROM attendance a
    JOIN students s ON a.student_id = s.student_id
    JOIN courses c ON a.course_id = c.course_id
    LEFT JOIN lecturer_courses lc ON a.course_id = lc.course_id
    LEFT JOIN lecturers l ON lc.lecturer_id = l.lecturer_id
    LEFT JOIN class_sessions cs ON a.session_id = cs.session_id
    JOIN departments d ON c.dept_id = d.dept_id
    JOIN levels lev ON c.level_id = lev.level_id
    ORDER BY a.attendance_date DESC, a.attendance_time DESC
");
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Attendance Reports - Ogbonnaya Onu Polytechnic</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <!-- DateRangePicker CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
    <!-- DataTables Buttons CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.bootstrap5.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }

        .card {
            border-radius: 0.5rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .card-header {
            padding: 0.75rem 1.25rem;
        }

        .card-header h5 {
            margin: 0;
            display: flex;
            align-items: center;
        }

        .card-header.bg-primary {
            background-color: #0d6efd !important;
        }

        .card-header.bg-success {
            background-color: #198754 !important;
        }

        .btn-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }

        .btn-success {
            background-color: #198754;
            border-color: #198754;
        }

        .input-group-text {
            background-color: #f8f9fa;
        }

        .table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }

        #exportExcelBtn {
            padding: 0.375rem 1rem;
        }

        .report-header {
            margin-bottom: 20px;
        }

        .report-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .report-subtitle {
            font-size: 1.1rem;
            color: #6c757d;
            margin-bottom: 0.25rem;
        }

        .report-date {
            font-style: italic;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="../images/logo.jpg" alt="Logo" width="30" height="30" class="d-inline-block align-text-top me-2 rounded-circle">
                Admin Dashboard
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Students
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="students.php">All Students</a></li>
                            <li><a class="dropdown-item" href="pending_students.php">Pending Approvals</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Lecturers
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="lecturers.php">All Lecturers</a></li>
                            <li><a class="dropdown-item" href="pending_lecturers.php">Pending Approvals</a></li>
                            <li><a class="dropdown-item" href="assign_courses.php">Assign Courses</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Courses
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="courses.php">All Courses</a></li>
                            <li><a class="dropdown-item" href="add_course.php">Add New Course</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Departments
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="departments.php">All Departments</a></li>
                            <li><a class="dropdown-item" href="add_department.php">Add New Department</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Reports
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item active" href="reports.php">Attendance Reports</a></li>
                            <li><a class="dropdown-item" href="absent_students.php">Absent Students</a></li>
                        </ul>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-1"></i> <?php echo $_SESSION['username']; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profile</a></li>
                            <li><a class="dropdown-item" href="change_password.php">Change Password</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col-md-12">
                <h2>Attendance Reports</h2>
                <p class="text-muted">View and export attendance records</p>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-filter me-2"></i> Filter Options</h5>
            </div>
            <div class="card-body">
                <form id="filterForm">
                    <div class="row align-items-end">
                        <div class="col-md-4 mb-3">
                            <label for="courseFilter" class="form-label">Course</label>
                            <select class="form-select" id="courseFilter" name="courseFilter">
                                <option value="">-- Select Course --</option>
                                <?php
                                $courses = $conn->query("SELECT course_id, course_code, course_title FROM courses ORDER BY course_code");
                                while ($course = $courses->fetch_assoc()) {
                                    echo "<option value='{$course['course_id']}'>{$course['course_code']} - {$course['course_title']}</option>";
                                }
                                ?>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="startDate" class="form-label">Start Date</label>
                            <div class="input-group">
                                <input type="date" class="form-control" id="startDate" name="startDate">
                                <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="endDate" class="form-label">End Date</label>
                            <div class="input-group">
                                <input type="date" class="form-control" id="endDate" name="endDate">
                                <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                            </div>
                        </div>
                        <div class="col-md-2 mb-3">
                            <button type="button" id="viewReportBtn" class="btn btn-primary w-100">
                                <i class="fas fa-search me-2"></i> Generate Report
                            </button>
                        </div>
                    </div>

                    <!-- Advanced filters (collapsed by default) -->
                    <div class="collapse" id="advancedFilters">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="departmentFilter" class="form-label">Department</label>
                                <select class="form-select" id="departmentFilter" name="departmentFilter">
                                    <option value="">All Departments</option>
                                    <?php
                                    $departments = $conn->query("SELECT dept_id, dept_name FROM departments ORDER BY dept_name");
                                    while ($department = $departments->fetch_assoc()) {
                                        echo "<option value='{$department['dept_id']}'>{$department['dept_name']}</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="levelFilter" class="form-label">Level</label>
                                <select class="form-select" id="levelFilter" name="levelFilter">
                                    <option value="">All Levels</option>
                                    <?php
                                    $levels = $conn->query("SELECT level_id, level_name FROM levels ORDER BY level_name");
                                    while ($level = $levels->fetch_assoc()) {
                                        echo "<option value='{$level['level_id']}'>{$level['level_name']}</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="lecturerFilter" class="form-label">Lecturer</label>
                                <select class="form-select" id="lecturerFilter" name="lecturerFilter">
                                    <option value="">All Lecturers</option>
                                    <?php
                                    $lecturers = $conn->query("SELECT lecturer_id, name, staff_code FROM lecturers ORDER BY name");
                                    while ($lecturer = $lecturers->fetch_assoc()) {
                                        echo "<option value='{$lecturer['lecturer_id']}'>{$lecturer['staff_code']} - {$lecturer['name']}</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="d-flex justify-content-between">
                                <button class="btn btn-link text-decoration-none p-0" type="button" data-bs-toggle="collapse" data-bs-target="#advancedFilters">
                                    <i class="fas fa-sliders-h me-1"></i> Advanced Filters
                                </button>
                                <button type="button" id="resetFilterBtn" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-undo me-1"></i> Reset Filters
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i> Report Results</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-end mb-3">
                    <button id="exportExcelBtn" class="btn btn-success">
                        <i class="fas fa-file-excel me-2"></i> Export to Excel
                    </button>
                </div>

                <?php if ($attendanceRecords->num_rows > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="attendanceTable">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Time</th>
                                    <th>Matric Number</th>
                                    <th>Student Name</th>
                                    <th>Course</th>
                                    <th>Department</th>
                                    <th>Level</th>
                                    <th>Status</th>
                                    <th>Marked By</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($record = $attendanceRecords->fetch_assoc()): ?>
                                    <tr>
                                        <td><?php echo date('d M Y', strtotime($record['attendance_date'])); ?></td>
                                        <td><?php echo date('h:i A', strtotime($record['attendance_time'])); ?></td>
                                        <td><?php echo $record['matric_number']; ?></td>
                                        <td><?php echo $record['student_name']; ?></td>
                                        <td><?php echo $record['course_code']; ?></td>
                                        <td><?php echo $record['dept_name']; ?></td>
                                        <td><?php echo $record['level_name']; ?></td>
                                        <td>
                                            <?php if ($record['status'] == 'present'): ?>
                                                <span class="badge bg-success">Present</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">Absent</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php
                                            $marked_by = ucfirst($record['marked_by'] ?? 'student');
                                            echo $marked_by;
                                            ?>
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-danger" onclick="deleteAttendance(<?php echo $record['attendance_id']; ?>);">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> No attendance records found for the selected criteria.
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-3 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; <?php echo date('Y'); ?> Ogbonnaya Onu Polytechnic. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="../about.php" class="text-white me-3">About</a>
                    <a href="../support.php" class="text-white">Support</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <!-- DataTables Buttons JS -->
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.print.min.js"></script>
    <!-- Moment.js -->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <!-- DateRangePicker JS -->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    <script>
        // Global variable for the DataTable
        var dataTable;

        // Format date for input fields
        function formatDateForInput(date) {
            var d = new Date(date),
                month = '' + (d.getMonth() + 1),
                day = '' + d.getDate(),
                year = d.getFullYear();

            if (month.length < 2) month = '0' + month;
            if (day.length < 2) day = '0' + day;

            return [year, month, day].join('-');
        }

        // Function to delete attendance
        function deleteAttendance(attendanceId) {
            console.log('Deleting attendance record with ID: ' + attendanceId);

            if (confirm('Are you sure you want to delete this attendance record? This action cannot be undone.')) {
                // Send AJAX request to delete the attendance record
                $.ajax({
                    url: 'delete_attendance.php',
                    type: 'POST',
                    data: { attendance_id: attendanceId },
                    dataType: 'json',
                    success: function(response) {
                        console.log('Delete response:', response);
                        if (response.success) {
                            // Find the row and remove it
                            var row = $('button[onclick*="' + attendanceId + '"]').closest('tr');
                            dataTable.row(row).remove().draw();

                            // Update the count in the header
                            var filteredCount = dataTable.rows({search:'applied'}).count();
                            var totalCount = dataTable.rows().count();
                            $('.card-header h5').text('Attendance Records (' + filteredCount + ' of ' + totalCount + ' records)');

                            // Show a toast or notification instead of an alert
                            var notification = $('<div class="alert alert-success alert-dismissible fade show" role="alert">' +
                                'Attendance record deleted successfully.' +
                                '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
                                '</div>');

                            // Add the notification at the top of the table
                            $('.card-body').prepend(notification);

                            // Auto-dismiss after 3 seconds
                            setTimeout(function() {
                                notification.alert('close');
                            }, 3000);
                        } else {
                            // Show error message as a toast
                            var errorNotification = $('<div class="alert alert-danger alert-dismissible fade show" role="alert">' +
                                'Error: ' + response.message +
                                '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
                                '</div>');

                            $('.card-body').prepend(errorNotification);

                            setTimeout(function() {
                                errorNotification.alert('close');
                            }, 3000);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Delete error:', xhr, status, error);

                        // Show error message as a toast
                        var errorNotification = $('<div class="alert alert-danger alert-dismissible fade show" role="alert">' +
                            'An error occurred while trying to delete the attendance record.' +
                            '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
                            '</div>');

                        $('.card-body').prepend(errorNotification);

                        setTimeout(function() {
                            errorNotification.alert('close');
                        }, 3000);
                    }
                });
            }
        }

        $(document).ready(function() {
            // Initialize DataTable
            dataTable = $('#attendanceTable').DataTable({
                paging: false,
                searching: false,
                info: false,
                ordering: true,
                autoWidth: false,
                columnDefs: [
                    { className: "text-center", targets: [0, 1, 7] }
                ],
                // After DataTable is initialized
                initComplete: function() {
                    // Show initial record count
                    var totalCount = dataTable.rows().count();
                    $('.card-header h5').text('Report Results (' + totalCount + ' records)');
                }
            });

            // Set default date values (last 30 days)
            var today = new Date();
            var thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(today.getDate() - 30);

            // Set default values
            $('#startDate').val(formatDateForInput(thirtyDaysAgo));
            $('#endDate').val(formatDateForInput(today));

            // VIEW REPORT BUTTON - Apply filters
            $('#viewReportBtn').on('click', function() {
                console.log('View Report button clicked');

                var startDate = $('#startDate').val();
                var endDate = $('#endDate').val();
                var departmentId = $('#departmentFilter').val();
                var levelId = $('#levelFilter').val();
                var courseId = $('#courseFilter').val();
                var lecturerId = $('#lecturerFilter').val();

                console.log('Applying filters:', { startDate, endDate, departmentId, levelId, courseId, lecturerId });

                // Clear any existing search/filter
                dataTable.search('').columns().search('').draw();

                // Remove any existing custom filters
                $.fn.dataTable.ext.search.pop();

                if (startDate && endDate) {
                    // Custom filtering function for date range
                    $.fn.dataTable.ext.search.push(
                        function(settings, data, dataIndex) {
                            var startDateObj = moment(startDate, 'YYYY-MM-DD');
                            var endDateObj = moment(endDate, 'YYYY-MM-DD');
                            var dateCol = moment(data[0], 'DD MMM YYYY');

                            if (dateCol >= startDateObj && dateCol <= endDateObj) {
                                return true;
                            }
                            return false;
                        }
                    );
                }

                // Apply department filter
                if (departmentId) {
                    dataTable.column(4).search(departmentId);
                }

                // Apply level filter
                if (levelId) {
                    dataTable.column(5).search(levelId);
                }

                // Apply course filter
                if (courseId) {
                    dataTable.column(3).search(courseId);
                }

                // Apply lecturer filter
                if (lecturerId) {
                    dataTable.column(6).search(lecturerId);
                }

                // Apply all filters at once
                dataTable.draw();

                // Show count of filtered records
                var filteredCount = dataTable.rows({search:'applied'}).count();
                var totalCount = dataTable.rows().count();

                // Update the card header to show the count
                $('.card-header h5').text('Attendance Records (' + filteredCount + ' of ' + totalCount + ' records)');

                // Show notification
                var notification = $('<div class="alert alert-success alert-dismissible fade show" role="alert">' +
                    'Report generated successfully. Showing ' + filteredCount + ' of ' + totalCount + ' records.' +
                    '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
                    '</div>');

                // Add the notification at the top of the table
                $('.card-body').prepend(notification);

                // Auto-dismiss after 3 seconds
                setTimeout(function() {
                    notification.alert('close');
                }, 3000);
            });

            // RESET BUTTON - Reset filters
            $('#resetFilterBtn').on('click', function() {
                console.log('Reset button clicked');

                // Reset to default date range
                $('#startDate').val(formatDateForInput(thirtyDaysAgo));
                $('#endDate').val(formatDateForInput(today));

                // Reset other filters
                $('#departmentFilter').val('');
                $('#levelFilter').val('');
                $('#courseFilter').val('');
                $('#lecturerFilter').val('');

                // Remove any existing custom filters
                $.fn.dataTable.ext.search.pop();

                // Reset DataTable
                dataTable.search('').columns().search('').draw();

                // Reset the header text
                $('.card-header h5').text('Attendance Records');

                // Reload all courses
                $.ajax({
                    url: 'get_filtered_courses.php',
                    type: 'GET',
                    dataType: 'json',
                    success: function(courses) {
                        $('#courseFilter').html('<option value="">All Courses</option>');
                        if (courses.length > 0) {
                            $.each(courses, function(i, course) {
                                $('#courseFilter').append(
                                    $('<option></option>')
                                        .attr('value', course.course_id)
                                        .text(course.course_code + ' - ' + course.course_title)
                                );
                            });
                        }
                    }
                });

                // Show notification
                var notification = $('<div class="alert alert-info alert-dismissible fade show" role="alert">' +
                    'Filters have been reset.' +
                    '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
                    '</div>');

                // Add the notification at the top of the table
                $('.card-body').prepend(notification);

                // Auto-dismiss after 3 seconds
                setTimeout(function() {
                    notification.alert('close');
                }, 3000);
            });

            // Handle department and level filter changes to update course dropdown
            $('#departmentFilter, #levelFilter').change(function() {
                var departmentId = $('#departmentFilter').val();
                var levelId = $('#levelFilter').val();

                // Only filter courses if at least one filter is selected
                if (departmentId || levelId) {
                    // Save current selection
                    var currentCourseId = $('#courseFilter').val();

                    // Clear and reload course options
                    $('#courseFilter').html('<option value="">All Courses</option>');

                    // Get filtered courses via AJAX
                    $.ajax({
                        url: 'get_filtered_courses.php',
                        type: 'GET',
                        data: {
                            dept_id: departmentId,
                            level_id: levelId
                        },
                        dataType: 'json',
                        success: function(courses) {
                            if (courses.length > 0) {
                                $.each(courses, function(i, course) {
                                    var option = $('<option></option>')
                                        .attr('value', course.course_id)
                                        .text(course.course_code + ' - ' + course.course_title);

                                    // Restore previous selection if it exists in new options
                                    if (course.course_id == currentCourseId) {
                                        option.attr('selected', 'selected');
                                    }

                                    $('#courseFilter').append(option);
                                });
                            }
                        }
                    });
                } else {
                    // If no filters, reload all courses
                    $.ajax({
                        url: 'get_filtered_courses.php',
                        type: 'GET',
                        dataType: 'json',
                        success: function(courses) {
                            $('#courseFilter').html('<option value="">All Courses</option>');
                            if (courses.length > 0) {
                                $.each(courses, function(i, course) {
                                    $('#courseFilter').append(
                                        $('<option></option>')
                                            .attr('value', course.course_id)
                                            .text(course.course_code + ' - ' + course.course_title)
                                    );
                                });
                            }
                        }
                    });
                }
            });

            // Handle Export to Excel button click
            $('#exportExcelBtn').on('click', function() {
                // Create a new Excel export button instance
                var excelBtn = new $.fn.dataTable.Buttons(dataTable, {
                    buttons: [
                        {
                            extend: 'excel',
                            text: 'Export to Excel',
                            title: function() {
                                var courseFilter = $('#courseFilter option:selected').text();
                                var departmentFilter = $('#departmentFilter option:selected').text();
                                var levelFilter = $('#levelFilter option:selected').text();
                                var startDate = $('#startDate').val();
                                var endDate = $('#endDate').val();

                                var title = 'Attendance Report';

                                if (courseFilter && courseFilter !== '-- Select Course --') {
                                    title += ' - ' + courseFilter;
                                }

                                if (departmentFilter && departmentFilter !== 'All Departments') {
                                    title += ' - ' + departmentFilter;
                                }

                                if (levelFilter && levelFilter !== 'All Levels') {
                                    title += ' - ' + levelFilter;
                                }

                                if (startDate && endDate) {
                                    title += ' - From ' + startDate + ' to ' + endDate;
                                }

                                return title;
                            },
                            filename: function() {
                                var date = new Date().toISOString().slice(0, 10);
                                return 'Attendance_Report_' + date;
                            },
                            exportOptions: {
                                columns: [0, 1, 2, 3, 4, 5, 6]
                            },
                            customize: function(xlsx) {
                                // You can customize the Excel file here if needed
                            }
                        }
                    ]
                });

                // Trigger the Excel export
                excelBtn.buttons().trigger();
            });

            // Delete attendance handler is now defined as a global function
        });
    </script>
</body>
</html>
