<?php
require_once '../config/functions.php';
require_once 'auth_check.php';

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set content type to JSON
header('Content-Type: application/json');

// Check if attendance ID is provided
if (!isset($_POST['attendance_id']) || empty($_POST['attendance_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Attendance ID is required'
    ]);
    exit;
}

$attendance_id = sanitize($_POST['attendance_id']);

// Get attendance details before deletion (for logging purposes)
$stmt = $conn->prepare("SELECT a.*, s.name as student_name, s.matric_number, c.course_code, c.course_title
                        FROM attendance a
                        JOIN students s ON a.student_id = s.student_id
                        JOIN courses c ON a.course_id = c.course_id
                        WHERE a.attendance_id = ?");
$stmt->bind_param("i", $attendance_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    echo json_encode([
        'success' => false,
        'message' => 'Attendance record not found'
    ]);
    exit;
}

$attendance = $result->fetch_assoc();

// Delete the attendance record
$stmt = $conn->prepare("DELETE FROM attendance WHERE attendance_id = ?");
$stmt->bind_param("i", $attendance_id);

if ($stmt->execute()) {
    // Log the deletion
    $admin_id = $_SESSION['admin_id'];
    $admin_name = $_SESSION['username'];
    $log_message = "Attendance record deleted by admin {$admin_name} (ID: {$admin_id}). Student: {$attendance['student_name']} ({$attendance['matric_number']}), Course: {$attendance['course_code']} - {$attendance['course_title']}, Date: {$attendance['attendance_date']}, Time: {$attendance['attendance_time']}";

    // Insert into activity log table (if it exists)
    if ($conn->query("SHOW TABLES LIKE 'activity_logs'")->num_rows > 0) {
        $stmt = $conn->prepare("INSERT INTO activity_logs (user_id, user_type, action, details, ip_address) VALUES (?, 'admin', 'delete_attendance', ?, ?)");
        $stmt->bind_param("iss", $admin_id, $log_message, $_SERVER['REMOTE_ADDR']);
        $stmt->execute();
    }

    echo json_encode([
        'success' => true,
        'message' => 'Attendance record deleted successfully'
    ]);
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Failed to delete attendance record: ' . $conn->error
    ]);
}
?>
