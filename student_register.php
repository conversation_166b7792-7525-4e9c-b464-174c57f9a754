<?php
require_once 'config/functions.php';
require_once 'config/security_questions_functions.php';

// Check if user is already logged in
if (isStudentLoggedIn()) {
    redirect('student/index.php');
}

// Ensure security questions tables exist
ensureSecurityQuestionsTables();

// Get departments, levels, and security questions for dropdown
$departments = $conn->query("SELECT * FROM departments ORDER BY dept_name");
$levels = $conn->query("SELECT * FROM levels ORDER BY level_id");
$security_questions = getSecurityQuestions();

// Process registration form
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitize($_POST['name']);
    $matric_number = sanitize($_POST['matric_number']);
    $email = sanitize($_POST['email']);
    $phone = sanitize($_POST['phone']);
    $dept_id = sanitize($_POST['dept_id']);
    $level_id = sanitize($_POST['level_id']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    $security_question1 = sanitize($_POST['security_question1']);
    $security_answer1 = sanitize($_POST['security_answer1']);
    $security_question2 = sanitize($_POST['security_question2']);
    $security_answer2 = sanitize($_POST['security_answer2']);

    // Get current academic session
    $current_session = $conn->query("SELECT semester_id FROM academic_sessions WHERE is_current = 1 LIMIT 1");
    $semester_id = 1; // Default to first semester if no current session set
    if ($current_session && $current_session->num_rows > 0) {
        $semester_id = $current_session->fetch_assoc()['semester_id'];
    }

    // Validate input
    if (empty($name) || empty($matric_number) || empty($email) || empty($phone) || empty($dept_id) ||
        empty($level_id) || empty($password) || empty($confirm_password) ||
        empty($security_question1) || empty($security_answer1) || empty($security_question2) || empty($security_answer2)) {
        $error = 'Please fill in all fields including security questions';
    } elseif ($password !== $confirm_password) {
        $error = 'Passwords do not match';
    } elseif ($security_question1 == $security_question2) {
        $error = 'Please select two different security questions';
    } elseif (!preg_match('/^[0-9]{11}$/', $phone)) {
        $error = 'Phone number must be exactly 11 digits';
    } else {
        // Check if matric number already exists
        $stmt = $conn->prepare("SELECT student_id FROM students WHERE matric_number = ?");
        $stmt->bind_param("s", $matric_number);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $error = 'Matriculation number already exists. If you have already registered, please wait for admin approval.';
        } else {
            // Hash password
            $hashed_password = hashPassword($password);

            // Insert new student
            $stmt = $conn->prepare("INSERT INTO students (name, matric_number, email, phone, password, dept_id, level_id, semester_id, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending')");
            $stmt->bind_param("sssssiis", $name, $matric_number, $email, $phone, $hashed_password, $dept_id, $level_id, $semester_id);

            if ($stmt->execute()) {
                $student_id = $conn->insert_id;

                // Save security questions and answers
                if (saveStudentSecurityAnswers($student_id, $security_question1, $security_answer1, $security_question2, $security_answer2)) {
                    // Create notification for admin about new student registration
                    $admin_message = "New student registration: $name ($matric_number) has registered and requires approval.";
                    createNotification(1, 'admin', $admin_message, $student_id, 'student_registration');

                    $success = 'Registration successful! Your account is pending approval by the administrator.';
                } else {
                    // If security answers failed to save, delete the student record
                    $conn->prepare("DELETE FROM students WHERE student_id = ?")->execute([$student_id]);
                    $error = 'Registration failed: Could not save security questions. Please try again.';
                }
            } else {
                $error = 'Registration failed: ' . $conn->error;
            }
        }
    }
}
?>

<?php include 'includes/header.php'; ?>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3 class="text-center">Student Registration</h3>
            </div>
            <div class="card-body">
                <?php if (!empty($success)): ?>
                    <div class="alert alert-success"><?php echo $success; ?></div>
                    <div class="text-center">
                        <a href="index.php" class="btn btn-primary">Back to Login</a>
                    </div>
                <?php else: ?>
                    <?php if (!empty($error)): ?>
                        <div class="alert alert-danger"><?php echo $error; ?></div>
                    <?php endif; ?>

                    <form method="POST" action="" id="registration-form">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Full Name</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="matric_number" class="form-label">Matriculation Number</label>
                                <input type="text" class="form-control" id="matric_number" name="matric_number" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="phone" name="phone" pattern="[0-9]{11}" maxlength="11" placeholder="Enter 11-digit phone number" required>
                                <div class="form-text">Please enter exactly 11 digits (e.g., 08012345678)</div>
                                <div id="phone-feedback" class="invalid-feedback" style="display: none;">
                                    Phone number must be exactly 11 digits
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="dept_id" class="form-label">Department</label>
                                <select class="form-select" id="dept_id" name="dept_id" required>
                                    <option value="">Select Department</option>
                                    <?php while ($dept = $departments->fetch_assoc()): ?>
                                        <option value="<?php echo $dept['dept_id']; ?>"><?php echo $dept['dept_name']; ?></option>
                                    <?php endwhile; ?>
                                </select>
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="level_id" class="form-label">Level</label>
                                <select class="form-select" id="level_id" name="level_id" required>
                                    <option value="">Select Level</option>
                                    <?php while ($level = $levels->fetch_assoc()): ?>
                                        <option value="<?php echo $level['level_id']; ?>"><?php echo $level['level_name']; ?></option>
                                    <?php endwhile; ?>
                                </select>
                            </div>


                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">Password</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="password" name="password" required>
                                    <span class="input-group-text password-toggle"><i class="fas fa-eye"></i></span>
                                </div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="confirm_password" class="form-label">Confirm Password</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                    <span class="input-group-text password-toggle"><i class="fas fa-eye"></i></span>
                                </div>
                                <div class="invalid-feedback" id="password-error"></div>
                            </div>
                        </div>

                        <!-- Security Questions Section -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h6 class="mb-0">Security Questions</h6>
                                <small class="text-muted">These questions will help you reset your password if you forget it</small>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="security_question1" class="form-label">Security Question 1</label>
                                        <select class="form-select" id="security_question1" name="security_question1" required>
                                            <option value="">Select a question</option>
                                            <?php foreach ($security_questions as $question): ?>
                                                <option value="<?php echo $question['question_id']; ?>"><?php echo htmlspecialchars($question['question_text']); ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="security_answer1" class="form-label">Answer 1</label>
                                        <input type="text" class="form-control" id="security_answer1" name="security_answer1" required>
                                        <small class="text-muted">Answer is case-insensitive</small>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="security_question2" class="form-label">Security Question 2</label>
                                        <select class="form-select" id="security_question2" name="security_question2" required>
                                            <option value="">Select a different question</option>
                                            <?php foreach ($security_questions as $question): ?>
                                                <option value="<?php echo $question['question_id']; ?>"><?php echo htmlspecialchars($question['question_text']); ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="security_answer2" class="form-label">Answer 2</label>
                                        <input type="text" class="form-control" id="security_answer2" name="security_answer2" required>
                                        <small class="text-muted">Answer is case-insensitive</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 mt-4">
                            <button type="submit" class="btn btn-primary">Register</button>
                            <a href="index.php" class="btn btn-secondary">Back to Login</a>
                        </div>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const phoneInput = document.getElementById('phone');

    // Only allow numbers and provide visual feedback
    phoneInput.addEventListener('input', function(e) {
        // Remove any non-digit characters
        let value = e.target.value.replace(/\D/g, '');

        // Limit to 11 digits
        if (value.length > 11) {
            value = value.slice(0, 11);
        }

        e.target.value = value;

        // Show validation feedback
        const feedback = document.getElementById('phone-feedback');
        if (value.length === 11) {
            e.target.classList.remove('is-invalid');
            e.target.classList.add('is-valid');
            if (feedback) feedback.style.display = 'none';
        } else {
            e.target.classList.remove('is-valid');
            e.target.classList.add('is-invalid');
            if (feedback) feedback.style.display = 'block';
        }
    });

    // Form validation
    const form = document.getElementById('registration-form');
    form.addEventListener('submit', function(e) {
        if (phoneInput.value.length !== 11) {
            e.preventDefault();
            alert('Phone number must be exactly 11 digits');
            phoneInput.focus();
            return;
        }

        // Validate security questions
        const question1 = document.getElementById('security_question1').value;
        const question2 = document.getElementById('security_question2').value;

        if (question1 === question2 && question1 !== '') {
            e.preventDefault();
            alert('Please select two different security questions');
            document.getElementById('security_question2').focus();
            return;
        }
    });

    // Password toggle
    const passwordToggles = document.querySelectorAll('.password-toggle');
    passwordToggles.forEach(function(toggle) {
        toggle.addEventListener('click', function() {
            const input = this.previousElementSibling;
            const icon = this.querySelector('i');

            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?>
