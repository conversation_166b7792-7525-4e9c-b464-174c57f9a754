<?php
// Session configuration
// Set session to expire when browser is closed
ini_set('session.cookie_lifetime', '0');

// Set session to use cookies only (no URL-based sessions)
ini_set('session.use_only_cookies', '1');

// Set session to use strict mode (prevents uninitialized session ID)
ini_set('session.use_strict_mode', '1');

// Set session to use HTTP only cookies (prevents JavaScript access)
ini_set('session.cookie_httponly', '1');

// Set session garbage collection probability
ini_set('session.gc_probability', '1');
ini_set('session.gc_divisor', '100');

// Set session garbage collection lifetime (30 minutes for better UX)
ini_set('session.gc_maxlifetime', '1800');

// Set session cookie parameters before starting the session
$lifetime = 0; // 0 = until browser is closed
$path = '/'; // Available in whole domain
$domain = ''; // Current domain
$secure = false; // Set to true if using HTTPS
$httponly = true; // Prevent JavaScript access to session cookie

// Set the session cookie parameters
session_set_cookie_params($lifetime, $path, $domain, $secure, $httponly);

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Force session regeneration to prevent session fixation (every 30 minutes)
if (!isset($_SESSION['last_regeneration']) || (time() - $_SESSION['last_regeneration']) > 1800) { // 30 minutes
    session_regenerate_id(true);
    $_SESSION['last_regeneration'] = time();
}
?>
