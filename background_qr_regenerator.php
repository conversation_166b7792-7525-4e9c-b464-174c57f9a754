<?php
/**
 * Background QR Code Regenerator
 * This script runs automatically to regenerate QR codes for courses
 * Can be run via cron job or called via AJAX
 */

// Prevent direct browser access unless it's an AJAX call or CLI
if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) && php_sapi_name() !== 'cli') {
    // Allow access only if called with a secret key
    if (!isset($_GET['secret']) || $_GET['secret'] !== 'qr_regen_2025') {
        http_response_code(403);
        die('Access denied');
    }
}

require_once 'config/functions.php';

// Set execution time limit for background processing
set_time_limit(300); // 5 minutes max

// Log function
function logMessage($message) {
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[$timestamp] $message" . PHP_EOL;
    file_put_contents('logs/qr_regeneration.log', $log_entry, FILE_APPEND | LOCK_EX);
}

// Create logs directory if it doesn't exist
if (!is_dir('logs')) {
    mkdir('logs', 0755, true);
}

logMessage("QR Code regeneration started");

try {
    // Get all courses that need QR code regeneration
    $courses = $conn->query("
        SELECT c.*, d.dept_name, l.level_name, s.semester_name
        FROM courses c
        JOIN departments d ON c.dept_id = d.dept_id
        JOIN levels l ON c.level_id = l.level_id
        JOIN semesters s ON c.semester_id = s.semester_id
        ORDER BY c.course_id
    ");

    $regenerated_count = 0;
    $error_count = 0;

    while ($course = $courses->fetch_assoc()) {
        try {
            // Generate QR code URL for attendance
            $attendance_url = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/student/scan_qr.php?course_id=" . $course['course_id'];
            
            // Generate QR code
            $qr_result = generateQRCode($attendance_url);
            
            if ($qr_result['success']) {
                // Update course with new QR code path
                $stmt = $conn->prepare("UPDATE courses SET qr_code = ?, qr_generated_at = NOW() WHERE course_id = ?");
                $stmt->bind_param("si", $qr_result['qr_path'], $course['course_id']);
                
                if ($stmt->execute()) {
                    $regenerated_count++;
                    logMessage("QR code regenerated for course: {$course['course_code']} - {$course['course_title']}");
                } else {
                    $error_count++;
                    logMessage("Failed to update database for course: {$course['course_code']} - Error: " . $conn->error);
                }
            } else {
                $error_count++;
                logMessage("Failed to generate QR code for course: {$course['course_code']} - Error: " . $qr_result['error']);
            }
            
            // Small delay to prevent server overload
            usleep(100000); // 0.1 second delay
            
        } catch (Exception $e) {
            $error_count++;
            logMessage("Exception while processing course {$course['course_code']}: " . $e->getMessage());
        }
    }

    // Update last regeneration timestamp
    $stmt = $conn->prepare("
        INSERT INTO system_settings (setting_key, setting_value) 
        VALUES ('last_qr_regeneration', NOW()) 
        ON DUPLICATE KEY UPDATE setting_value = NOW()
    ");
    $stmt->execute();

    $summary = "QR Code regeneration completed. Regenerated: $regenerated_count, Errors: $error_count";
    logMessage($summary);

    // Return JSON response for AJAX calls
    if (isset($_SERVER['HTTP_X_REQUESTED_WITH'])) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'message' => $summary,
            'regenerated' => $regenerated_count,
            'errors' => $error_count
        ]);
    } else {
        echo $summary . "\n";
    }

} catch (Exception $e) {
    $error_message = "Fatal error during QR regeneration: " . $e->getMessage();
    logMessage($error_message);
    
    if (isset($_SERVER['HTTP_X_REQUESTED_WITH'])) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => $error_message
        ]);
    } else {
        echo $error_message . "\n";
    }
}
?>
