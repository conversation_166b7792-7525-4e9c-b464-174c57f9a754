# Disable directory listing
Options -Indexes

# Protect .htaccess file
<Files .htaccess>
    Order Allow,<PERSON>y
    <PERSON> from all
</Files>

# Protect config files
<FilesMatch "^(database\.php|functions\.php)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Protect database directory
<FilesMatch "^(.*\.sql)$">
    Order Allow,Deny
    <PERSON> from all
</FilesMatch>

# Enable PHP error reporting
php_flag display_errors on
php_value error_reporting E_ALL

# Set default character set
AddDefaultCharset UTF-8

# Protect against XSS attacks
<IfModule mod_headers.c>
    Header set X-XSS-Protection "1; mode=block"
    Header set X-Content-Type-Options "nosniff"
    Header set X-Frame-Options "SAMEORIGIN"
    Header set Content-Security-Policy "default-src 'self'; script-src 'self' https://cdn.jsdelivr.net https://code.jquery.com https://unpkg.com 'unsafe-inline'; style-src 'self' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com 'unsafe-inline'; img-src 'self' https://chart.googleapis.com data:; font-src 'self' https://cdnjs.cloudflare.com; connect-src 'self';"
</IfModule>

# Redirect to HTTPS if available
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteCond %{HTTPS} off
    RewriteCond %{HTTP_HOST} !^localhost [NC]
    RewriteCond %{HTTP_HOST} !^127\.0\.0\.1 [NC]
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</IfModule>
