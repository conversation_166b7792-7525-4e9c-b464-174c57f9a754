<?php
require_once '../config/functions.php';

// Check if student is logged in
if (!isStudentLoggedIn()) {
    redirect('../index.php');
}

// Get student details
$student_id = $_SESSION['student_id'];
$student = getStudentDetails($student_id);

// Check if session_id is provided
$session_id = isset($_GET['session_id']) ? intval($_GET['session_id']) : null;
$active_session = null;

if ($session_id) {
    // Get session details
    $stmt = $conn->prepare("
        SELECT cs.*, c.course_code, c.course_title, c.course_id, l.name as lecturer_name
        FROM class_sessions cs
        JOIN courses c ON cs.course_id = c.course_id
        JOIN lecturers l ON cs.lecturer_id = l.lecturer_id
        WHERE cs.session_id = ? AND cs.status = 'active'
    ");
    $stmt->bind_param("i", $session_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $active_session = $result->fetch_assoc();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scan QR Code - Ogbonnaya Onu Polytechnic</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="../images/logo.jpg" alt="Logo" width="30" height="30" class="d-inline-block align-text-top me-2 rounded-circle">
                Student Dashboard
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">My Courses</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="attendance.php">My Attendance</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="scan.php">Scan QR Code</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-1"></i> <?php echo $student['name']; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profile</a></li>
                            <li><a class="dropdown-item" href="change_password.php">Change Password</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col-md-12">
                <h2>Scan QR Code for Attendance</h2>
                <p class="text-muted">Use your device's camera to scan the QR code provided by your lecturer</p>
            </div>
        </div>

        <?php if ($active_session): ?>
        <!-- Active Class Session Info -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card border-danger">
                    <div class="card-header bg-danger text-white">
                        <h5><i class="fas fa-bell me-2"></i> Active Class Session</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i> Please mark your attendance for the following class:
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <p><strong>Course:</strong> <?php echo $active_session['course_code'] . ' - ' . $active_session['course_title']; ?></p>
                                <p><strong>Lecturer:</strong> <?php echo $active_session['lecturer_name']; ?></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Started At:</strong> <?php echo date('h:i A', strtotime($active_session['start_time'])); ?></p>
                                <p><strong>Date:</strong> <?php echo date('d M Y', strtotime($active_session['start_time'])); ?></p>
                            </div>
                        </div>

                        <?php
                        // Check if student has already marked attendance
                        $attended = hasMarkedAttendance($student_id, $active_session['session_id']);
                        if ($attended):
                        ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i> You have already marked your attendance for this class.
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> Please scan the QR code below to mark your attendance.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5>QR Code Scanner</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> Position the QR code within the scanner frame to mark your attendance.
                        </div>

                        <div class="scanner-container mb-4">
                            <div id="qr-reader"></div>
                        </div>

                        <div id="scan-result"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-3 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; <?php echo date('Y'); ?> Ogbonnaya Onu Polytechnic. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="../about.php" class="text-white me-3">About</a>
                    <a href="../contact.php" class="text-white">Contact</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- QR Code Scanner -->
    <script src="https://unpkg.com/html5-qrcode"></script>
    <!-- Custom JS -->
    <script src="../assets/js/script.js"></script>

    <script>
        $(document).ready(function() {
            const html5QrCode = new Html5Qrcode("qr-reader");
            const qrCodeSuccessCallback = (decodedText, decodedResult) => {
                // Stop scanning
                html5QrCode.stop().then(() => {
                    console.log('QR Code scanning stopped.');
                    processQrCode(decodedText);
                }).catch((err) => {
                    console.error('Failed to stop QR Code scanning.', err);
                });
            };

            const config = { fps: 10, qrbox: { width: 250, height: 250 } };

            // Start scanning
            html5QrCode.start(
                { facingMode: "environment" },
                config,
                qrCodeSuccessCallback
            ).catch(err => {
                // Show camera permission error
                $('#scan-result').html(`
                    <div class="alert alert-danger">
                        <strong>Error:</strong> Camera access denied or not available.
                        Please allow camera access and try again.
                    </div>
                    <button class="btn btn-primary" onclick="location.reload()">
                        <i class="fas fa-sync-alt"></i> Try Again
                    </button>
                `);
            });

            // Process QR code data and mark attendance
            function processQrCode(qrData) {
                try {
                    // Try to parse the QR data as JSON
                    let jsonData;
                    try {
                        jsonData = JSON.parse(qrData);
                    } catch (e) {
                        // If not valid JSON, show error
                        showError("Invalid QR code format. Please scan a valid attendance QR code.");
                        return;
                    }

                    // Show processing message
                    $('#scan-result').html(`
                        <div class="alert alert-info">
                            <i class="fas fa-spinner fa-spin me-2"></i> Processing your attendance...
                        </div>
                    `);

                    // Prepare data to send
                    let postData = { qr_data: qrData };

                    // Add session_id if available
                    <?php if ($session_id): ?>
                    postData.session_id = <?php echo $session_id; ?>;
                    <?php endif; ?>

                    // Send data to server to mark attendance
                    $.ajax({
                        url: '../mark_attendance.php',
                        type: 'POST',
                        data: postData,
                        dataType: 'json',
                        success: function(response) {
                            if (response.success) {
                                // Show success message
                                $('#scan-result').html(`
                                    <div class="alert alert-success">
                                        <h5><i class="fas fa-check-circle me-2"></i> Attendance Marked Successfully!</h5>
                                        <p><strong>Course:</strong> ${response.course}</p>
                                        <p><strong>Student:</strong> ${response.student}</p>
                                        <p><strong>Date:</strong> ${response.date}</p>
                                        <p><strong>Time:</strong> ${response.time}</p>
                                    </div>
                                    <div class="d-grid gap-2">
                                        <a href="attendance.php" class="btn btn-primary">
                                            <i class="fas fa-clipboard-check me-2"></i> View My Attendance
                                        </a>
                                        <button class="btn btn-secondary" onclick="location.reload()">
                                            <i class="fas fa-qrcode me-2"></i> Scan Another QR Code
                                        </button>
                                    </div>
                                `);

                                // If this was for an active session, redirect back to dashboard
                                <?php if ($session_id): ?>
                                // Redirect back to dashboard after 3 seconds
                                setTimeout(function() {
                                    window.location.href = 'index.php';
                                }, 3000);
                                <?php endif; ?>
                            } else {
                                // Show error message
                                showError(response.message || "Failed to mark attendance. Please try again.");
                            }
                        },
                        error: function() {
                            // Show error message
                            showError("Connection error. Please check your internet connection and try again.");
                        }
                    });
                } catch (error) {
                    // Show error message
                    showError("An error occurred while processing the QR code. Please try again.");
                    console.error(error);
                }
            }

            // Helper function to show error messages
            function showError(message) {
                $('#scan-result').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i> <strong>Error:</strong> ${message}
                    </div>
                    <button class="btn btn-primary" onclick="location.reload()">
                        <i class="fas fa-sync-alt me-2"></i> Try Again
                    </button>
                `);
            }
        });
    </script>
</body>
</html>
