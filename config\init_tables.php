<?php
// This file initializes database tables if they don't exist
// It should be included in functions.php

// Create admin table if it doesn't exist
$result = $conn->query("SHOW TABLES LIKE 'admin'");
if ($result->num_rows == 0) {
    $sql = "CREATE TABLE admin (
        admin_id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";

    if ($conn->query($sql)) {
        // Insert default admin user (username: admin, password: admin@2023)
        $username = "admin";
        $password = password_hash("admin@2023", PASSWORD_DEFAULT);

        $sql = "INSERT INTO admin (username, password) VALUES ('$username', '$password')";
        $conn->query($sql);
    }
}

// Create departments table if it doesn't exist
$result = $conn->query("SHOW TABLES LIKE 'departments'");
if ($result->num_rows == 0) {
    $sql = "CREATE TABLE departments (
        dept_id INT AUTO_INCREMENT PRIMARY KEY,
        dept_name VARCHAR(100) NOT NULL
    )";

    if ($conn->query($sql)) {
        // Insert Computer Science department for the hardcoded courses
        $conn->query("INSERT IGNORE INTO departments (dept_name) VALUES ('Computer Science')");
    }
}

// Create levels table if it doesn't exist
$result = $conn->query("SHOW TABLES LIKE 'levels'");
if ($result->num_rows == 0) {
    $sql = "CREATE TABLE levels (
        level_id INT AUTO_INCREMENT PRIMARY KEY,
        level_name VARCHAR(50) NOT NULL
    )";

    $conn->query($sql);
}

// Create semesters table if it doesn't exist
$result = $conn->query("SHOW TABLES LIKE 'semesters'");
if ($result->num_rows == 0) {
    $sql = "CREATE TABLE semesters (
        semester_id INT AUTO_INCREMENT PRIMARY KEY,
        semester_name VARCHAR(50) NOT NULL
    )";

    $conn->query($sql);
}

// Create courses table if it doesn't exist
$result = $conn->query("SHOW TABLES LIKE 'courses'");
if ($result->num_rows == 0) {
    $sql = "CREATE TABLE courses (
        course_id INT AUTO_INCREMENT PRIMARY KEY,
        course_code VARCHAR(20) NOT NULL,
        course_title VARCHAR(100) NOT NULL,
        dept_id INT NOT NULL,
        level_id INT NOT NULL,
        semester_id INT NOT NULL,
        qr_code VARCHAR(255),
        FOREIGN KEY (dept_id) REFERENCES departments(dept_id),
        FOREIGN KEY (level_id) REFERENCES levels(level_id),
        FOREIGN KEY (semester_id) REFERENCES semesters(semester_id)
    )";

    $conn->query($sql);
}

// Add qr_generated_at column to courses table if it doesn't exist
$result = $conn->query("SHOW COLUMNS FROM courses LIKE 'qr_generated_at'");
if ($result->num_rows == 0) {
    $conn->query("ALTER TABLE courses ADD COLUMN qr_generated_at TIMESTAMP NULL");
}

// Create students table if it doesn't exist
$result = $conn->query("SHOW TABLES LIKE 'students'");
if ($result->num_rows == 0) {
    $sql = "CREATE TABLE students (
        student_id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        matric_number VARCHAR(50) NOT NULL UNIQUE,
        email VARCHAR(100) NOT NULL UNIQUE,
        phone VARCHAR(20),
        password VARCHAR(255) NOT NULL,
        dept_id INT NOT NULL,
        level_id INT NOT NULL,
        semester_id INT NOT NULL,
        status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (dept_id) REFERENCES departments(dept_id),
        FOREIGN KEY (level_id) REFERENCES levels(level_id),
        FOREIGN KEY (semester_id) REFERENCES semesters(semester_id)
    )";

    $conn->query($sql);
}

// Create lecturers table if it doesn't exist
$result = $conn->query("SHOW TABLES LIKE 'lecturers'");
if ($result->num_rows == 0) {
    $sql = "CREATE TABLE lecturers (
        lecturer_id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        staff_code VARCHAR(50) NOT NULL UNIQUE,
        email VARCHAR(100) NOT NULL UNIQUE,
        phone VARCHAR(20),
        password VARCHAR(255) NOT NULL,
        dept_id INT NOT NULL,
        status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (dept_id) REFERENCES departments(dept_id)
    )";

    $conn->query($sql);
}

// Create attendance table if it doesn't exist
$result = $conn->query("SHOW TABLES LIKE 'attendance'");
if ($result->num_rows == 0) {
    $sql = "CREATE TABLE attendance (
        attendance_id INT AUTO_INCREMENT PRIMARY KEY,
        student_id INT NOT NULL,
        course_id INT NOT NULL,
        session_id INT NULL,
        attendance_date DATE NOT NULL,
        attendance_time TIME NOT NULL,
        status ENUM('present', 'absent') DEFAULT 'present',
        marked_by ENUM('student', 'lecturer', 'system') DEFAULT 'student',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (student_id) REFERENCES students(student_id),
        FOREIGN KEY (course_id) REFERENCES courses(course_id),
        FOREIGN KEY (session_id) REFERENCES class_sessions(session_id)
    )";

    $conn->query($sql);
}

// Add missing columns to attendance table if they don't exist
$columns_to_add = [
    'session_id' => 'INT NULL',
    'status' => "ENUM('present', 'absent') DEFAULT 'present'",
    'marked_by' => "ENUM('student', 'lecturer', 'system') DEFAULT 'student'",
    'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
];

foreach ($columns_to_add as $column => $definition) {
    $result = $conn->query("SHOW COLUMNS FROM attendance LIKE '$column'");
    if ($result->num_rows == 0) {
        $conn->query("ALTER TABLE attendance ADD COLUMN $column $definition");
    }
}

// Update existing attendance records to have proper status if they don't have one
$conn->query("UPDATE attendance SET status = 'present' WHERE status IS NULL OR status = ''");
$conn->query("UPDATE attendance SET marked_by = 'student' WHERE marked_by IS NULL OR marked_by = ''");

// Students table created - admin will add students manually

// Courses table created - admin will add courses manually
// However, Computer Science HND1 courses are hardcoded for the system
$result = $conn->query("SELECT COUNT(*) as count FROM courses");
if ($result) {
    $count = $result->fetch_assoc()['count'];

    if ($count == 0) {
        // Check if Computer Science department exists
        $cs_dept_result = $conn->query("SELECT dept_id FROM departments WHERE dept_name = 'Computer Science'");
        if ($cs_dept_result && $cs_dept_result->num_rows > 0) {
            $cs_dept_id = $cs_dept_result->fetch_assoc()['dept_id'];
            $hnd2_level_id = 2; // HND2

            // First Semester Courses
            $first_semester_courses = [
                ['COM405', 'RESEARCH METHODOLOGY'],
                ['COM411', 'WEB DEVELOPMENT (PHP)'],
                ['COM412', 'PROJECT MANAGEMENT'],
                ['COM413', 'COMPILER CONSTRUCTION'],
                ['COM414', 'DATA COMMUNICATION AND NETWORKING'],
                ['COM415', 'MULTIMEDIA'],
                ['GNS401', 'COMMUNICATION IN ENGLISH IV'],
                ['ENT416', 'ENTREPRENEURSHIP EDUCATION']
            ];

            foreach ($first_semester_courses as $course) {
                $course_code = $conn->real_escape_string($course[0]);
                $course_title = $conn->real_escape_string($course[1]);

                // Generate QR code for the course
                $qr_data = json_encode([
                    'course_code' => $course[0],
                    'course_title' => $course[1],
                    'dept_id' => $cs_dept_id,
                    'level_id' => $hnd2_level_id,
                    'semester_id' => 1,
                    'timestamp' => time()
                ]);
                $qr_code = generateQRCode($qr_data);

                $conn->query("INSERT IGNORE INTO courses (course_code, course_title, dept_id, level_id, semester_id, qr_code) VALUES ('$course_code', '$course_title', $cs_dept_id, $hnd2_level_id, 1, '$qr_code')");
            }

            // Second Semester Courses
            $second_semester_courses = [
                ['COM423', 'EXPERT SYSTEM & MACHINE LEARNING'],
                ['COM422', 'COMPUTER GRAPHICS & ANIMATION'],
                ['COM426', 'COMPUTER SECURITY'],
                ['COM424', 'ETHICAL & PROFESSIONAL PRACTICE IN I.T.']
            ];

            foreach ($second_semester_courses as $course) {
                $course_code = $conn->real_escape_string($course[0]);
                $course_title = $conn->real_escape_string($course[1]);

                // Generate QR code for the course
                $qr_data = json_encode([
                    'course_code' => $course[0],
                    'course_title' => $course[1],
                    'dept_id' => $cs_dept_id,
                    'level_id' => $hnd2_level_id,
                    'semester_id' => 2,
                    'timestamp' => time()
                ]);
                $qr_code = generateQRCode($qr_data);

                $conn->query("INSERT IGNORE INTO courses (course_code, course_title, dept_id, level_id, semester_id, qr_code) VALUES ('$course_code', '$course_title', $cs_dept_id, $hnd2_level_id, 2, '$qr_code')");
            }
        }
    }
}
// Old password_reset_requests table is no longer needed - using security questions instead

// Create notifications table if it doesn't exist
$result = $conn->query("SHOW TABLES LIKE 'notifications'");
if ($result->num_rows == 0) {
    $sql = "CREATE TABLE notifications (
        notification_id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        user_type ENUM('student', 'lecturer', 'admin') NOT NULL,
        message TEXT NOT NULL,
        is_read TINYINT(1) DEFAULT 0,
        related_id INT NULL,
        related_type VARCHAR(50) NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";

    $conn->query($sql);
}

// Create class_sessions table if it doesn't exist
$result = $conn->query("SHOW TABLES LIKE 'class_sessions'");
if ($result->num_rows == 0) {
    $sql = "CREATE TABLE class_sessions (
        session_id INT AUTO_INCREMENT PRIMARY KEY,
        course_id INT NOT NULL,
        lecturer_id INT NOT NULL,
        start_time DATETIME NOT NULL,
        end_time DATETIME NULL,
        qr_code VARCHAR(255) NULL,
        status ENUM('active', 'ended') DEFAULT 'active',
        FOREIGN KEY (course_id) REFERENCES courses(course_id),
        FOREIGN KEY (lecturer_id) REFERENCES lecturers(lecturer_id)
    )";

    $conn->query($sql);
}

// Create lecturer_courses table if it doesn't exist
$result = $conn->query("SHOW TABLES LIKE 'lecturer_courses'");
if ($result->num_rows == 0) {
    $sql = "CREATE TABLE lecturer_courses (
        id INT AUTO_INCREMENT PRIMARY KEY,
        lecturer_id INT NOT NULL,
        course_id INT NOT NULL,
        FOREIGN KEY (lecturer_id) REFERENCES lecturers(lecturer_id),
        FOREIGN KEY (course_id) REFERENCES courses(course_id),
        UNIQUE KEY (lecturer_id, course_id)
    )";

    $conn->query($sql);
}

// Timetable-related tables have been removed to simplify the system

// Create academic_sessions table if it doesn't exist
$result = $conn->query("SHOW TABLES LIKE 'academic_sessions'");
if ($result->num_rows == 0) {
    $sql = "CREATE TABLE academic_sessions (
        session_id INT AUTO_INCREMENT PRIMARY KEY,
        academic_year VARCHAR(20) NOT NULL,
        semester_id INT NOT NULL,
        is_current TINYINT(1) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (semester_id) REFERENCES semesters(semester_id)
    )";

    if ($conn->query($sql)) {
        // Insert default academic session (2024/2025 First Semester)
        $conn->query("INSERT INTO academic_sessions (academic_year, semester_id, is_current) VALUES ('2024/2025', 1, 1)");
    }
}

// Create system_settings table if it doesn't exist
$result = $conn->query("SHOW TABLES LIKE 'system_settings'");
if ($result->num_rows == 0) {
    $sql = "CREATE TABLE system_settings (
        setting_id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";

    if ($conn->query($sql) === TRUE) {
        // Insert default settings
        $conn->query("INSERT INTO system_settings (setting_key, setting_value) VALUES
            ('qr_regeneration_interval', '3600'),
            ('auto_qr_regeneration', '1'),
            ('last_qr_regeneration', NOW())
        ");
    }
}

// Security questions tables are now created during setup.php or can be run manually
// require_once __DIR__ . '/create_security_questions_tables.php';
?>
