<?php
require_once '../config/functions.php';
require_once 'auth_check.php';

// Handle mark as read actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['mark_read'])) {
        $notification_id = sanitize($_POST['notification_id']);
        if (markNotificationAsRead($notification_id)) {
            $message = displaySuccess("Notification marked as read!");
        } else {
            $message = displayError("Error marking notification as read.");
        }
    } elseif (isset($_POST['mark_all_read'])) {
        if (markAllAdminNotificationsAsRead()) {
            $message = displaySuccess("All notifications marked as read!");
        } else {
            $message = displayError("Error marking all notifications as read.");
        }
    }
}

// Get all admin notifications
$notifications = getAdminNotifications(1, 50); // Get last 50 notifications
$unreadCount = getAdminNotificationCount();
$unreadNotifications = $unreadCount; // For navbar
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notifications - Admin Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <style>
        .notification-item {
            transition: all 0.3s ease;
        }
        .notification-item:hover {
            background-color: #f8f9fa;
        }
        .notification-unread {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        .notification-read {
            background-color: #ffffff;
            border-left: 4px solid #e0e0e0;
        }
        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }
        .notification-time {
            font-size: 0.85em;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2 class="mb-1">
                            <i class="fas fa-bell me-2 text-primary"></i>Notifications
                        </h2>
                        <p class="text-muted mb-0">
                            <?php echo $unreadCount; ?> unread notification<?php echo $unreadCount != 1 ? 's' : ''; ?>
                        </p>
                    </div>
                    <?php if ($unreadCount > 0): ?>
                        <form method="POST" action="" class="d-inline">
                            <button type="submit" name="mark_all_read" class="btn btn-outline-primary">
                                <i class="fas fa-check-double me-1"></i>Mark All as Read
                            </button>
                        </form>
                    <?php endif; ?>
                </div>

                <?php if (isset($message)) echo $message; ?>

                <div class="card">
                    <div class="card-body p-0">
                        <?php if ($notifications->num_rows > 0): ?>
                            <div class="list-group list-group-flush">
                                <?php while ($notification = $notifications->fetch_assoc()): ?>
                                    <div class="list-group-item notification-item <?php echo $notification['is_read'] ? 'notification-read' : 'notification-unread'; ?>">
                                        <div class="d-flex align-items-start">
                                            <div class="notification-icon bg-<?php echo $notification['is_read'] ? 'secondary' : 'primary'; ?> text-white me-3">
                                                <?php if ($notification['related_type'] == 'lecturer_registration'): ?>
                                                    <i class="fas fa-chalkboard-teacher"></i>
                                                <?php elseif ($notification['related_type'] == 'student_registration'): ?>
                                                    <i class="fas fa-user-graduate"></i>
                                                <?php else: ?>
                                                    <i class="fas fa-info-circle"></i>
                                                <?php endif; ?>
                                            </div>
                                            <div class="flex-grow-1">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div>
                                                        <p class="mb-1 <?php echo $notification['is_read'] ? '' : 'fw-bold'; ?>">
                                                            <?php echo htmlspecialchars($notification['message']); ?>
                                                        </p>
                                                        <small class="notification-time">
                                                            <i class="fas fa-clock me-1"></i>
                                                            <?php echo date('M j, Y \a\t g:i A', strtotime($notification['created_at'])); ?>
                                                        </small>
                                                    </div>
                                                    <div class="d-flex align-items-center">
                                                        <?php if (!$notification['is_read']): ?>
                                                            <form method="POST" action="" class="me-2">
                                                                <input type="hidden" name="notification_id" value="<?php echo $notification['notification_id']; ?>">
                                                                <button type="submit" name="mark_read" class="btn btn-sm btn-outline-primary" title="Mark as read">
                                                                    <i class="fas fa-check"></i>
                                                                </button>
                                                            </form>
                                                        <?php endif; ?>
                                                        
                                                        <?php if ($notification['related_type'] == 'lecturer_registration'): ?>
                                                            <a href="pending_lecturers.php" class="btn btn-sm btn-primary" title="View pending lecturers">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                        <?php elseif ($notification['related_type'] == 'student_registration'): ?>
                                                            <a href="pending_students.php" class="btn btn-sm btn-primary" title="View pending students">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endwhile; ?>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-5">
                                <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No notifications yet</h5>
                                <p class="text-muted">You'll see notifications here when lecturers or students register.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/script.js"></script>
    <!-- Session Manager -->
    <script src="../assets/js/session_manager.js"></script>
    <!-- Notification Manager -->
    <script src="assets/js/notification_manager.js"></script>

    <script>
    // Clear any active toasts when user visits notifications page
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(function() {
            if (window.notificationManager) {
                window.notificationManager.resetNotificationState();
            }
        }, 1000);
    });

    // Auto-refresh notifications page every 30 seconds
    setInterval(function() {
        if (window.notificationManager) {
            window.notificationManager.refresh();
        }
    }, 30000);
    </script>
</body>
</html>
