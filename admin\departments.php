<?php
require_once '../config/functions.php';
require_once 'auth_check.php';

// Check if dept_code column exists, if not add it
$columns = $conn->query("SHOW COLUMNS FROM departments LIKE 'dept_code'");
if ($columns->num_rows == 0) {
    // Add dept_code column
    $conn->query("ALTER TABLE departments ADD COLUMN dept_code VARCHAR(10) UNIQUE AFTER dept_name");

    // Update existing departments with default codes
    $existing_depts = $conn->query("SELECT dept_id, dept_name FROM departments");
    while ($dept = $existing_depts->fetch_assoc()) {
        $code = strtoupper(substr($dept['dept_name'], 0, 3));
        $counter = 1;
        $original_code = $code;

        // Ensure unique code
        while (true) {
            $check = $conn->prepare("SELECT dept_id FROM departments WHERE dept_code = ?");
            $check->bind_param("s", $code);
            $check->execute();
            if ($check->get_result()->num_rows == 0) {
                break;
            }
            $code = $original_code . $counter;
            $counter++;
        }

        $update = $conn->prepare("UPDATE departments SET dept_code = ? WHERE dept_id = ?");
        $update->bind_param("si", $code, $dept['dept_id']);
        $update->execute();
    }
}

// Get all departments
$departments = $conn->query("SELECT * FROM departments ORDER BY dept_name");

// Process delete department if requested
$message = '';
if (isset($_GET['delete']) && !empty($_GET['delete'])) {
    $dept_id = sanitize($_GET['delete']);

    // Check if department exists
    $stmt = $conn->prepare("SELECT dept_id FROM departments WHERE dept_id = ?");
    $stmt->bind_param("i", $dept_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        // Check if department has associated students or lecturers
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM students WHERE dept_id = ?");
        $stmt->bind_param("i", $dept_id);
        $stmt->execute();
        $studentCount = $stmt->get_result()->fetch_assoc()['count'];

        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM lecturers WHERE dept_id = ?");
        $stmt->bind_param("i", $dept_id);
        $stmt->execute();
        $lecturerCount = $stmt->get_result()->fetch_assoc()['count'];

        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM courses WHERE dept_id = ?");
        $stmt->bind_param("i", $dept_id);
        $stmt->execute();
        $courseCount = $stmt->get_result()->fetch_assoc()['count'];

        if ($studentCount > 0 || $lecturerCount > 0 || $courseCount > 0) {
            $message = displayError("Cannot delete department. It has associated students, lecturers, or courses.");
        } else {
            // Delete department
            $stmt = $conn->prepare("DELETE FROM departments WHERE dept_id = ?");
            $stmt->bind_param("i", $dept_id);

            if ($stmt->execute()) {
                $message = displaySuccess("Department deleted successfully!");
                // Refresh departments list
                $departments = $conn->query("SELECT * FROM departments ORDER BY dept_name");
            } else {
                $message = displayError("Error deleting department: " . $conn->error);
            }
        }
    } else {
        $message = displayError("Department not found");
    }
}

// Process add department if submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_department'])) {
    $dept_name = sanitize($_POST['dept_name']);
    $dept_code = sanitize($_POST['dept_code']);

    if (empty($dept_name) || empty($dept_code)) {
        $message = displayError("Department name and code are required");
    } else {
        // Check if department already exists
        $stmt = $conn->prepare("SELECT dept_id FROM departments WHERE dept_name = ? OR dept_code = ?");
        $stmt->bind_param("ss", $dept_name, $dept_code);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $message = displayError("Department with this name or code already exists");
        } else {
            // Add new department
            $stmt = $conn->prepare("INSERT INTO departments (dept_name, dept_code) VALUES (?, ?)");
            $stmt->bind_param("ss", $dept_name, $dept_code);

            if ($stmt->execute()) {
                $message = displaySuccess("Department added successfully!");
                // Refresh departments list
                $departments = $conn->query("SELECT * FROM departments ORDER BY dept_name");
            } else {
                $message = displayError("Error adding department: " . $conn->error);
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Departments - Ogbonnaya Onu Polytechnic</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }

        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .card-header {
            background-color: #343a40;
            color: white;
            border-radius: 10px 10px 0 0 !important;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="../images/logo.jpg" alt="Logo" width="30" height="30" class="d-inline-block align-text-top me-2 rounded-circle">
                Admin Dashboard
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Students
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="students.php">All Students</a></li>
                            <li><a class="dropdown-item" href="pending_students.php">Pending Approvals</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Lecturers
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="lecturers.php">All Lecturers</a></li>
                            <li><a class="dropdown-item" href="pending_lecturers.php">Pending Approvals</a></li>
                            <li><a class="dropdown-item" href="assign_courses.php">Assign Courses</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Courses
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="courses.php">All Courses</a></li>
                            <li><a class="dropdown-item" href="add_course.php">Add New Course</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Departments
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item active" href="departments.php">All Departments</a></li>
                            <li><a class="dropdown-item" href="add_department.php">Add New Department</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">Reports</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-1"></i> <?php echo $_SESSION['username']; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profile</a></li>
                            <li><a class="dropdown-item" href="change_password.php">Change Password</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col-md-12">
                <h2>Departments</h2>
                <p class="text-muted">Manage departments for the polytechnic</p>
            </div>
        </div>

        <?php echo $message; ?>

        <div class="row">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Add New Department</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="">
                            <div class="mb-3">
                                <label for="dept_name" class="form-label">Department Name</label>
                                <input type="text" class="form-control" id="dept_name" name="dept_name" required>
                            </div>
                            <div class="mb-3">
                                <label for="dept_code" class="form-label">Department Code</label>
                                <input type="text" class="form-control" id="dept_code" name="dept_code" required>
                                <div class="form-text">Short code for the department (e.g., CSC, EEE)</div>
                            </div>
                            <div class="d-grid">
                                <button type="submit" name="add_department" class="btn btn-primary">
                                    <i class="fas fa-plus-circle me-1"></i> Add Department
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Departments List</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($departments->num_rows > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover data-table">
                                    <thead>
                                        <tr>
                                            <th>Department Name</th>
                                            <th>Code</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($department = $departments->fetch_assoc()): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($department['dept_name']); ?></td>
                                                <td><?php echo htmlspecialchars($department['dept_code'] ?? 'N/A'); ?></td>
                                                <td>
                                                    <a href="edit_department.php?id=<?php echo $department['dept_id']; ?>"
                                                       class="btn btn-sm btn-warning" title="Edit Department">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="departments.php?delete=<?php echo $department['dept_id']; ?>"
                                                       class="btn btn-sm btn-danger"
                                                       onclick="return confirm('Are you sure you want to delete this department? This action cannot be undone.')"
                                                       title="Delete Department">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">No departments found. Add your first department using the form.</div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-3 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; <?php echo date('Y'); ?> Ogbonnaya Onu Polytechnic. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="../about.php" class="text-white me-3">About</a>
                    <a href="../support.php" class="text-white">Support</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script>
        $(document).ready(function() {
            $('.data-table').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
            });
        });
    </script>
</body>
</html>
