<?php
require_once '../config/functions.php';
require_once 'auth_check.php';

// Check if student ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    redirect('students.php');
}

$student_id = sanitize($_GET['id']);

// Get student details
$stmt = $conn->prepare("
    SELECT s.*, d.dept_name, l.level_name, sem.semester_name
    FROM students s
    JOIN departments d ON s.dept_id = d.dept_id
    JOIN levels l ON s.level_id = l.level_id
    JOIN semesters sem ON s.semester_id = sem.semester_id
    WHERE s.student_id = ?
");
$stmt->bind_param("i", $student_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    redirect('students.php');
}

$student = $result->fetch_assoc();

// Get attendance records for this student
$attendance = $conn->query("
    SELECT a.*, c.course_code, c.course_title
    FROM attendance a
    JOIN courses c ON a.course_id = c.course_id
    WHERE a.student_id = $student_id
    ORDER BY a.attendance_date DESC, a.attendance_time DESC
");

// Count total courses and attended courses
$totalCourses = $conn->query("
    SELECT COUNT(*) as count
    FROM courses
    WHERE dept_id = {$student['dept_id']}
    AND level_id = {$student['level_id']}
    AND semester_id = {$student['semester_id']}
")->fetch_assoc()['count'];

$attendedCourses = $conn->query("
    SELECT COUNT(DISTINCT course_id) as count
    FROM attendance
    WHERE student_id = $student_id
")->fetch_assoc()['count'];

// Calculate attendance percentage
$attendancePercentage = ($totalCourses > 0) ? round(($attendedCourses / $totalCourses) * 100) : 0;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Details - Ogbonnaya Onu Polytechnic</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="../images/logo.jpg" alt="Logo" width="30" height="30" class="d-inline-block align-text-top me-2 rounded-circle">
                Admin Dashboard
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Students
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item active" href="students.php">All Students</a></li>
                            <li><a class="dropdown-item" href="pending_students.php">Pending Approvals</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Lecturers
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="lecturers.php">All Lecturers</a></li>
                            <li><a class="dropdown-item" href="pending_lecturers.php">Pending Approvals</a></li>
                            <li><a class="dropdown-item" href="assign_courses.php">Assign Courses</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Courses
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="courses.php">All Courses</a></li>
                            <li><a class="dropdown-item" href="add_course.php">Add New Course</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Departments
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="departments.php">All Departments</a></li>
                            <li><a class="dropdown-item" href="add_department.php">Add New Department</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">Reports</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-1"></i> <?php echo $_SESSION['username']; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profile</a></li>
                            <li><a class="dropdown-item" href="change_password.php">Change Password</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col-md-8">
                <h2>Student Details</h2>
                <p class="text-muted">Viewing details for <?php echo $student['name']; ?></p>
            </div>
            <div class="col-md-4 text-end">
                <a href="students.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Back to Students
                </a>
            </div>
        </div>

        <div class="row">
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Student Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-3">
                            <i class="fas fa-user-graduate fa-5x text-primary"></i>
                        </div>
                        <table class="table table-borderless">
                            <tr>
                                <th>Name:</th>
                                <td><?php echo $student['name']; ?></td>
                            </tr>
                            <tr>
                                <th>Matric Number:</th>
                                <td><?php echo $student['matric_number']; ?></td>
                            </tr>
                            <tr>
                                <th>Email:</th>
                                <td><?php echo $student['email']; ?></td>
                            </tr>
                            <tr>
                                <th>Department:</th>
                                <td><?php echo $student['dept_name']; ?></td>
                            </tr>
                            <tr>
                                <th>Level:</th>
                                <td><?php echo $student['level_name']; ?></td>
                            </tr>
                            <tr>
                                <th>Semester:</th>
                                <td><?php echo $student['semester_name']; ?></td>
                            </tr>
                            <tr>
                                <th>Status:</th>
                                <td>
                                    <?php if ($student['status'] == 'approved'): ?>
                                        <span class="badge bg-success">Approved</span>
                                    <?php elseif ($student['status'] == 'pending'): ?>
                                        <span class="badge bg-warning">Pending</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Rejected</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <th>Registered On:</th>
                                <td><?php echo date('d M Y, h:i A', strtotime($student['created_at'])); ?></td>
                            </tr>
                        </table>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5>Attendance Summary</h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-3">
                            <div class="progress" style="height: 25px;">
                                <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $attendancePercentage; ?>%;" aria-valuenow="<?php echo $attendancePercentage; ?>" aria-valuemin="0" aria-valuemax="100">
                                    <?php echo $attendancePercentage; ?>%
                                </div>
                            </div>
                        </div>
                        <table class="table table-borderless">
                            <tr>
                                <th>Total Courses:</th>
                                <td><?php echo $totalCourses; ?></td>
                            </tr>
                            <tr>
                                <th>Attended Courses:</th>
                                <td><?php echo $attendedCourses; ?></td>
                            </tr>
                            <tr>
                                <th>Attendance Rate:</th>
                                <td><?php echo $attendancePercentage; ?>%</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>

            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5>Attendance Records</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($attendance->num_rows > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover data-table">
                                    <thead>
                                        <tr>
                                            <th>Course Code</th>
                                            <th>Course Title</th>
                                            <th>Date</th>
                                            <th>Time</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($record = $attendance->fetch_assoc()): ?>
                                            <tr>
                                                <td><?php echo $record['course_code']; ?></td>
                                                <td><?php echo $record['course_title']; ?></td>
                                                <td><?php echo date('d M Y', strtotime($record['attendance_date'])); ?></td>
                                                <td><?php echo date('h:i A', strtotime($record['attendance_time'])); ?></td>
                                            </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">No attendance records found for this student.</div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-3 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; <?php echo date('Y'); ?> Ogbonnaya Onu Polytechnic. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p>QR Code Attendance Management System</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/script.js"></script>
    <!-- Session Manager -->
    <script src="../assets/js/session_manager.js"></script>
    <script>
        $(document).ready(function() {
            $('.data-table').DataTable({
                "order": [[2, "desc"]]
            });
        });
    </script>
</body>
</html>
