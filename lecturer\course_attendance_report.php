<?php
require_once '../config/functions.php';

// Check if lecturer is logged in
if (!isLecturerLoggedIn()) {
    redirect('../index.php');
}

// Get lecturer details
$lecturer_id = $_SESSION['lecturer_id'];
$lecturer = getLecturerDetails($lecturer_id);

// Check if course_id is provided
if (!isset($_GET['course_id'])) {
    redirect('index.php');
}

$course_id = sanitize($_GET['course_id']);
// Get course details
$stmt = $conn->prepare("
    SELECT c.*, d.dept_name, l.level_name, s.semester_name
    FROM courses c
    JOIN departments d ON c.dept_id = d.dept_id
    JOIN levels l ON c.level_id = l.level_id
    JOIN semesters s ON c.semester_id = s.semester_id
    WHERE c.course_id = ?
");
$stmt->bind_param("i", $course_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    redirect('index.php');
}

$course = $result->fetch_assoc();

// Get all students in this course's department, level, and semester
$students = $conn->query("
    SELECT * FROM students
    WHERE dept_id = {$course['dept_id']}
    AND level_id = {$course['level_id']}
    AND semester_id = {$course['semester_id']}
    AND status = 'approved'
    ORDER BY name
");

// Get all class sessions for this course
$sessions = $conn->query("
    SELECT * FROM class_sessions
    WHERE course_id = $course_id AND lecturer_id = $lecturer_id
    ORDER BY start_time
");

// Store session data for easier access
$session_data = [];
$total_sessions = 0;
while ($session = $sessions->fetch_assoc()) {
    $session_data[] = $session;
    $total_sessions++;
}

// Get all scheduled classes for this course
$scheduled_classes = $conn->query("
    SELECT * FROM class_schedules
    WHERE course_id = $course_id AND lecturer_id = $lecturer_id
    AND (status = 'ended' OR status = 'started')
    ORDER BY class_date, start_time
");

// Store scheduled class data for easier access
$scheduled_class_data = [];
$total_scheduled_classes = 0;
while ($class = $scheduled_classes->fetch_assoc()) {
    $scheduled_class_data[] = $class;
    $total_scheduled_classes++;
}

// Determine total conducted classes (use the higher count)
$total_conducted_classes = max($total_sessions, $total_scheduled_classes);

// Get attendance data for each student
$student_attendance = [];
$student_missed = [];

while ($student = $students->fetch_assoc()) {
    $student_id = $student['student_id'];

    // Get attendance records for this student and course
    $attendance_query = $conn->prepare("
        SELECT * FROM attendance
        WHERE student_id = ? AND course_id = ?
    ");
    $attendance_query->bind_param("ii", $student_id, $course_id);
    $attendance_query->execute();
    $attendance_result = $attendance_query->get_result();

    // Store attended sessions and count total attendances
    $attended_sessions = [];
    $total_attendances = 0;
    while ($attendance = $attendance_result->fetch_assoc()) {
        if (isset($attendance['session_id'])) {
            $attended_sessions[] = $attendance['session_id'];
        }
        $total_attendances++;
    }

    // Calculate attendance rate based on conducted classes vs attended classes
    $attendance_rate = ($total_conducted_classes > 0) ? ($total_attendances / $total_conducted_classes) * 100 : 0;

    // Get missed lectures
    $missed_query = $conn->prepare("
        SELECT * FROM missed_lectures
        WHERE student_id = ? AND course_id = ?
    ");
    $missed_query->bind_param("ii", $student_id, $course_id);
    $missed_query->execute();
    $missed_result = $missed_query->get_result();

    // Store missed sessions
    $missed_sessions = [];
    while ($missed = $missed_result->fetch_assoc()) {
        $missed_sessions[] = $missed['session_id'];
    }

    // Store student data
    $student_attendance[$student_id] = [
        'student' => $student,
        'attended' => $attended_sessions,
        'total_attendances' => $total_attendances,
        'attendance_rate' => $attendance_rate,
        'missed' => $missed_sessions
    ];
}

// Handle export to Excel
if (isset($_POST['export_excel'])) {
    // Set headers for Excel download
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment;filename="attendance_report_' . $course['course_code'] . '.xls"');
    header('Cache-Control: max-age=0');

    // Create Excel content
    echo '<table border="1">';
    echo '<tr><th colspan="' . ($total_sessions + 7) . '">Attendance Report: ' . $course['course_code'] . ' - ' . $course['course_title'] . '</th></tr>';
    echo '<tr><th colspan="' . ($total_sessions + 7) . '">Department: ' . $course['dept_name'] . ', Level: ' . $course['level_name'] . ', Semester: ' . $course['semester_name'] . '</th></tr>';
    echo '<tr><th colspan="' . ($total_sessions + 7) . '">Total Conducted Classes: ' . $total_conducted_classes . ' | Generated on: ' . date('d/m/Y H:i') . '</th></tr>';

    // Header row
    echo '<tr>';
    echo '<th>S/N</th>';
    echo '<th>Matric Number</th>';
    echo '<th>Student Name</th>';
    echo '<th>Phone Number</th>';

    // Session dates as headers
    foreach ($session_data as $session) {
        echo '<th>' . date('d/m/Y', strtotime($session['start_time'])) . '</th>';
    }

    echo '<th>Classes Attended</th>';
    echo '<th>Total Classes</th>';
    echo '<th>Attendance Rate</th>';
    echo '</tr>';

    // Data rows
    $sn = 1;
    foreach ($student_attendance as $student_id => $data) {
        echo '<tr>';
        echo '<td>' . $sn++ . '</td>';
        echo '<td>' . $data['student']['matric_number'] . '</td>';
        echo '<td>' . $data['student']['name'] . '</td>';
        echo '<td>' . $data['student']['phone'] . '</td>';

        // Attendance status for each session
        foreach ($session_data as $session) {
            if (in_array($session['session_id'], $data['attended'])) {
                echo '<td>Present</td>';
            } else {
                echo '<td>Absent</td>';
            }
        }

        echo '<td>' . $data['total_attendances'] . '</td>';
        echo '<td>' . $total_conducted_classes . '</td>';
        echo '<td>' . number_format($data['attendance_rate'], 1) . '%</td>';
        echo '</tr>';
    }

    echo '</table>';
    exit;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Course Attendance Report - Lecturer Dashboard</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .attendance-cell {
            text-align: center;
            width: 40px;
        }
        .present {
            background-color: #d1e7dd;
            color: #0f5132;
        }
        .absent {
            background-color: #f8d7da;
            color: #842029;
        }
        .attendance-table th, .attendance-table td {
            vertical-align: middle;
        }
    </style>
</head>
<body>
    <!-- Include Navbar -->
    <?php include 'includes/navbar.php'; ?>

    <div class="container-fluid mt-4">
        <div class="row mb-3">
            <div class="col-md-8">
                <h2><i class="fas fa-chart-bar me-2"></i> Attendance Report</h2>
                <h4><?php echo $course['course_code'] . ' - ' . $course['course_title']; ?></h4>
                <p>
                    <strong>Department:</strong> <?php echo $course['dept_name']; ?> |
                    <strong>Level:</strong> <?php echo $course['level_name']; ?> |
                    <strong>Semester:</strong> <?php echo $course['semester_name']; ?>
                </p>

            </div>
            <div class="col-md-4 text-end">
                <form method="POST" action="">
                    <button type="submit" name="export_excel" class="btn btn-success">
                        <i class="fas fa-file-excel me-2"></i> Export to Excel
                    </button>
                    <a href="index.php" class="btn btn-secondary ms-2">
                        <i class="fas fa-arrow-left me-2"></i> Back
                    </a>
                </form>
            </div>
        </div>

        <div class="card">
            <div class="card-header bg-primary text-white">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h5 class="mb-0"><i class="fas fa-users me-2"></i> Student Attendance</h5>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <span class="badge bg-light text-dark me-2">
                            Total Sessions: <?php echo $total_sessions; ?>
                        </span>
                        <span class="badge bg-light text-dark me-2">
                            Scheduled Classes: <?php echo $total_scheduled_classes; ?>
                        </span>
                        <span class="badge bg-light text-dark">
                            Total Conducted: <?php echo $total_conducted_classes; ?>
                        </span>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <?php if ($students->num_rows > 0 && $total_conducted_classes > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover attendance-table">
                            <thead class="table-light">
                                <tr>
                                    <th rowspan="2" style="width: 50px;">S/N</th>
                                    <th rowspan="2" style="width: 120px;">Matric Number</th>
                                    <th rowspan="2">Student Name</th>
                                    <th rowspan="2" style="width: 120px;">Phone Number</th>
                                    <th colspan="<?php echo $total_sessions; ?>" class="text-center">Attendance by Date</th>
                                    <th rowspan="2" style="width: 100px;">Attendance Rate</th>
                                    <th rowspan="2" style="width: 100px;">Missed Lectures</th>
                                </tr>
                                <tr>
                                    <?php foreach ($session_data as $session): ?>
                                        <th class="attendance-cell">
                                            <?php echo date('d/m', strtotime($session['start_time'])); ?>
                                        </th>
                                    <?php endforeach; ?>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $sn = 1;
                                foreach ($student_attendance as $student_id => $data):
                                ?>
                                    <tr>
                                        <td><?php echo $sn++; ?></td>
                                        <td><?php echo $data['student']['matric_number']; ?></td>
                                        <td><?php echo $data['student']['name']; ?></td>
                                        <td><?php echo $data['student']['phone'] ?? 'N/A'; ?></td>

                                        <?php foreach ($session_data as $session): ?>
                                            <?php if (in_array($session['session_id'], $data['attended'])): ?>
                                                <td class="attendance-cell present">
                                                    <i class="fas fa-check"></i>
                                                </td>
                                            <?php else: ?>
                                                <td class="attendance-cell absent">
                                                    <i class="fas fa-times"></i>
                                                </td>
                                            <?php endif; ?>
                                        <?php endforeach; ?>

                                        <td class="text-center">
                                            <?php
                                            $rate = $data['attendance_rate'];
                                            $badge_class = 'bg-danger';
                                            if ($rate >= 75) {
                                                $badge_class = 'bg-success';
                                            } elseif ($rate >= 50) {
                                                $badge_class = 'bg-warning text-dark';
                                            } elseif ($rate >= 25) {
                                                $badge_class = 'bg-info text-dark';
                                            }
                                            ?>
                                            <div>
                                                <span class="badge <?php echo $badge_class; ?>">
                                                    <?php echo number_format($rate, 1); ?>%
                                                </span>
                                            </div>
                                            <small class="text-muted mt-1 d-block">
                                                <?php echo $data['total_attendances']; ?> of <?php echo $total_conducted_classes; ?> classes
                                            </small>
                                        </td>
                                        <td class="text-center">
                                            <?php echo count($data['missed']); ?>
                                            <?php if (count($data['missed']) > 0): ?>
                                                <button type="button" class="btn btn-sm btn-outline-danger ms-2"
                                                        data-bs-toggle="modal" data-bs-target="#missedModal<?php echo $student_id; ?>">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Missed Lectures Modals -->
                    <?php foreach ($student_attendance as $student_id => $data): ?>
                        <?php if (count($data['missed']) > 0): ?>
                            <div class="modal fade" id="missedModal<?php echo $student_id; ?>" tabindex="-1" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header bg-danger text-white">
                                            <h5 class="modal-title">
                                                <i class="fas fa-calendar-times me-2"></i>
                                                Missed Lectures: <?php echo $data['student']['name']; ?>
                                            </h5>
                                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <p><strong>Matric Number:</strong> <?php echo $data['student']['matric_number']; ?></p>
                                            <div class="table-responsive">
                                                <table class="table table-bordered">
                                                    <thead class="table-light">
                                                        <tr>
                                                            <th>#</th>
                                                            <th>Date</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php
                                                        $count = 1;
                                                        foreach ($data['missed'] as $missed_session_id):
                                                            // Find session details
                                                            $session_details = null;
                                                            foreach ($session_data as $session) {
                                                                if ($session['session_id'] == $missed_session_id) {
                                                                    $session_details = $session;
                                                                    break;
                                                                }
                                                            }

                                                            if ($session_details):
                                                        ?>
                                                            <tr>
                                                                <td><?php echo $count++; ?></td>
                                                                <td><?php echo date('d M Y', strtotime($session_details['start_time'])); ?></td>
                                                            </tr>
                                                        <?php
                                                            endif;
                                                        endforeach;
                                                        ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; ?>

                <?php elseif ($total_conducted_classes === 0): ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i> No classes have been conducted for this course yet. Start a class session or schedule classes to track attendance.
                    </div>
                <?php else: ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> No students found for this course.
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-3 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; <?php echo date('Y'); ?> Ogbonnaya Onu Polytechnic. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="../about.php" class="text-white me-3">About</a>
                    <a href="../contact.php" class="text-white">Contact</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
</body>
</html>
