<?php
require_once '../config/functions.php';

// Check if lecturer is logged in
if (!isLecturerLoggedIn()) {
    redirect('../index.php');
}

// Get lecturer details
$lecturer_id = $_SESSION['lecturer_id'];
$lecturer = getLecturerDetails($lecturer_id);

// Get assigned courses
$courses = $conn->query("
    SELECT c.*, d.dept_name, l.level_name, s.semester_name
    FROM lecturer_courses lc
    JOIN courses c ON lc.course_id = c.course_id
    JOIN departments d ON c.dept_id = d.dept_id
    JOIN levels l ON c.level_id = l.level_id
    JOIN semesters s ON c.semester_id = s.semester_id
    WHERE lc.lecturer_id = $lecturer_id
    ORDER BY c.course_code
");

// Get student counts for each course
$courseStudentCounts = [];
$courseAttendanceCounts = [];

// Reset courses result pointer
$courses->data_seek(0);
while ($course = $courses->fetch_assoc()) {
    // Count students in this course's department, level, and semester
    $query = "
        SELECT COUNT(*) as count
        FROM students
        WHERE dept_id = {$course['dept_id']}
        AND level_id = {$course['level_id']}
        AND semester_id = {$course['semester_id']}
        AND status = 'approved'
    ";
    $studentCount = $conn->query($query)->fetch_assoc()['count'];
    $courseStudentCounts[$course['course_id']] = $studentCount;

    // Count attendance for this course
    $query = "
        SELECT COUNT(DISTINCT student_id) as count
        FROM attendance
        WHERE course_id = {$course['course_id']}
    ";
    $attendanceCount = $conn->query($query)->fetch_assoc()['count'];
    $courseAttendanceCounts[$course['course_id']] = $attendanceCount;

    // Generate QR code if not already generated
    if (empty($course['qr_code'])) {
        $qr_data = json_encode([
            'course_code' => $course['course_code'],
            'course_title' => $course['course_title'],
            'timestamp' => time()
        ]);

        $qr_code = generateQRCode($qr_data);

        $stmt = $conn->prepare("UPDATE courses SET qr_code = ? WHERE course_id = ?");
        $stmt->bind_param("si", $qr_code, $course['course_id']);
        $stmt->execute();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Courses - Ogbonnaya Onu Polytechnic</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        /* Main Styles for Ogbonnaya Onu Polytechnic Attendance System */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }

        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .card-header {
            background-color: #343a40;
            color: white;
            border-radius: 10px 10px 0 0 !important;
        }

        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
        }

        .btn-primary:hover {
            background-color: #0069d9;
            border-color: #0062cc;
        }

        .btn-success {
            background-color: #28a745;
            border-color: #28a745;
        }

        .btn-success:hover {
            background-color: #218838;
            border-color: #1e7e34;
        }

        .qr-container {
            text-align: center;
            margin: 20px 0;
        }

        .qr-code {
            padding: 15px;
            background: white;
            display: inline-block;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="../images/logo.jpg" alt="Logo" width="30" height="30" class="d-inline-block align-text-top me-2">
                Lecturer Dashboard
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="courses.php">My Courses</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="attendance.php">Attendance</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">Reports</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">Students</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-1"></i> <?php echo $lecturer['name']; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profile</a></li>
                            <li><a class="dropdown-item" href="change_password.php">Change Password</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col-md-12">
                <h2>My Courses</h2>
                <p class="text-muted">Manage your assigned courses and attendance</p>
            </div>
        </div>

        <!-- Courses List -->
        <div class="row">
            <?php
            // Reset courses result pointer
            $courses->data_seek(0);
            if ($courses->num_rows > 0):
                while ($course = $courses->fetch_assoc()):
                    $studentCount = $courseStudentCounts[$course['course_id']] ?? 0;
                    $attendanceCount = $courseAttendanceCounts[$course['course_id']] ?? 0;
                    $attendanceRate = ($studentCount > 0) ? round(($attendanceCount / $studentCount) * 100) : 0;
            ?>
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="mb-0"><?php echo $course['course_code']; ?></h5>
                        </div>
                        <div class="card-body">
                            <h6 class="card-title"><?php echo $course['course_title']; ?></h6>
                            <p class="card-text">
                                <strong>Department:</strong> <?php echo $course['dept_name']; ?><br>
                                <strong>Level:</strong> <?php echo $course['level_name']; ?><br>
                                <strong>Semester:</strong> <?php echo $course['semester_name']; ?><br>
                                <strong>Students:</strong> <?php echo $studentCount; ?><br>
                                <strong>Attendance:</strong> <?php echo $attendanceCount; ?> students (<?php echo $attendanceRate; ?>%)
                            </p>

                            <div class="progress mb-3">
                                <div class="progress-bar bg-<?php echo ($attendanceRate >= 75) ? 'success' : (($attendanceRate >= 50) ? 'warning' : 'danger'); ?>"
                                     role="progressbar"
                                     style="width: <?php echo $attendanceRate; ?>%"
                                     aria-valuenow="<?php echo $attendanceRate; ?>"
                                     aria-valuemin="0"
                                     aria-valuemax="100"></div>
                            </div>
                        </div>
                        <div class="card-footer d-flex justify-content-between">
                            <a href="course_details.php?id=<?php echo $course['course_id']; ?>" class="btn btn-primary">
                                <i class="fas fa-info-circle"></i> Details
                            </a>
                            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#qrModal<?php echo $course['course_id']; ?>">
                                <i class="fas fa-qrcode"></i> QR Code
                            </button>
                        </div>
                    </div>
                </div>

                <!-- QR Code Modal -->
                <div class="modal fade" id="qrModal<?php echo $course['course_id']; ?>" tabindex="-1" aria-labelledby="qrModalLabel<?php echo $course['course_id']; ?>" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="qrModalLabel<?php echo $course['course_id']; ?>">QR Code for <?php echo $course['course_code']; ?></h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body text-center">
                                <h6><?php echo $course['course_title']; ?></h6>
                                <div class="qr-code my-3">
                                    <?php if (!empty($course['qr_code']) && file_exists('../' . $course['qr_code'])): ?>
                                        <img src="../<?php echo $course['qr_code']; ?>" alt="QR Code" class="img-fluid">
                                    <?php else: ?>
                                        <div id="qr-loading-<?php echo $course['course_id']; ?>" class="text-center">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                            <p class="mt-2">Generating QR code...</p>
                                        </div>
                                        <div id="qr-container-<?php echo $course['course_id']; ?>" class="d-none">
                                            <!-- QR code will be loaded here -->
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <p class="mb-0">Students can scan this QR code to mark their attendance for this course.</p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                <?php if (!empty($course['qr_code']) && file_exists($course['qr_code'])): ?>
                                <button type="button" class="btn btn-warning regenerate-qr-btn" data-course-id="<?php echo $course['course_id']; ?>">
                                    <i class="fas fa-sync-alt"></i> Regenerate
                                </button>
                                <a href="<?php echo $course['qr_code']; ?>" download="<?php echo $course['course_code']; ?>_QR.png" class="btn btn-primary">
                                    <i class="fas fa-download"></i> Download
                                </a>
                                <?php else: ?>
                                <a href="#" download="<?php echo $course['course_code']; ?>_QR.png" class="btn btn-primary" id="download-btn-<?php echo $course['course_id']; ?>" disabled>
                                    <i class="fas fa-download"></i> Download
                                </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php
                endwhile;
            else:
            ?>
                <div class="col-md-12">
                    <div class="alert alert-info">
                        <p>You don't have any courses assigned to you yet. Please contact the administrator.</p>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-3 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; <?php echo date('Y'); ?> Ogbonnaya Onu Polytechnic. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="../about.php" class="text-white me-3">About</a>
                    <a href="../contact.php" class="text-white">Contact</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/script.js"></script>

    <!-- QR Code Generation Script -->
    <script>
        $(document).ready(function() {
            // Generate QR code when modal is shown
            $('.modal').on('shown.bs.modal', function (e) {
                const modalId = $(this).attr('id');
                const courseId = modalId.replace('qrModal', '');

                // Check if QR code needs to be generated
                if ($('#qr-loading-' + courseId).length > 0) {
                    generateCourseQR(courseId);
                }
            });

            // Function to generate course QR code
            function generateCourseQR(courseId, regenerate = false) {
                const loadingElement = $('#qr-loading-' + courseId);
                const containerElement = $('#qr-container-' + courseId);
                const modalBody = $('#qrModal' + courseId + ' .modal-body');

                // If regenerating, show loading state
                if (regenerate) {
                    // Create loading element if it doesn't exist
                    if (loadingElement.length === 0) {
                        const loadingHtml = `
                            <div id="qr-loading-${courseId}" class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2">Regenerating QR code...</p>
                            </div>
                        `;

                        // Hide existing QR code and show loading
                        modalBody.find('.qr-code img').hide();
                        modalBody.find('.qr-code').append(loadingHtml);
                    } else {
                        loadingElement.removeClass('d-none');
                        containerElement.addClass('d-none');
                    }
                }

                // Call the API to generate QR code
                $.ajax({
                    url: '../generate_qr.php',
                    type: 'GET',
                    data: {
                        id: courseId,
                        regenerate: regenerate ? 1 : 0
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            if (regenerate) {
                                // For regeneration, reload the modal content
                                location.reload();
                            } else {
                                // Create image element
                                const img = $('<img>')
                                    .attr('src', response.qr_code)
                                    .attr('alt', 'QR Code')
                                    .addClass('img-fluid');

                                // Add image to container
                                containerElement.html(img);

                                // Hide loading, show container
                                loadingElement.addClass('d-none');
                                containerElement.removeClass('d-none');

                                // Update download button href
                                const downloadBtn = $('#download-btn-' + courseId);
                                if (downloadBtn.length > 0) {
                                    downloadBtn.attr('href', response.qr_code);
                                    downloadBtn.removeAttr('disabled');
                                }
                            }
                        } else {
                            // Show error message
                            loadingElement.html('<div class="alert alert-danger">' +
                                (response.error || 'Failed to generate QR code') + '</div>');
                        }
                    },
                    error: function() {
                        // Show error message
                        loadingElement.html('<div class="alert alert-danger">Error connecting to server</div>');
                    }
                });
            }

            // Handle regenerate button click
            $('.regenerate-qr-btn').on('click', function() {
                const courseId = $(this).data('course-id');

                // Disable the button and show loading state
                $(this).prop('disabled', true);
                $(this).html('<i class="fas fa-spinner fa-spin"></i> Regenerating...');

                // Call regenerate function
                generateCourseQR(courseId, true);
            });
        });
    </script>
</body>
</html>
