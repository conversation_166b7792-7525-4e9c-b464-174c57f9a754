ATTENDANCE SYSTEM - CODE EXPLANATION
====================================

This document explains the technologies used and the structure of the attendance system code.

TECHNOLOGIES USED
=================

1. FRONTEND TECHNOLOGIES:
   - HTML5: Modern web markup language
   - CSS3: Styling and responsive design
   - Bootstrap 5.3: CSS framework for responsive, mobile-first design
   - JavaScript: Client-side interactivity
   - jQuery: JavaScript library for DOM manipulation
   - Font Awesome 6.4: Icon library for beautiful icons
   - DataTables: jQuery plugin for interactive tables

2. BACKEND TECHNOLOGIES:
   - PHP 7.4+: Server-side programming language
   - MySQL 5.7+: Relational database management system
   - MySQLi: PHP extension for MySQL database interaction

3. EXTERNAL SERVICES:
   - Google Charts API: For QR code generation
   - CDN Services: For Bootstrap, jQuery, and Font Awesome

4. SECURITY FEATURES:
   - Password hashing using <PERSON><PERSON>'s password_hash()
   - SQL injection prevention using prepared statements
   - Session management for user authentication
   - CSRF protection through session tokens
   - Input sanitization and validation

FOLDER STRUCTURE EXPLANATION
============================

ROOT DIRECTORY (/)
├── setup.php                 # Installation wizard
├── index.php                 # Main login page
├── about.php                 # About the school page
├── support.php               # Support/contact page
├── HOW_TO_DEPLOY.txt         # Deployment instructions
├── CODE_EXPLANATION.txt      # This file
└── README.md                 # Project overview

CONFIG FOLDER (/config/)
├── database.php              # Database connection settings (auto-generated)
├── functions.php             # Common PHP functions used across the system
├── init_tables.php           # Database table initialization and updates
└── session.php               # Session configuration (auto-generated)

ADMIN FOLDER (/admin/)
├── index.php                 # Admin dashboard
├── login.php                 # Admin login page
├── logout.php                # Admin logout handler
├── profile.php               # Admin profile management
├── change_password.php       # Admin password change
├── departments.php           # Manage departments
├── courses.php               # Manage courses
├── lecturers.php             # Manage lecturers
├── students.php              # Manage students
├── reports.php               # System-wide reports
├── academic_sessions.php     # Academic year/semester management
├── notifications.php         # System notifications
├── auth_check.php            # Admin authentication verification
└── includes/
    └── navbar.php            # Admin navigation bar

LECTURER FOLDER (/lecturer/)
├── index.php                 # Lecturer dashboard
├── login.php                 # Lecturer login page
├── register.php              # Lecturer registration
├── logout.php                # Lecturer logout handler
├── profile.php               # Lecturer profile management
├── change_password.php       # Lecturer password change
├── courses.php               # View assigned courses
├── students.php              # View enrolled students
├── start_class.php           # Start class session and generate QR
├── attendance.php            # View attendance records
├── reports.php               # Generate attendance reports
├── absent_students.php       # View students who missed classes
├── auth_check.php            # Lecturer authentication verification
├── includes/
│   └── navbar.php            # Lecturer navigation bar
└── qr_codes/                 # Generated QR code images

STUDENT FOLDER (/student/)
├── index.php                 # Student dashboard
├── login.php                 # Student login page
├── register.php              # Student registration
├── logout.php                # Student logout handler
├── profile.php               # Student profile management
├── change_password.php       # Student password change
├── courses.php               # View available courses
├── attendance.php            # View attendance history
├── mark_attendance.php       # QR code scanning page
├── auth_check.php            # Student authentication verification
└── includes/
    └── navbar.php            # Student navigation bar

QR_CODES FOLDER (/qr_codes/)
└── [Generated QR code images] # System-generated QR codes for courses

KEY FILES EXPLANATION
=====================

1. SETUP.PHP:
   - Bootstrap installation wizard
   - Creates database and tables
   - Sets up admin account
   - Configures initial system settings
   - Adds default Computer Science HND2 courses

2. CONFIG/FUNCTIONS.PHP:
   - Database connection management
   - User authentication functions
   - Input sanitization functions
   - Session management
   - Common utility functions

3. CONFIG/INIT_TABLES.PHP:
   - Database schema updates
   - Adds missing columns to existing tables
   - Ensures database compatibility
   - Handles system upgrades

4. INDEX.PHP (ROOT):
   - Main entry point
   - User role selection (Admin/Lecturer/Student)
   - System status display
   - Navigation to appropriate login pages

DATABASE STRUCTURE
==================

MAIN TABLES:
1. admin: System administrators
2. departments: Academic departments
3. levels: Academic levels (ND1, ND2, HND1, HND2)
4. semesters: Academic semesters (First, Second)
5. courses: Course information with QR codes
6. lecturers: Lecturer accounts and information
7. students: Student accounts and information
8. lecturer_courses: Links lecturers to their courses
9. class_sessions: Active/ended class sessions
10. attendance: Student attendance records
11. notifications: System notifications
12. academic_sessions: Academic year management
13. security_questions: Password reset questions
14. student_security_answers: Student security answers
15. lecturer_security_answers: Lecturer security answers

KEY RELATIONSHIPS:
- Students belong to departments, levels, and semesters
- Courses belong to departments, levels, and semesters
- Lecturers are assigned to courses through lecturer_courses
- Attendance links students to courses and class sessions
- Class sessions track when courses are active

SYSTEM WORKFLOW
===============

1. ADMIN WORKFLOW:
   - Login to admin dashboard
   - Manage departments, courses, lecturers, students
   - Approve pending registrations
   - View system-wide reports
   - Configure academic sessions

2. LECTURER WORKFLOW:
   - Register and wait for admin approval
   - Login to lecturer dashboard
   - View assigned courses
   - Start class session (generates QR code)
   - Students scan QR code to mark attendance
   - End class session (marks absent students)
   - View attendance reports and absent students

3. STUDENT WORKFLOW:
   - Register and wait for admin approval
   - Login to student dashboard
   - View available courses and lecturers
   - Scan QR code during class to mark attendance
   - View personal attendance history

ATTENDANCE SYSTEM LOGIC
=======================

1. CLASS SESSION CREATION:
   - Lecturer starts a class session
   - System generates unique QR code
   - QR code contains session information
   - Session status set to "active"

2. ATTENDANCE MARKING:
   - Student scans QR code
   - System verifies student enrollment
   - Attendance record created with "present" status
   - Timestamp recorded

3. CLASS SESSION ENDING:
   - Lecturer ends the class session
   - System identifies students who didn't mark attendance
   - Absent records created for missing students
   - Session status set to "ended"

4. REPORTING:
   - Real-time attendance tracking
   - Detailed reports with dates, times, and status
   - Export functionality for Excel
   - Filter by course, date range, and status

SECURITY FEATURES
=================

1. AUTHENTICATION:
   - Password hashing using PHP's password_hash()
   - Session-based authentication
   - Role-based access control
   - Automatic session timeout

2. DATA PROTECTION:
   - SQL injection prevention using prepared statements
   - Input sanitization and validation
   - XSS protection through output escaping
   - CSRF protection

3. ACCESS CONTROL:
   - Admin approval required for lecturer/student accounts
   - Role-based page access restrictions
   - Session verification on protected pages
   - Secure password reset with security questions

RESPONSIVE DESIGN
=================

1. BOOTSTRAP FRAMEWORK:
   - Mobile-first responsive design
   - Grid system for flexible layouts
   - Pre-built components and utilities
   - Cross-browser compatibility

2. USER INTERFACE:
   - Clean, professional design
   - Intuitive navigation
   - Color-coded status indicators
   - Interactive tables and forms

3. ACCESSIBILITY:
   - Semantic HTML structure
   - Keyboard navigation support
   - Screen reader friendly
   - High contrast design

MAINTENANCE AND UPDATES
=======================

1. DATABASE UPDATES:
   - init_tables.php handles schema changes
   - Automatic column additions for upgrades
   - Data migration scripts included

2. SYSTEM MONITORING:
   - Error logging and reporting
   - Session management
   - Performance optimization
   - Regular backup recommendations

3. CUSTOMIZATION:
   - Easy theme modifications through CSS
   - Configurable session timeouts
   - Customizable course structures
   - Flexible department management

This attendance system is designed to be scalable, secure, and user-friendly,
providing a complete solution for educational institutions to track and
manage student attendance efficiently.
