<?php
session_start();
require_once 'config/functions.php';

echo "<h2>Admin Session Debug Information</h2>";

// Check session
echo "<h3>Session Information</h3>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

// Check if admin is logged in
echo "<h3>Login Status</h3>";
if (isAdminLoggedIn()) {
    echo "✅ Admin is logged in<br>";
    $admin_id = $_SESSION['admin_id'];
    echo "Admin ID: $admin_id<br>";
    echo "Username: " . ($_SESSION['username'] ?? 'Not set') . "<br>";
    echo "Last Activity: " . ($_SESSION['last_activity'] ?? 'Not set') . "<br>";
    echo "Last Regeneration: " . ($_SESSION['last_regeneration'] ?? 'Not set') . "<br>";
    
    // Calculate session age
    if (isset($_SESSION['last_activity'])) {
        $session_age = time() - $_SESSION['last_activity'];
        echo "Session Age: " . gmdate('H:i:s', $session_age) . " (hours:minutes:seconds)<br>";
        
        // Check if session would timeout
        $max_idle_time = 7200; // 2 hours as set in auth_check.php
        $time_until_timeout = $max_idle_time - $session_age;
        
        if ($time_until_timeout > 0) {
            echo "Time until timeout: " . gmdate('H:i:s', $time_until_timeout) . "<br>";
            echo "✅ Session is valid<br>";
        } else {
            echo "❌ Session should have timed out<br>";
        }
    }
} else {
    echo "❌ Admin is not logged in<br>";
}

// Check database connection
echo "<h3>Database Connection</h3>";
if ($conn) {
    echo "✅ Database connected<br>";
} else {
    echo "❌ Database connection failed<br>";
}

// Test a simple query
echo "<h3>Database Test</h3>";
$result = $conn->query("SELECT COUNT(*) as count FROM admin");
if ($result) {
    $count = $result->fetch_assoc()['count'];
    echo "Total admins in database: $count<br>";
} else {
    echo "❌ Query failed: " . $conn->error . "<br>";
}

// Check session configuration
echo "<h3>Session Configuration</h3>";
echo "Session ID: " . session_id() . "<br>";
echo "Session Name: " . session_name() . "<br>";
echo "Session Cookie Lifetime: " . ini_get('session.cookie_lifetime') . "<br>";
echo "Session GC Maxlifetime: " . ini_get('session.gc_maxlifetime') . "<br>";
echo "Session Use Cookies: " . ini_get('session.use_cookies') . "<br>";
echo "Session Use Only Cookies: " . ini_get('session.use_only_cookies') . "<br>";

// Check if auto QR regeneration is working
echo "<h3>QR Regeneration Status</h3>";
$qr_settings = $conn->query("SELECT * FROM system_settings WHERE setting_key LIKE '%qr%'");
if ($qr_settings && $qr_settings->num_rows > 0) {
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Setting</th><th>Value</th></tr>";
    while ($setting = $qr_settings->fetch_assoc()) {
        echo "<tr><td>{$setting['setting_key']}</td><td>{$setting['setting_value']}</td></tr>";
    }
    echo "</table>";
} else {
    echo "No QR regeneration settings found<br>";
}

echo "<br><a href='admin/index.php'>Go to Admin Dashboard</a> | ";
echo "<a href='index.php'>Back to Login</a>";
?>
