<?php
require_once 'auth_check.php';
require_once '../config/functions.php';

$success = '';
$error = '';

// Handle AJAX requests for progress updates
if (isset($_GET['action']) && $_GET['action'] === 'get_total') {
    $total = $conn->query("SELECT COUNT(*) as total FROM courses")->fetch_assoc()['total'];
    header('Content-Type: application/json');
    echo json_encode(['total' => $total]);
    exit;
}

if (isset($_GET['action']) && $_GET['action'] === 'regenerate_batch') {
    $offset = intval($_GET['offset'] ?? 0);
    $limit = 5; // Process 5 courses at a time

    $courses = $conn->query("SELECT * FROM courses LIMIT $limit OFFSET $offset");

    $regenerated = 0;
    $failed = 0;
    $processed_courses = [];

    while ($course = $courses->fetch_assoc()) {
        // Generate QR code data
        $qr_data = json_encode([
            'course_id' => $course['course_id'],
            'course_code' => $course['course_code'],
            'course_title' => $course['course_title'],
            'dept_id' => $course['dept_id'],
            'level_id' => $course['level_id'],
            'semester_id' => $course['semester_id'],
            'timestamp' => time()
        ]);

        // Generate new QR code
        $qr_code = generateQRCode($qr_data);

        if ($qr_code) {
            // Update course with new QR code
            $stmt = $conn->prepare("UPDATE courses SET qr_code = ? WHERE course_id = ?");
            $stmt->bind_param("si", $qr_code, $course['course_id']);

            if ($stmt->execute()) {
                $regenerated++;
                $processed_courses[] = [
                    'course_code' => $course['course_code'],
                    'status' => 'success'
                ];
            } else {
                $failed++;
                $processed_courses[] = [
                    'course_code' => $course['course_code'],
                    'status' => 'failed'
                ];
            }
        } else {
            $failed++;
            $processed_courses[] = [
                'course_code' => $course['course_code'],
                'status' => 'failed'
            ];
        }
    }

    header('Content-Type: application/json');
    echo json_encode([
        'regenerated' => $regenerated,
        'failed' => $failed,
        'processed_courses' => $processed_courses,
        'has_more' => $courses->num_rows === $limit
    ]);
    exit;
}

// Legacy processing for direct access
if ($_SERVER['REQUEST_METHOD'] === 'POST' || isset($_GET['confirm'])) {
    // Redirect to avoid processing here - let AJAX handle it
    if (!isset($_GET['legacy'])) {
        header('Location: regenerate_all_qr.php');
        exit;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Regenerate All QR Codes - Admin Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0"><i class="fas fa-sync-alt me-2"></i>Regenerate All QR Codes</h5>
                    </div>
                    <div class="card-body">
                        <!-- Initial Warning -->
                        <div id="warning-section">
                            <div class="alert alert-warning">
                                <h6 class="alert-heading"><i class="fas fa-exclamation-triangle me-2"></i>Warning</h6>
                                <p>This action will regenerate QR codes for ALL courses in the system. This may take a few moments.</p>
                                <p class="mb-0"><strong>Are you sure you want to continue?</strong></p>
                            </div>

                            <div class="text-center">
                                <a href="courses.php" class="btn btn-secondary me-2">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                                <button id="start-regeneration" class="btn btn-warning">
                                    <i class="fas fa-sync-alt me-2"></i>Yes, Regenerate All QR Codes
                                </button>
                            </div>
                        </div>

                        <!-- Progress Section -->
                        <div id="progress-section" style="display: none;">
                            <div class="mb-3">
                                <h6>Regenerating QR Codes...</h6>
                                <div class="progress mb-2" style="height: 25px;">
                                    <div id="progress-bar" class="progress-bar progress-bar-striped progress-bar-animated bg-warning"
                                         role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                        0%
                                    </div>
                                </div>
                                <div class="row text-center">
                                    <div class="col-md-4">
                                        <small class="text-muted">Processed: <span id="processed-count">0</span>/<span id="total-count">0</span></small>
                                    </div>
                                    <div class="col-md-4">
                                        <small class="text-success">Success: <span id="success-count">0</span></small>
                                    </div>
                                    <div class="col-md-4">
                                        <small class="text-danger">Failed: <span id="failed-count">0</span></small>
                                    </div>
                                </div>
                            </div>

                            <!-- Current Processing -->
                            <div id="current-processing" class="alert alert-info" style="display: none;">
                                <i class="fas fa-spinner fa-spin me-2"></i>Processing: <span id="current-course"></span>
                            </div>

                            <!-- Processing Log -->
                            <div id="processing-log" class="mt-3" style="max-height: 200px; overflow-y: auto; display: none;">
                                <h6>Processing Log:</h6>
                                <div id="log-content" class="small"></div>
                            </div>
                        </div>

                        <!-- Results Section -->
                        <div id="results-section" style="display: none;">
                            <div id="results-alert"></div>
                            <div class="text-center">
                                <a href="courses.php" class="btn btn-primary">
                                    <i class="fas fa-arrow-left me-2"></i>Back to Courses
                                </a>
                                <button id="regenerate-again" class="btn btn-warning ms-2">
                                    <i class="fas fa-redo me-2"></i>Regenerate Again
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        let totalCourses = 0;
        let processedCourses = 0;
        let successCount = 0;
        let failedCount = 0;
        let currentOffset = 0;

        document.getElementById('start-regeneration').addEventListener('click', function() {
            startRegeneration();
        });

        document.getElementById('regenerate-again').addEventListener('click', function() {
            resetAndStart();
        });

        function resetAndStart() {
            // Reset counters
            totalCourses = 0;
            processedCourses = 0;
            successCount = 0;
            failedCount = 0;
            currentOffset = 0;

            // Reset UI
            document.getElementById('progress-bar').style.width = '0%';
            document.getElementById('progress-bar').textContent = '0%';
            document.getElementById('processed-count').textContent = '0';
            document.getElementById('success-count').textContent = '0';
            document.getElementById('failed-count').textContent = '0';
            document.getElementById('log-content').innerHTML = '';

            // Show progress section, hide others
            document.getElementById('warning-section').style.display = 'none';
            document.getElementById('results-section').style.display = 'none';
            document.getElementById('progress-section').style.display = 'block';

            startRegeneration();
        }

        async function startRegeneration() {
            // Hide warning, show progress
            document.getElementById('warning-section').style.display = 'none';
            document.getElementById('progress-section').style.display = 'block';
            document.getElementById('processing-log').style.display = 'block';

            try {
                // Get total count first
                const totalResponse = await fetch('regenerate_all_qr.php?action=get_total');
                const totalData = await totalResponse.json();
                totalCourses = totalData.total;

                document.getElementById('total-count').textContent = totalCourses;

                if (totalCourses === 0) {
                    showResults('warning', 'No courses found in the system.');
                    return;
                }

                // Start processing batches
                await processBatches();

            } catch (error) {
                console.error('Error:', error);
                showResults('danger', 'An error occurred during regeneration: ' + error.message);
            }
        }

        async function processBatches() {
            let hasMore = true;

            while (hasMore) {
                try {
                    const response = await fetch(`regenerate_all_qr.php?action=regenerate_batch&offset=${currentOffset}`);
                    const data = await response.json();

                    // Update counters
                    successCount += data.regenerated;
                    failedCount += data.failed;
                    processedCourses += data.processed_courses.length;

                    // Update progress bar
                    const percentage = Math.round((processedCourses / totalCourses) * 100);
                    const progressBar = document.getElementById('progress-bar');
                    progressBar.style.width = percentage + '%';
                    progressBar.textContent = percentage + '%';
                    progressBar.setAttribute('aria-valuenow', percentage);

                    // Update counters
                    document.getElementById('processed-count').textContent = processedCourses;
                    document.getElementById('success-count').textContent = successCount;
                    document.getElementById('failed-count').textContent = failedCount;

                    // Update log
                    const logContent = document.getElementById('log-content');
                    data.processed_courses.forEach(course => {
                        const logEntry = document.createElement('div');
                        logEntry.className = course.status === 'success' ? 'text-success' : 'text-danger';
                        logEntry.innerHTML = `<i class="fas fa-${course.status === 'success' ? 'check' : 'times'} me-1"></i>${course.course_code} - ${course.status === 'success' ? 'Success' : 'Failed'}`;
                        logContent.appendChild(logEntry);
                    });

                    // Scroll log to bottom
                    logContent.scrollTop = logContent.scrollHeight;

                    // Check if there are more courses to process
                    hasMore = data.has_more;
                    currentOffset += 5; // Batch size

                    // Small delay to show progress
                    await new Promise(resolve => setTimeout(resolve, 500));

                } catch (error) {
                    console.error('Batch processing error:', error);
                    hasMore = false;
                    showResults('danger', 'An error occurred during batch processing: ' + error.message);
                    return;
                }
            }

            // Show final results
            showFinalResults();
        }

        function showFinalResults() {
            let alertType = 'success';
            let message = '';

            if (successCount > 0 && failedCount === 0) {
                alertType = 'success';
                message = `<i class="fas fa-check-circle me-2"></i>Successfully regenerated QR codes for all ${successCount} course(s)!`;
            } else if (successCount > 0 && failedCount > 0) {
                alertType = 'warning';
                message = `<i class="fas fa-exclamation-triangle me-2"></i>Regenerated ${successCount} course(s) successfully, but ${failedCount} course(s) failed.`;
            } else {
                alertType = 'danger';
                message = `<i class="fas fa-times-circle me-2"></i>Failed to regenerate any QR codes. All ${failedCount} attempts failed.`;
            }

            showResults(alertType, message);
        }

        function showResults(type, message) {
            document.getElementById('progress-section').style.display = 'none';
            document.getElementById('results-section').style.display = 'block';

            const resultsAlert = document.getElementById('results-alert');
            resultsAlert.className = `alert alert-${type}`;
            resultsAlert.innerHTML = message;
        }
    </script>
</body>
</html>
