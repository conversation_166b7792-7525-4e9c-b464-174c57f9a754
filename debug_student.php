<?php
session_start();
require_once 'config/functions.php';

echo "<h2>Student Debug Information</h2>";

// Check session
echo "<h3>Session Information</h3>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

// Check if student is logged in
echo "<h3>Login Status</h3>";
if (isStudentLoggedIn()) {
    echo "✅ Student is logged in<br>";
    $student_id = $_SESSION['student_id'];
    echo "Student ID: $student_id<br>";
    
    // Get student details
    $student = getStudentDetails($student_id);
    if ($student) {
        echo "<h3>Student Details</h3>";
        echo "<pre>";
        print_r($student);
        echo "</pre>";
    } else {
        echo "❌ Could not fetch student details<br>";
    }
} else {
    echo "❌ Student is not logged in<br>";
}

// Check database connection
echo "<h3>Database Connection</h3>";
if ($conn) {
    echo "✅ Database connected<br>";
} else {
    echo "❌ Database connection failed<br>";
}

// Test a simple query
echo "<h3>Database Test</h3>";
$result = $conn->query("SELECT COUNT(*) as count FROM students");
if ($result) {
    $count = $result->fetch_assoc()['count'];
    echo "Total students in database: $count<br>";
} else {
    echo "❌ Query failed: " . $conn->error . "<br>";
}

echo "<br><a href='index.php'>Back to Login</a>";
?>
