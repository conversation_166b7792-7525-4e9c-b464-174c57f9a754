<?php
// Get admin notification count if not already set
if (!isset($unreadNotifications)) {
    $unreadNotifications = getAdminNotificationCount();
}
?>

<nav class="navbar navbar-expand-lg navbar-dark bg-dark">
    <div class="container">
        <a class="navbar-brand" href="index.php">
            <img src="../images/logo.jpg" alt="Logo" width="30" height="30" class="d-inline-block align-text-top me-2 rounded-circle">
            Admin Dashboard
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'index.php' ? 'active' : ''; ?>" href="index.php">
                        <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'academic_sessions.php' ? 'active' : ''; ?>" href="academic_sessions.php">
                        <i class="fas fa-calendar-alt me-1"></i>Academic Sessions
                    </a>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle <?php echo in_array(basename($_SERVER['PHP_SELF']), ['students.php', 'pending_students.php']) ? 'active' : ''; ?>" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-user-graduate me-1"></i>Students
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="students.php">All Students</a></li>
                        <li><a class="dropdown-item" href="pending_students.php">
                            Pending Approval
                            <?php 
                            $pendingStudentsCount = $conn->query("SELECT COUNT(*) as count FROM students WHERE status = 'pending'")->fetch_assoc()['count'];
                            if ($pendingStudentsCount > 0): 
                            ?>
                                <span class="badge bg-warning text-dark ms-1"><?php echo $pendingStudentsCount; ?></span>
                            <?php endif; ?>
                        </a></li>
                    </ul>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle <?php echo in_array(basename($_SERVER['PHP_SELF']), ['lecturers.php', 'pending_lecturers.php']) ? 'active' : ''; ?>" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-chalkboard-teacher me-1"></i>Lecturers
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="lecturers.php">All Lecturers</a></li>
                        <li><a class="dropdown-item" href="pending_lecturers.php">
                            Pending Approval
                            <?php 
                            $pendingLecturersCount = $conn->query("SELECT COUNT(*) as count FROM lecturers WHERE status = 'pending'")->fetch_assoc()['count'];
                            if ($pendingLecturersCount > 0): 
                            ?>
                                <span class="badge bg-warning text-dark ms-1"><?php echo $pendingLecturersCount; ?></span>
                            <?php endif; ?>
                        </a></li>
                    </ul>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle <?php echo in_array(basename($_SERVER['PHP_SELF']), ['courses.php', 'add_course.php']) ? 'active' : ''; ?>" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-book me-1"></i>Courses
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="courses.php">All Courses</a></li>
                        <li><a class="dropdown-item" href="add_course.php">Add Course</a></li>
                    </ul>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle <?php echo in_array(basename($_SERVER['PHP_SELF']), ['departments.php', 'add_department.php']) ? 'active' : ''; ?>" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-building me-1"></i>Departments
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="departments.php">All Departments</a></li>
                        <li><a class="dropdown-item" href="add_department.php">Add Department</a></li>
                    </ul>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle <?php echo in_array(basename($_SERVER['PHP_SELF']), ['reports.php', 'absent_students.php']) ? 'active' : ''; ?>" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-chart-bar me-1"></i>Reports
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="reports.php">Attendance Reports</a></li>
                        <li><a class="dropdown-item" href="absent_students.php">Absent Students</a></li>
                    </ul>
                </li>
            </ul>
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link position-relative <?php echo basename($_SERVER['PHP_SELF']) == 'notifications.php' ? 'active' : ''; ?>" href="notifications.php" title="Notifications">
                        <i class="fas fa-bell"></i>
                        <?php if ($unreadNotifications > 0): ?>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                <?php echo $unreadNotifications > 99 ? '99+' : $unreadNotifications; ?>
                                <span class="visually-hidden">unread notifications</span>
                            </span>
                        <?php endif; ?>
                    </a>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-user-circle me-1"></i> <?php echo $_SESSION['username']; ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="profile.php">
                            <i class="fas fa-user me-2"></i>Profile
                        </a></li>
                        <li><a class="dropdown-item" href="change_password.php">
                            <i class="fas fa-key me-2"></i>Change Password
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="../index.php?logout=1">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

<!-- Auto QR Regenerator Script -->
<script src="../assets/js/auto_qr_regenerator.js"></script>
<script>
    // Enable auto QR regeneration for admin pages
    window.autoQRRegeneration = true;
</script>
