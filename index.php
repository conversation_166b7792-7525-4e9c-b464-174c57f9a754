<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include required files
require_once 'config/functions.php';

// Handle logout request
if (isset($_GET['logout'])) {
    // Clear session data
    $_SESSION = array();
    session_destroy();

    // Start a new session for the success message
    session_start();
    $_SESSION['success_message'] = "You have been successfully logged out.";

    // Redirect to remove the logout parameter from URL
    redirect('index.php');
}

// Check if user is already logged in (only if not logging out)
if (isAdminLoggedIn()) {
    redirect('admin/index.php');
} elseif (isLecturerLoggedIn()) {
    redirect('lecturer/index.php');
} elseif (isStudentLoggedIn()) {
    redirect('student/index.php');
}

// Process login form
$error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitize($_POST['username']);
    $password = $_POST['password'];
    $user_type = sanitize($_POST['user_type']);

    if (empty($username) || empty($password) || empty($user_type)) {
        $error = 'Please fill in all fields';
    } else {
        // Check login based on user type
        switch ($user_type) {
            case 'admin':
                $stmt = $conn->prepare("SELECT admin_id, username, password FROM admin WHERE username = ?");
                $stmt->bind_param("s", $username);
                $stmt->execute();
                $result = $stmt->get_result();

                if ($row = $result->fetch_assoc()) {
                    if (verifyPassword($password, $row['password'])) {
                        // Clear any existing session data
                        $_SESSION = array();
                        session_destroy();

                        // Start a new session
                        session_start();

                        // Set session variables
                        $_SESSION['admin_id'] = $row['admin_id'];
                        $_SESSION['username'] = $row['username'];
                        $_SESSION['last_activity'] = time();
                        $_SESSION['last_regeneration'] = time();
                        $_SESSION['browser_session_id'] = session_id();

                        redirect('admin/index.php');
                    } else {
                        $error = 'Invalid password';
                    }
                } else {
                    $error = 'Admin not found';
                }
                break;

            case 'lecturer':
                $stmt = $conn->prepare("SELECT lecturer_id, name, password, status FROM lecturers WHERE staff_code = ?");
                $stmt->bind_param("s", $username);
                $stmt->execute();
                $result = $stmt->get_result();

                if ($row = $result->fetch_assoc()) {
                    if ($row['status'] !== 'approved') {
                        $error = 'Your account is pending approval. Please contact the administrator.';
                    } elseif (verifyPassword($password, $row['password'])) {
                        // Clear any existing session data
                        $_SESSION = array();
                        session_destroy();

                        // Start a new session
                        session_start();

                        // Set session variables
                        $_SESSION['lecturer_id'] = $row['lecturer_id'];
                        $_SESSION['username'] = $row['name'];
                        $_SESSION['last_activity'] = time();
                        $_SESSION['last_regeneration'] = time();

                        redirect('lecturer/index.php');
                    } else {
                        $error = 'Invalid password';
                    }
                } else {
                    $error = 'Lecturer not found';
                }
                break;

            case 'student':
                $stmt = $conn->prepare("SELECT student_id, name, password, status FROM students WHERE matric_number = ?");
                $stmt->bind_param("s", $username);
                $stmt->execute();
                $result = $stmt->get_result();

                if ($row = $result->fetch_assoc()) {
                    if ($row['status'] !== 'approved') {
                        $error = 'Your account is pending approval. Please contact the administrator.';
                    } elseif (verifyPassword($password, $row['password'])) {
                        // Clear any existing session data
                        $_SESSION = array();
                        session_destroy();

                        // Start a new session
                        session_start();

                        // Set session variables
                        $_SESSION['student_id'] = $row['student_id'];
                        $_SESSION['username'] = $row['name'];
                        $_SESSION['last_activity'] = time();
                        $_SESSION['last_regeneration'] = time();

                        redirect('student/index.php');
                    } else {
                        $error = 'Invalid password';
                    }
                } else {
                    $error = 'Student not found';
                }
                break;

            default:
                $error = 'Invalid user type';
                break;
        }
    }
}
?>

<?php include 'includes/header.php'; ?>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4" style="height: 100%; background-color: rgba(255, 255, 255, 0.7);">
            <div class="card-body">
                <h2 class="text-center mb-4">Welcome to Ogbonnaya Onu Polytechnic</h2>
                <p class="lead text-center">QR Code Attendance Management System</p>

                <div class="text-center mb-4">
                    <img src="images/image1.jpg" alt="Attendance" class="img-fluid rounded shadow" style="max-width: 100%; height: auto; max-height: 350px; object-fit: cover;">
                </div>

                <p class="text-center fw-bold">This system helps to manage student attendance using QR codes, making the process efficient and preventing proxy attendance.</p>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card mb-4" style="height: 100%; background-color: rgba(255, 255, 255, 0.7);">
            <div class="card-header bg-dark text-white">
                <h3 class="text-center mb-0">Login</h3>
            </div>
            <div class="card-body">
                <?php if (!empty($error)): ?>
                    <div class="alert alert-danger"><?php echo $error; ?></div>
                <?php endif; ?>

                <?php if (isset($_SESSION['error_message'])): ?>
                    <div class="alert alert-danger"><?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?></div>
                <?php endif; ?>

                <?php if (isset($_SESSION['success_message'])): ?>
                    <div class="alert alert-success"><?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?></div>
                <?php endif; ?>

                <form method="POST" action="">
                    <div class="mb-3">
                        <label for="user_type" class="form-label">Login As</label>
                        <select class="form-select" id="user_type" name="user_type" required>
                            <option value="">Select User Type</option>
                            <option value="admin">Admin</option>
                            <option value="lecturer">Lecturer</option>
                            <option value="student">Student</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="username" class="form-label">Username / Staff ID / Matric Number</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="password" name="password" required>
                            <span class="input-group-text password-toggle" onclick="togglePassword()"><i class="fas fa-eye" id="toggleIcon"></i></span>
                        </div>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">Login</button>
                    </div>

                    <div class="text-center mt-3">
                        <a href="forgot_password.php" class="text-decoration-none">Forgot Password?</a>
                    </div>
                </form>

                <div class="mt-3 text-center">
                    <a href="reset_password_security.php" class="text-decoration-none">
                        <i class="fas fa-key me-1"></i>Forgot Password?
                    </a>
                </div>

                <div class="mt-4 text-center">
                    <p>Don't have an account?</p>
                    <div class="row">
                        <div class="col-md-6">
                            <a href="lecturer_register.php" class="btn btn-outline-secondary d-block">Register as Lecturer</a>
                        </div>
                        <div class="col-md-6">
                            <a href="student_register.php" class="btn btn-outline-secondary d-block">Register as Student</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
