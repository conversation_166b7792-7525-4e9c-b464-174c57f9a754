<?php
require_once '../config/functions.php';
require_once 'auth_check.php';

// Process approval/rejection
$message = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['approve'])) {
        $student_id = sanitize($_POST['student_id']);

        // Get student details for notification
        $student_stmt = $conn->prepare("SELECT name, email FROM students WHERE student_id = ?");
        $student_stmt->bind_param("i", $student_id);
        $student_stmt->execute();
        $student_data = $student_stmt->get_result()->fetch_assoc();

        $stmt = $conn->prepare("UPDATE students SET status = 'approved' WHERE student_id = ?");
        $stmt->bind_param("i", $student_id);
        if ($stmt->execute()) {
            // Create notification for the student
            $student_message = "Congratulations! Your student account has been approved. You can now log in and start marking attendance.";
            createNotification($student_id, 'student', $student_message, null, 'account_approved');

            // Mark the related admin notification as read
            markRelatedAdminNotificationAsRead($student_id, 'student_registration');

            $message = displaySuccess("Student approved successfully!");
        } else {
            $message = displayError("Error approving student: " . $conn->error);
        }
    } elseif (isset($_POST['reject'])) {
        $student_id = sanitize($_POST['student_id']);

        // Get student details for notification
        $student_stmt = $conn->prepare("SELECT name, email FROM students WHERE student_id = ?");
        $student_stmt->bind_param("i", $student_id);
        $student_stmt->execute();
        $student_data = $student_stmt->get_result()->fetch_assoc();

        $stmt = $conn->prepare("UPDATE students SET status = 'rejected' WHERE student_id = ?");
        $stmt->bind_param("i", $student_id);
        if ($stmt->execute()) {
            // Create notification for the student
            $student_message = "We regret to inform you that your student account application has been rejected. Please contact the administrator for more information.";
            createNotification($student_id, 'student', $student_message, null, 'account_rejected');

            // Mark the related admin notification as read
            markRelatedAdminNotificationAsRead($student_id, 'student_registration');

            $message = displaySuccess("Student rejected successfully!");
        } else {
            $message = displayError("Error rejecting student: " . $conn->error);
        }
    }
}

// Get all pending students
$pendingStudents = $conn->query("
    SELECT s.*, d.dept_name, l.level_name, sem.semester_name
    FROM students s
    JOIN departments d ON s.dept_id = d.dept_id
    JOIN levels l ON s.level_id = l.level_id
    JOIN semesters sem ON s.semester_id = sem.semester_id
    WHERE s.status = 'pending'
    ORDER BY s.created_at DESC
");

// Get admin notification count for navbar
$unreadNotifications = getAdminNotificationCount();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pending Student Approvals - Ogbonnaya Onu Polytechnic</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="../assets/images/logo.jpg" alt="Logo" width="30" height="30" class="d-inline-block align-text-top me-2">
                Admin Dashboard
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Students
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="students.php">All Students</a></li>
                            <li><a class="dropdown-item active" href="pending_students.php">Pending Approvals</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Lecturers
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="lecturers.php">All Lecturers</a></li>
                            <li><a class="dropdown-item" href="pending_lecturers.php">Pending Approvals</a></li>
                            <li><a class="dropdown-item" href="assign_courses.php">Assign Courses</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Courses
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="courses.php">All Courses</a></li>
                            <li><a class="dropdown-item" href="add_course.php">Add New Course</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Departments
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="departments.php">All Departments</a></li>
                            <li><a class="dropdown-item" href="add_department.php">Add New Department</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">Reports</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-1"></i> <?php echo $_SESSION['username']; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profile</a></li>
                            <li><a class="dropdown-item" href="change_password.php">Change Password</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col-md-12">
                <h2>Pending Student Approvals</h2>
                <p class="text-muted">Approve or reject student registration requests</p>
            </div>
        </div>

        <?php echo $message; ?>

        <div class="card">
            <div class="card-body">
                <?php if ($pendingStudents->num_rows > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover data-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Matric Number</th>
                                    <th>Email</th>
                                    <th>Department</th>
                                    <th>Level</th>
                                    <th>Semester</th>
                                    <th>Registration Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($student = $pendingStudents->fetch_assoc()): ?>
                                    <tr>
                                        <td><?php echo $student['student_id']; ?></td>
                                        <td><?php echo $student['name']; ?></td>
                                        <td><?php echo $student['matric_number']; ?></td>
                                        <td><?php echo $student['email']; ?></td>
                                        <td><?php echo $student['dept_name']; ?></td>
                                        <td><?php echo $student['level_name']; ?></td>
                                        <td><?php echo $student['semester_name']; ?></td>
                                        <td><?php echo date('d M Y, h:i A', strtotime($student['created_at'])); ?></td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <form method="POST" action="" class="me-1">
                                                    <input type="hidden" name="student_id" value="<?php echo $student['student_id']; ?>">
                                                    <button type="submit" name="approve" class="btn btn-sm btn-success" onclick="return confirm('Are you sure you want to approve this student?')">
                                                        <i class="fas fa-check"></i> Approve
                                                    </button>
                                                </form>
                                                <form method="POST" action="">
                                                    <input type="hidden" name="student_id" value="<?php echo $student['student_id']; ?>">
                                                    <button type="submit" name="reject" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to reject this student?')">
                                                        <i class="fas fa-times"></i> Reject
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info">No pending student approvals at this time.</div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-3 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; <?php echo date('Y'); ?> Ogbonnaya Onu Polytechnic. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="../about.php" class="text-white me-3">About</a>
                    <a href="../support.php" class="text-white">Support</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/script.js"></script>
    <!-- Session Manager -->
    <script src="../assets/js/session_manager.js"></script>
    <script>
        $(document).ready(function() {
            $('.data-table').DataTable({
                "order": [[7, "desc"]]
            });
        });
    </script>
</body>
</html>
