<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ogbonnaya Onu Polytechnic Attendance System</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        /* Main Styles for Ogbonnaya Onu Polytechnic Attendance System */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: url('images/image2.jpg') no-repeat center center fixed;
            background-size: cover;
            position: relative;
        }

        /* Add overlay to entire body */
        body::before {
            content: "";
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(2px);
            -webkit-backdrop-filter: blur(2px);
            z-index: -1;
        }

        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .card-header {
            background-color: #343a40;
            color: white;
            border-radius: 10px 10px 0 0 !important;
        }

        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
        }

        .btn-primary:hover {
            background-color: #0069d9;
            border-color: #0062cc;
        }

        .btn-success {
            background-color: #28a745;
            border-color: #28a745;
        }

        .btn-success:hover {
            background-color: #218838;
            border-color: #1e7e34;
        }

        .login-container, .register-container {
            max-width: 500px;
            margin: 50px auto;
        }

        .dashboard-stats .card {
            transition: transform 0.3s;
        }

        .dashboard-stats .card:hover {
            transform: translateY(-5px);
        }

        .qr-container {
            text-align: center;
            margin: 20px 0;
        }

        .qr-code {
            padding: 15px;
            background: white;
            display: inline-block;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .scanner-container {
            width: 100%;
            max-width: 500px;
            margin: 0 auto;
        }

        #qr-reader {
            width: 100%;
            border-radius: 10px;
            overflow: hidden;
        }

        .table-container {
            overflow-x: auto;
        }

        .password-container {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #6c757d;
        }

        @media (max-width: 768px) {
            .card {
                margin-bottom: 15px;
            }

            .login-container, .register-container {
                margin: 30px auto;
            }
        }

        /* Custom styles for attendance status */
        .present {
            background-color: #d4edda;
            color: #155724;
        }

        .absent {
            background-color: #f8d7da;
            color: #721c24;
        }

        /* Animation for alerts */
        .alert {
            animation: fadeIn 0.5s;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* Navbar styles */
        .navbar-custom {
            background-color: #343a40;
        }

        .navbar-custom .navbar-brand,
        .navbar-custom .nav-link {
            color: white;
        }

        .navbar-custom .nav-link:hover {
            color: rgba(255, 255, 255, 0.8);
        }

        /* Profile image styles */
        .profile-img {
            width: 150px;
            height: 150px;
            object-fit: cover;
            border-radius: 50%;
            border: 5px solid #f8f9fa;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        /* Dashboard card icons */
        .dashboard-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: #007bff;
        }

        /* Approval badges */
        .badge-pending {
            background-color: #ffc107;
            color: #212529;
        }

        .badge-approved {
            background-color: #28a745;
            color: white;
        }

        .badge-rejected {
            background-color: #dc3545;
            color: white;
        }

        /* For backdrop-filter support */
        @supports (backdrop-filter: blur(5px)) {
            .blur-bg {
                backdrop-filter: blur(5px);
            }
        }

        /* Fallback for browsers that don't support backdrop-filter */
        @supports not (backdrop-filter: blur(5px)) {
            .blur-bg {
                background-color: rgba(255, 255, 255, 0.9) !important;
            }
        }
    </style>
</head>
<body>
    <header class="bg-dark text-white py-3">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-2">
                    <img src="images/logo.jpg" alt="Ogbonnaya Onu Polytechnic Logo" class="img-fluid rounded-circle" style="max-height: 80px; width: 80px; height: 80px; object-fit: cover; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.2);">
                </div>
                <div class="col-md-10">
                    <h1 class="mb-0">Ogbonnaya Onu Polytechnic</h1>
                    <p class="mb-0">Attendance Management System</p>
                </div>
            </div>
        </div>
    </header>
    <div class="container mt-4">
