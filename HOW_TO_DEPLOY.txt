ATTENDANCE SYSTEM DEPLOYMENT GUIDE
=====================================

This guide will help you deploy the Attendance System on your server or local machine.

REQUIREMENTS
============
- Web server (Apache/Nginx) with PHP 7.4 or higher
- MySQL 5.7 or higher (or MariaDB 10.2+)
- PHP extensions: mysqli, session, json, curl
- Internet connection (for Bootstrap, FontAwesome, and QR code generation)

STEP 1: DOWNLOAD AND EXTRACT
============================
1. Download the attendance system files
2. Extract all files to your web server directory:
   - For XAMPP: C:\xampp\htdocs\ATTENDANCE\
   - For WAMP: C:\wamp64\www\ATTENDANCE\
   - For Linux: /var/www/html/ATTENDANCE/

STEP 2: SET FOLDER PERMISSIONS
==============================
Make sure these folders are writable by the web server:
- config/ (for database configuration)
- qr_codes/ (for QR code storage)
- lecturer/qr_codes/ (for lecturer QR codes)

For Linux/Mac:
chmod 755 config/
chmod 755 qr_codes/
chmod 755 lecturer/qr_codes/

STEP 3: START YOUR WEB SERVER AND DATABASE
==========================================
1. Start Apache and MySQL services
   - XAMPP: Open XAMPP Control Panel, start Apache and MySQL
   - WAMP: Start WAMP services
   - Linux: sudo systemctl start apache2 mysql

2. Make sure MySQL is running on port 3306 (default)

STEP 4: RUN THE SETUP WIZARD
============================
1. Open your web browser
2. Go to: http://localhost/ATTENDANCE/setup.php
3. Follow the 5-step setup wizard:

   STEP 1: Welcome Screen
   - Click "Start Setup"

   STEP 2: Database Configuration
   - Host: localhost (usually)
   - Username: root (default for XAMPP/WAMP)
   - Password: (leave empty for XAMPP, or enter your MySQL password)
   - Database Name: attendance_system (or choose your own)
   - Click "Test Connection & Create Database"

   STEP 3: Create Tables
   - Click "Create Tables"
   - Wait for all tables to be created successfully

   STEP 4: Create Admin Account
   - Enter admin username (e.g., admin)
   - Enter admin email
   - Enter a strong password
   - Confirm password
   - Click "Create Admin Account"

   STEP 5: Setup Complete
   - Review the setup summary
   - Click "Go to Login Page"

STEP 5: FIRST LOGIN
===================
1. Go to: http://localhost/ATTENDANCE/
2. Click "Admin Login"
3. Enter the admin credentials you created
4. You're now in the admin dashboard!

STEP 6: INITIAL CONFIGURATION
=============================
After logging in as admin, you should:

1. ADD DEPARTMENTS:
   - Go to "Manage Departments"
   - Add your school's departments
   - Computer Science is already added by default

2. ADD COURSES:
   - Go to "Manage Courses"
   - Computer Science HND2 courses are pre-loaded
   - Add more courses as needed

3. APPROVE LECTURERS:
   - When lecturers register, approve them in "Manage Lecturers"
   - Assign courses to approved lecturers

4. APPROVE STUDENTS:
   - When students register, approve them in "Manage Students"

STEP 7: USER REGISTRATION
=========================
1. LECTURER REGISTRATION:
   - Go to: http://localhost/ATTENDANCE/lecturer/register.php
   - Lecturers fill the form and wait for admin approval

2. STUDENT REGISTRATION:
   - Go to: http://localhost/ATTENDANCE/student/register.php
   - Students fill the form and wait for admin approval

STEP 8: SYSTEM USAGE
====================
1. LECTURERS:
   - Login at: http://localhost/ATTENDANCE/lecturer/
   - Start class sessions to generate QR codes
   - Students scan QR codes to mark attendance
   - End class sessions to finalize attendance
   - View reports and absent students

2. STUDENTS:
   - Login at: http://localhost/ATTENDANCE/student/
   - View available courses and lecturers
   - Scan QR codes during class to mark attendance
   - View attendance history

3. ADMIN:
   - Login at: http://localhost/ATTENDANCE/admin/
   - Manage all users, courses, and departments
   - View system-wide reports
   - Configure academic sessions

TROUBLESHOOTING
===============
1. "Database connection failed":
   - Check MySQL is running
   - Verify database credentials
   - Make sure database exists

2. "Permission denied" errors:
   - Check folder permissions for config/ and qr_codes/
   - Make sure web server can write to these folders

3. "Setup already completed":
   - If you need to re-run setup, delete config/database.php
   - Or drop the database and start fresh

4. QR codes not working:
   - Check internet connection (uses Google Charts API)
   - Verify qr_codes/ folder is writable

5. Sessions timing out too quickly:
   - Check config/session.php for timeout settings
   - Adjust timeout values as needed

SECURITY NOTES
==============
1. Change default admin password after first login
2. Use strong passwords for all accounts
3. Keep the system updated
4. Backup your database regularly
5. Don't expose the system to public internet without proper security

BACKUP INSTRUCTIONS
===================
1. Database backup:
   - Export the attendance_system database from phpMyAdmin
   - Or use: mysqldump -u root -p attendance_system > backup.sql

2. Files backup:
   - Copy the entire ATTENDANCE folder
   - Include config/ and qr_codes/ folders

SUPPORT
=======
For technical support or questions:
- Check the CODE_EXPLANATION.txt file for system details
- Review the admin dashboard for system status
- Contact your system administrator

SYSTEM FEATURES
===============
✓ Admin, Lecturer, and Student roles
✓ QR code-based attendance marking
✓ Real-time attendance tracking
✓ Comprehensive reporting system
✓ Academic session management
✓ Email notifications
✓ Security questions for password reset
✓ Responsive Bootstrap design
✓ Excel export functionality

DEPLOYMENT COMPLETE!
====================
Your attendance system is now ready to use. Start by adding departments, 
courses, and approving users to begin tracking attendance.
