/**
 * Automatic QR Code Regenerator
 * Runs in the background to regenerate QR codes periodically
 */

class AutoQRRegenerator {
    constructor() {
        this.isRunning = false;
        this.intervalId = null;
        this.regenerationInterval = 60 * 60 * 1000; // 1 hour in milliseconds
        this.lastCheck = localStorage.getItem('lastQRCheck') || 0;
        
        // Start the background process
        this.init();
    }

    init() {
        // Check if we should run regeneration on page load
        const now = Date.now();
        const timeSinceLastCheck = now - parseInt(this.lastCheck);
        
        // If more than 1 hour has passed since last check, run regeneration
        if (timeSinceLastCheck > this.regenerationInterval) {
            this.runRegeneration();
        }

        // Set up periodic regeneration
        this.startPeriodicRegeneration();
        
        // Listen for visibility change to run when tab becomes active
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.checkAndRegenerate();
            }
        });
    }

    startPeriodicRegeneration() {
        // Clear any existing interval
        if (this.intervalId) {
            clearInterval(this.intervalId);
        }

        // Set up new interval
        this.intervalId = setInterval(() => {
            this.checkAndRegenerate();
        }, this.regenerationInterval);
    }

    checkAndRegenerate() {
        const now = Date.now();
        const timeSinceLastCheck = now - parseInt(this.lastCheck);
        
        if (timeSinceLastCheck > this.regenerationInterval) {
            this.runRegeneration();
        }
    }

    async runRegeneration() {
        if (this.isRunning) {
            console.log('QR regeneration already running, skipping...');
            return;
        }

        this.isRunning = true;
        console.log('Starting automatic QR code regeneration...');

        try {
            const response = await fetch('background_qr_regenerator.php', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const result = await response.json();
                
                if (result.success) {
                    console.log(`QR regeneration completed: ${result.regenerated} codes regenerated, ${result.errors} errors`);
                    
                    // Show subtle notification if user is admin
                    if (window.location.pathname.includes('/admin/')) {
                        this.showNotification(`QR codes updated: ${result.regenerated} regenerated`, 'success');
                    }
                } else {
                    console.error('QR regeneration failed:', result.message);
                }
            } else {
                console.error('QR regeneration request failed:', response.status);
            }

            // Update last check time
            this.lastCheck = Date.now();
            localStorage.setItem('lastQRCheck', this.lastCheck.toString());

        } catch (error) {
            console.error('Error during QR regeneration:', error);
        } finally {
            this.isRunning = false;
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = `
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 300px;
            font-size: 0.875rem;
        `;
        
        notification.innerHTML = `
            <i class="fas fa-qrcode me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Add to page
        document.body.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    // Manual trigger method
    async triggerManualRegeneration() {
        console.log('Manual QR regeneration triggered');
        await this.runRegeneration();
    }

    // Stop the automatic regeneration
    stop() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
        this.isRunning = false;
        console.log('Automatic QR regeneration stopped');
    }

    // Get status
    getStatus() {
        return {
            isRunning: this.isRunning,
            lastCheck: new Date(parseInt(this.lastCheck)),
            nextCheck: new Date(parseInt(this.lastCheck) + this.regenerationInterval)
        };
    }
}

// Initialize the auto regenerator when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Only run on admin pages or if explicitly enabled
    const shouldRun = window.location.pathname.includes('/admin/') || 
                     window.autoQRRegeneration === true;
    
    if (shouldRun) {
        window.autoQRRegenerator = new AutoQRRegenerator();
        
        // Expose manual trigger function globally
        window.triggerQRRegeneration = function() {
            if (window.autoQRRegenerator) {
                return window.autoQRRegenerator.triggerManualRegeneration();
            }
        };
        
        // Expose status function globally
        window.getQRRegenerationStatus = function() {
            if (window.autoQRRegenerator) {
                return window.autoQRRegenerator.getStatus();
            }
        };
    }
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AutoQRRegenerator;
}
