<?php
require_once '../config/functions.php';

// Check if student is logged in
if (!isStudentLoggedIn()) {
    redirect('../index.php');
}

// Get student details
$student_id = $_SESSION['student_id'];
$student = getStudentDetails($student_id);

if (!$student) {
    redirect('../index.php');
}

// Get current academic session
$current_session = getCurrentAcademicSession();

// Get courses for this student's department, level, and semester
$courses_query = $conn->prepare("
    SELECT c.*, d.dept_name, l.level_name, s.semester_name
    FROM courses c
    JOIN departments d ON c.dept_id = d.dept_id
    JOIN levels l ON c.level_id = l.level_id
    JOIN semesters s ON c.semester_id = s.semester_id
    WHERE c.dept_id = ? AND c.level_id = ? AND c.semester_id = ?
    ORDER BY c.course_code
");
$courses_query->bind_param("iii", $student['dept_id'], $student['level_id'], $student['semester_id']);
$courses_query->execute();
$courses = $courses_query->get_result();

// Get statistics
$totalCourses = $courses->num_rows;

// Check if status column exists in attendance table
$statusColumnExists = false;
$result = $conn->query("SHOW COLUMNS FROM attendance LIKE 'status'");
if ($result && $result->num_rows > 0) {
    $statusColumnExists = true;
}

// Get attendance statistics
if ($statusColumnExists) {
    $attendanceStats = $conn->prepare("
        SELECT
            COUNT(*) as total_attendance,
            COUNT(CASE WHEN status = 'present' THEN 1 END) as present_count,
            COUNT(CASE WHEN status = 'absent' THEN 1 END) as absent_count,
            COUNT(DISTINCT course_id) as courses_attended
        FROM attendance
        WHERE student_id = ?
    ");
} else {
    // Fallback query without status column (assume all records are present)
    $attendanceStats = $conn->prepare("
        SELECT
            COUNT(*) as total_attendance,
            COUNT(*) as present_count,
            0 as absent_count,
            COUNT(DISTINCT course_id) as courses_attended
        FROM attendance
        WHERE student_id = ?
    ");
}
$attendanceStats->bind_param("i", $student_id);
$attendanceStats->execute();
$stats = $attendanceStats->get_result()->fetch_assoc();

// Calculate attendance percentage
$attendancePercentage = ($stats['total_attendance'] > 0) ?
    round(($stats['present_count'] / $stats['total_attendance']) * 100) : 0;

// Get recent attendance records
if ($statusColumnExists) {
    $recentAttendance = $conn->prepare("
        SELECT a.*, c.course_code, c.course_title, cs.start_time
        FROM attendance a
        JOIN courses c ON a.course_id = c.course_id
        LEFT JOIN class_sessions cs ON a.session_id = cs.session_id
        WHERE a.student_id = ?
        ORDER BY a.attendance_date DESC, a.attendance_time DESC
        LIMIT 5
    ");
} else {
    // Fallback query without session_id join
    $recentAttendance = $conn->prepare("
        SELECT a.*, c.course_code, c.course_title, 'present' as status
        FROM attendance a
        JOIN courses c ON a.course_id = c.course_id
        WHERE a.student_id = ?
        ORDER BY a.attendance_date DESC, a.attendance_time DESC
        LIMIT 5
    ");
}
$recentAttendance->bind_param("i", $student_id);
$recentAttendance->execute();
$recentRecords = $recentAttendance->get_result();

// Get active class sessions for this student
if ($statusColumnExists) {
    $activeSessions = $conn->prepare("
        SELECT cs.*, c.course_code, c.course_title, l.name as lecturer_name
        FROM class_sessions cs
        JOIN courses c ON cs.course_id = c.course_id
        JOIN lecturers l ON cs.lecturer_id = l.lecturer_id
        WHERE cs.status = 'active'
        AND c.dept_id = ? AND c.level_id = ? AND c.semester_id = ?
        AND cs.session_id NOT IN (
            SELECT session_id FROM attendance WHERE student_id = ? AND session_id IS NOT NULL
        )
        ORDER BY cs.start_time DESC
    ");
    $activeSessions->bind_param("iiii", $student['dept_id'], $student['level_id'], $student['semester_id'], $student_id);
} else {
    // Fallback: show all active sessions (can't check if already attended)
    $activeSessions = $conn->prepare("
        SELECT cs.*, c.course_code, c.course_title, l.name as lecturer_name
        FROM class_sessions cs
        JOIN courses c ON cs.course_id = c.course_id
        JOIN lecturers l ON cs.lecturer_id = l.lecturer_id
        WHERE cs.status = 'active'
        AND c.dept_id = ? AND c.level_id = ? AND c.semester_id = ?
        ORDER BY cs.start_time DESC
    ");
    $activeSessions->bind_param("iii", $student['dept_id'], $student['level_id'], $student['semester_id']);
}
$activeSessions->execute();
$activeSessionsResult = $activeSessions->get_result();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Dashboard - Ogbonnaya Onu Polytechnic</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="../images/logo.jpg" alt="Logo" width="30" height="30" class="d-inline-block align-text-top me-2 rounded-circle">
                Student Dashboard
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">My Courses</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="attendance.php">My Attendance</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="scan_qr.php">Scan QR Code</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i> <?php echo htmlspecialchars($student['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profile</a></li>
                            <li><a class="dropdown-item" href="change_password.php">Change Password</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="h3 mb-3">Student Dashboard</h1>
                <div class="alert alert-info">
                    <strong>Welcome, <?php echo htmlspecialchars($student['full_name']); ?>!</strong><br>
                    <small>
                        <i class="fas fa-id-card me-1"></i> <?php echo htmlspecialchars($student['matric_number']); ?> |
                        <i class="fas fa-building me-1"></i> <?php echo htmlspecialchars($student['dept_name']); ?> |
                        <i class="fas fa-layer-group me-1"></i> <?php echo htmlspecialchars($student['level_name']); ?><br>
                        Academic Year: <?php echo $current_session['academic_year']; ?> - <?php echo $current_session['semester_name']; ?>
                    </small>
                </div>
            </div>
        </div>

        <!-- Active Class Sessions Alert -->
        <?php if ($activeSessionsResult->num_rows > 0): ?>
            <div class="row mb-4">
                <div class="col-12">
                    <div class="alert alert-warning" role="alert">
                        <h5 class="alert-heading">
                            <i class="fas fa-bell me-2"></i>
                            Active Class Session<?php echo $activeSessionsResult->num_rows > 1 ? 's' : ''; ?> Available!
                        </h5>
                        <p class="mb-3">You have <?php echo $activeSessionsResult->num_rows; ?> active class session<?php echo $activeSessionsResult->num_rows > 1 ? 's' : ''; ?> waiting for attendance.</p>

                        <div class="table-responsive">
                            <table class="table table-sm table-striped">
                                <thead>
                                    <tr>
                                        <th>Course</th>
                                        <th>Lecturer</th>
                                        <th>Started</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    // Reset the result pointer to read again
                                    $activeSessionsResult->data_seek(0);
                                    while ($session = $activeSessionsResult->fetch_assoc()):
                                    ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($session['course_code']); ?> - <?php echo htmlspecialchars($session['course_title']); ?></td>
                                            <td><?php echo htmlspecialchars($session['lecturer_name']); ?></td>
                                            <td><?php echo date('h:i A', strtotime($session['start_time'])); ?></td>
                                            <td>
                                                <a href="scan.php?session_id=<?php echo $session['session_id']; ?>" class="btn btn-primary btn-sm">
                                                    <i class="fas fa-qrcode me-1"></i> Mark Attendance
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Success/Error Messages -->
        <?php if (isset($_SESSION['success'])): ?>
            <div class="row mb-4">
                <div class="col-12">
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i><?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
            <div class="row mb-4">
                <div class="col-12">
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-book text-primary mb-2" style="font-size: 2rem;"></i>
                        <h5 class="card-title">Total Courses</h5>
                        <h2 class="card-text text-primary"><?php echo $totalCourses; ?></h2>
                        <p class="text-muted small">This semester</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-check-circle text-success mb-2" style="font-size: 2rem;"></i>
                        <h5 class="card-title">Classes Attended</h5>
                        <h2 class="card-text text-success"><?php echo $stats['present_count']; ?></h2>
                        <p class="text-muted small">Present records</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-percentage text-info mb-2" style="font-size: 2rem;"></i>
                        <h5 class="card-title">Attendance Rate</h5>
                        <h2 class="card-text text-info"><?php echo $attendancePercentage; ?>%</h2>
                        <div class="progress mt-2">
                            <div class="progress-bar bg-info" role="progressbar" style="width: <?php echo $attendancePercentage; ?>%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-times-circle text-danger mb-2" style="font-size: 2rem;"></i>
                        <h5 class="card-title">Missed Classes</h5>
                        <h2 class="card-text text-danger"><?php echo $stats['absent_count']; ?></h2>
                        <p class="text-muted small">Absent records</p>
                    </div>
                </div>
            </div>
        </div>
        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-2">
                                <a href="scan_qr.php" class="btn btn-success d-block">
                                    <i class="fas fa-qrcode me-2"></i> Scan QR Code for Attendance
                                </a>
                            </div>
                            <div class="col-md-4 mb-2">
                                <a href="attendance.php" class="btn btn-primary d-block">
                                    <i class="fas fa-clipboard-check me-2"></i> View My Attendance
                                </a>
                            </div>
                            <div class="col-md-4 mb-2">
                                <a href="courses.php" class="btn btn-info d-block">
                                    <i class="fas fa-book me-2"></i> View All Courses
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Row -->
        <div class="row">
            <!-- My Courses -->
            <div class="col-lg-8 mb-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-book me-2"></i>
                            My Courses (<?php echo $totalCourses; ?>)
                        </h5>
                        <a href="courses.php" class="btn btn-sm btn-primary">View All</a>
                    </div>
                    <div class="card-body">
                        <?php if ($totalCourses > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>Course Code</th>
                                            <th>Course Title</th>
                                            <th>Level</th>
                                            <th>Semester</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        // Reset courses result pointer
                                        $courses->data_seek(0);
                                        $count = 0;
                                        while ($course = $courses->fetch_assoc()):
                                            if ($count >= 5) break; // Show only first 5 courses
                                            $count++;
                                        ?>
                                            <tr>
                                                <td><strong><?php echo htmlspecialchars($course['course_code']); ?></strong></td>
                                                <td><?php echo htmlspecialchars($course['course_title']); ?></td>
                                                <td><?php echo htmlspecialchars($course['level_name']); ?></td>
                                                <td><?php echo htmlspecialchars($course['semester_name']); ?></td>
                                                <td>
                                                    <?php if (!empty($course['qr_code'])): ?>
                                                        <span class="badge bg-success">QR Ready</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-warning">No QR</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php if ($totalCourses > 5): ?>
                                <div class="text-center mt-3">
                                    <a href="courses.php" class="btn btn-outline-primary">
                                        View All <?php echo $totalCourses; ?> Courses
                                    </a>
                                </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                No courses available for your department, level, and semester. Please contact the administrator.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Recent Attendance -->
            <div class="col-lg-4 mb-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-clock me-2"></i>
                            Recent Attendance
                        </h5>
                        <a href="attendance.php" class="btn btn-sm btn-primary">View All</a>
                    </div>
                    <div class="card-body">
                        <?php if ($recentRecords->num_rows > 0): ?>
                            <div class="list-group list-group-flush">
                                <?php while ($record = $recentRecords->fetch_assoc()): ?>
                                    <div class="list-group-item px-0">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1">
                                                    <?php echo htmlspecialchars($record['course_code']); ?>
                                                </h6>
                                                <p class="mb-1 small text-muted">
                                                    <?php echo htmlspecialchars($record['course_title']); ?>
                                                </p>
                                                <small class="text-muted">
                                                    <?php echo date('M j, Y', strtotime($record['attendance_date'])); ?> at
                                                    <?php echo date('h:i A', strtotime($record['attendance_time'])); ?>
                                                </small>
                                            </div>
                                            <div>
                                                <?php
                                                $status = isset($record['status']) ? $record['status'] : 'present';
                                                ?>
                                                <span class="badge bg-<?php echo $status == 'present' ? 'success' : 'danger'; ?>">
                                                    <?php echo ucfirst($status); ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                <?php endwhile; ?>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-clipboard-list fa-2x text-muted mb-3"></i>
                                <p class="text-muted">No attendance records found yet.</p>
                                <a href="scan_qr.php" class="btn btn-sm btn-primary">
                                    <i class="fas fa-qrcode me-1"></i> Mark Your First Attendance
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/script.js"></script>
</body>
</html>
