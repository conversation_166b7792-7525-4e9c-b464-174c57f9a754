<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and is a student
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'student') {
    header('Location: ../login.php');
    exit;
}

$student_id = $_SESSION['user_id'];

// Get student information
$student_query = $conn->prepare("SELECT * FROM students WHERE student_id = ?");
$student_query->bind_param("i", $student_id);
$student_query->execute();
$student = $student_query->get_result()->fetch_assoc();

if (!$student) {
    header('Location: ../login.php');
    exit;
}

// Get current academic settings
$academic_query = $conn->query("SELECT * FROM academic_settings WHERE is_active = 1 LIMIT 1");
$academic_settings = $academic_query->fetch_assoc();

// Get all courses for this student's department, level, and current semester
$courses_query = $conn->prepare("
    SELECT c.*, d.dept_name
    FROM courses c
    JOIN departments d ON c.dept_id = d.dept_id
    WHERE c.dept_id = ? AND c.level_id = ? AND c.semester_id = ?
    ORDER BY c.course_order ASC, c.course_code ASC
");
$courses_query->bind_param("iii", $student['dept_id'], $student['level_id'], $academic_settings['current_semester']);
$courses_query->execute();
$courses = $courses_query->get_result();

$page_title = "My Courses";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Student Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include '../includes/student_navbar.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/student_sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><i class="fas fa-book me-2"></i><?php echo $page_title; ?></h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <span class="badge bg-info fs-6">
                                <?php echo $academic_settings['academic_year']; ?> -
                                <?php echo $academic_settings['current_semester'] == 1 ? 'First' : 'Second'; ?> Semester
                            </span>
                        </div>
                    </div>
                </div>

                <?php if (isset($_SESSION['success'])): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($_SESSION['error'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Student Info Card -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>Student Name:</strong><br>
                                        <span class="text-primary"><?php echo htmlspecialchars($student['name']); ?></span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Matric Number:</strong><br>
                                        <span class="text-info"><?php echo htmlspecialchars($student['matric_number']); ?></span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Department:</strong><br>
                                        <span class="text-success"><?php echo htmlspecialchars($student['dept_name']); ?></span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Level:</strong><br>
                                        <span class="badge bg-secondary">
                                            <?php
                                            $levels = ['', 'ND1', 'HND2', 'HND1', 'ND2'];
                                            echo $levels[$student['level_id']] ?? 'Unknown';
                                            ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Courses Grid -->
                <div class="row">
                    <?php if ($courses->num_rows > 0): ?>
                        <?php while ($course = $courses->fetch_assoc()):
                            // Get lecturer for this course
                            $lecturer_query = $conn->prepare("
                                SELECT l.name as lecturer_name, l.staff_code, l.email
                                FROM lecturer_courses lc
                                JOIN lecturers l ON lc.lecturer_id = l.lecturer_id
                                WHERE lc.course_id = ? AND l.status = 'approved'
                                LIMIT 1
                            ");
                            $lecturer_query->bind_param("i", $course['course_id']);
                            $lecturer_query->execute();
                            $lecturer_result = $lecturer_query->get_result();
                            $lecturer = $lecturer_result->fetch_assoc();
                        ?>
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="card h-100 border-left-primary course-card">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-3">
                                            <h5 class="card-title text-primary mb-0"><?php echo htmlspecialchars($course['course_code']); ?></h5>
                                            <span class="badge bg-info"><?php echo $course['credit_units']; ?> Units</span>
                                        </div>

                                        <h6 class="card-subtitle mb-3 text-muted"><?php echo htmlspecialchars($course['course_title']); ?></h6>

                                        <div class="mb-3">
                                            <small class="text-muted">
                                                <i class="fas fa-building me-1"></i><?php echo htmlspecialchars($course['dept_name']); ?>
                                            </small>
                                        </div>

                                        <div class="lecturer-info mb-3">
                                            <h6 class="mb-2"><i class="fas fa-user-tie me-2"></i>Lecturer</h6>
                                            <?php if ($lecturer): ?>
                                                <div class="p-2 bg-light rounded">
                                                    <strong><?php echo htmlspecialchars($lecturer['lecturer_name']); ?></strong><br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($lecturer['staff_code']); ?></small><br>
                                                    <small class="text-info">
                                                        <i class="fas fa-envelope me-1"></i><?php echo htmlspecialchars($lecturer['email']); ?>
                                                    </small>
                                                </div>
                                            <?php else: ?>
                                                <div class="p-2 bg-warning bg-opacity-10 rounded">
                                                    <span class="text-warning">
                                                        <i class="fas fa-exclamation-triangle me-1"></i>No lecturer assigned yet
                                                    </span>
                                                </div>
                                            <?php endif; ?>
                                        </div>

                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                <i class="fas fa-calendar me-1"></i>
                                                <?php echo $academic_settings['current_semester'] == 1 ? 'First' : 'Second'; ?> Semester
                                            </small>
                                            <?php if ($course['qr_code']): ?>
                                                <button class="btn btn-sm btn-outline-primary" onclick="viewQRCode('<?php echo $course['course_id']; ?>')">
                                                    <i class="fas fa-qrcode me-1"></i>QR Code
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endwhile; ?>
                    <?php else: ?>
                        <div class="col-12">
                            <div class="alert alert-info text-center">
                                <i class="fas fa-info-circle fa-3x mb-3"></i>
                                <h4>No Courses Available</h4>
                                <p class="mb-0">There are no courses available for your department, level, and current semester. Please contact the administrator for assistance.</p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </main>
        </div>
    </div>

    <!-- QR Code Modal -->
    <div class="modal fade" id="qrModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Course QR Code</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center" id="qrModalBody">
                    <!-- QR code will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function viewQRCode(courseId) {
            // Load QR code via AJAX
            fetch(`../admin/view_qr.php?course_id=${courseId}`)
                .then(response => response.text())
                .then(data => {
                    document.getElementById('qrModalBody').innerHTML = data;
                    new bootstrap.Modal(document.getElementById('qrModal')).show();
                })
                .catch(error => {
                    console.error('Error loading QR code:', error);
                    document.getElementById('qrModalBody').innerHTML = '<div class="alert alert-danger">Error loading QR code</div>';
                    new bootstrap.Modal(document.getElementById('qrModal')).show();
                });
        }
    </script>
</body>
</html>
