<?php
// Database configuration (generated by setup.php)
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'attendance_system';

try {
    $conn = new mysqli($host, $username, $password);
    if ($conn->connect_error) { die('Connection failed: '.$conn->connect_error); }
    $result = $conn->query("SHOW DATABASES LIKE 'attendance_system'");
    if ($result->num_rows == 0) { $conn->query("CREATE DATABASE `attendance_system` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"); }
    $conn->select_db($database);
    $conn->set_charset('utf8mb4');
} catch (Exception $e) { die('Database connection error: '.$e->getMessage()); }
?>
