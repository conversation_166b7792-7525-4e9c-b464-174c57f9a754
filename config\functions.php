<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include session configuration
require_once __DIR__ . '/session.php';

// Include database connection
require_once 'database.php';

// Initialize database tables if they don't exist
require_once 'init_tables.php';

// Include security questions functions
require_once 'security_questions_functions.php';

// Function to sanitize input data
function sanitize($data) {
    global $conn;
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    $data = $conn->real_escape_string($data);
    return $data;
}

// Function to hash password
function hashPassword($password) {
    return password_hash($password, PASSWORD_BCRYPT);
}

// Function to verify password
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// Function to check if user is logged in
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

// Function to check if admin is logged in
function isAdminLoggedIn() {
    return isset($_SESSION['admin_id']);
}

// Function to check if lecturer is logged in
function isLecturerLoggedIn() {
    return isset($_SESSION['lecturer_id']);
}

// Function to check if student is logged in
function isStudentLoggedIn() {
    return isset($_SESSION['student_id']);
}

// Function to redirect
function redirect($url) {
    header("Location: $url");
    exit();
}

// Function to display error message
function displayError($message) {
    return "<div class='alert alert-danger'>$message</div>";
}

// Function to display success message
function displaySuccess($message) {
    return "<div class='alert alert-success'>$message</div>";
}

// Function to generate QR code
function generateQRCode($data, $size = 300) {
    // Always use project root qr_codes directory (config/.. -> project root)
    $qrDir = dirname(__DIR__) . DIRECTORY_SEPARATOR . 'qr_codes';
    if (!is_dir($qrDir)) {
        @mkdir($qrDir, 0755, true);
    }

    // Convert data to JSON if it's an array
    if (is_array($data)) {
        $data = json_encode($data);
    }

    // Create a unique filename (absolute for saving, relative for returning)
    $relative = 'qr_codes/qr_' . time() . '_' . rand(1000, 9999) . '.png';
    $absolute = dirname(__DIR__) . DIRECTORY_SEPARATOR . $relative;

    // Generate QR code using QR Server API
    $qr_api_url = "https://api.qrserver.com/v1/create-qr-code/?size={$size}x{$size}&data=" . urlencode($data);

    // Try to download the QR code image
    $qr_image = @file_get_contents($qr_api_url);

    if ($qr_image !== false) {
        // Save the QR code image
        file_put_contents($absolute, $qr_image);
        return $relative;
    }

    // Fallback to a different API if the first one fails
    $qr_api_url2 = "https://chart.apis.google.com/chart?cht=qr&chs={$size}x{$size}&chl=" . urlencode($data);
    $qr_image = @file_get_contents($qr_api_url2);

    if ($qr_image !== false) {
        // Save the QR code image
        file_put_contents($absolute, $qr_image);
        return $relative;
    }

    // If both APIs fail, return a placeholder
    return 'assets/images/qr_placeholder.png';
}

// Function to get course QR code
function getCourseQRCode($course_id) {
    global $conn;

    // Get course details
    $stmt = $conn->prepare("SELECT * FROM courses WHERE course_id = ?");
    $stmt->bind_param("i", $course_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        return false;
    }

    $course = $result->fetch_assoc();

    // Check if QR code exists
    if (empty($course['qr_code'])) {
        // Generate QR code data
        $qr_data = json_encode([
            'course_id' => $course['course_id'],
            'course_code' => $course['course_code'],
            'course_title' => $course['course_title'],
            'timestamp' => time()
        ]);

        // Generate QR code
        $qr_code = generateQRCode($qr_data);

        // Update course with new QR code
        $stmt = $conn->prepare("UPDATE courses SET qr_code = ? WHERE course_id = ?");
        $stmt->bind_param("si", $qr_code, $course_id);
        $stmt->execute();

        return $qr_code;
    }

    return $course['qr_code'];
}

// Function to get department name by ID
function getDepartmentName($dept_id) {
    global $conn;
    $stmt = $conn->prepare("SELECT dept_name FROM departments WHERE dept_id = ?");
    $stmt->bind_param("i", $dept_id);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($row = $result->fetch_assoc()) {
        return $row['dept_name'];
    }
    return "Unknown Department";
}

// Function to get level name by ID
function getLevelName($level_id) {
    global $conn;
    $stmt = $conn->prepare("SELECT level_name FROM levels WHERE level_id = ?");
    $stmt->bind_param("i", $level_id);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($row = $result->fetch_assoc()) {
        return $row['level_name'];
    }
    return "Unknown Level";
}

// Function to get semester name by ID
function getSemesterName($semester_id) {
    global $conn;
    $stmt = $conn->prepare("SELECT semester_name FROM semesters WHERE semester_id = ?");
    $stmt->bind_param("i", $semester_id);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($row = $result->fetch_assoc()) {
        return $row['semester_name'];
    }
    return "Unknown Semester";
}

// Function to get course details by ID
function getCourseDetails($course_id) {
    global $conn;
    $stmt = $conn->prepare("SELECT * FROM courses WHERE course_id = ?");
    $stmt->bind_param("i", $course_id);
    $stmt->execute();
    $result = $stmt->get_result();
    return $result->fetch_assoc();
}

// Function to get student details by ID
function getStudentDetails($student_id) {
    global $conn;
    $stmt = $conn->prepare("
        SELECT s.*, d.dept_name, l.level_name, sem.semester_name
        FROM students s
        LEFT JOIN departments d ON s.dept_id = d.dept_id
        LEFT JOIN levels l ON s.level_id = l.level_id
        LEFT JOIN semesters sem ON s.semester_id = sem.semester_id
        WHERE s.student_id = ?
    ");
    $stmt->bind_param("i", $student_id);
    $stmt->execute();
    $result = $stmt->get_result();
    return $result->fetch_assoc();
}

// Function to get lecturer details by ID
function getLecturerDetails($lecturer_id) {
    global $conn;
    $stmt = $conn->prepare("SELECT * FROM lecturers WHERE lecturer_id = ?");
    $stmt->bind_param("i", $lecturer_id);
    $stmt->execute();
    $result = $stmt->get_result();
    return $result->fetch_assoc();
}

// Function to start a class session
function startClassSession($course_id, $lecturer_id) {
    global $conn;

    // Check if there's already an active session for this course
    $stmt = $conn->prepare("SELECT * FROM class_sessions WHERE course_id = ? AND status = 'active'");
    $stmt->bind_param("i", $course_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        return ['success' => false, 'message' => 'There is already an active session for this course'];
    }

    // Start a new session
    $start_time = date('Y-m-d H:i:s');
    $stmt = $conn->prepare("INSERT INTO class_sessions (course_id, lecturer_id, start_time, status) VALUES (?, ?, ?, 'active')");
    $stmt->bind_param("iis", $course_id, $lecturer_id, $start_time);

    if ($stmt->execute()) {
        $session_id = $conn->insert_id;

        // Get course details
        $course = getCourseDetails($course_id);

        // Get all students in this course's department, level, and semester
        $stmt = $conn->prepare("SELECT student_id FROM students WHERE dept_id = ? AND level_id = ? AND semester_id = ? AND status = 'approved'");
        $stmt->bind_param("iii", $course['dept_id'], $course['level_id'], $course['semester_id']);
        $stmt->execute();
        $students = $stmt->get_result();

        // Create notifications for all students
        while ($student = $students->fetch_assoc()) {
            $message = "Class has started for " . $course['course_code'] . " - " . $course['course_title'] . ". Please mark your attendance.";
            createNotification($student['student_id'], 'student', $message, $session_id, 'class_session');
        }

        return ['success' => true, 'session_id' => $session_id];
    } else {
        return ['success' => false, 'message' => 'Failed to start class session: ' . $conn->error];
    }
}

// Function to end a class session
function endClassSession($session_id) {
    global $conn;

    // Check if the session exists and is active
    $stmt = $conn->prepare("
        SELECT cs.*, c.course_id, c.dept_id, c.level_id, c.semester_id
        FROM class_sessions cs
        JOIN courses c ON cs.course_id = c.course_id
        WHERE cs.session_id = ? AND cs.status = 'active'
    ");
    $stmt->bind_param("i", $session_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        return ['success' => false, 'message' => 'No active session found with this ID'];
    }

    $session = $result->fetch_assoc();

    // End the session
    $end_time = date('Y-m-d H:i:s');
    $stmt = $conn->prepare("UPDATE class_sessions SET end_time = ?, status = 'ended' WHERE session_id = ?");
    $stmt->bind_param("si", $end_time, $session_id);

    if ($stmt->execute()) {
        // Now mark absent students who didn't attend
        markAbsentStudents($session_id, $session['course_id'], $session['dept_id'], $session['level_id'], $session['semester_id']);

        return ['success' => true];
    } else {
        return ['success' => false, 'message' => 'Failed to end class session: ' . $conn->error];
    }
}

// Function to mark absent students when class ends
function markAbsentStudents($session_id, $course_id, $dept_id, $level_id, $semester_id) {
    global $conn;

    // Get all students who should be in this class but didn't mark attendance
    $stmt = $conn->prepare("
        SELECT s.student_id
        FROM students s
        WHERE s.dept_id = ? AND s.level_id = ? AND s.semester_id = ? AND s.status = 'approved'
        AND s.student_id NOT IN (
            SELECT a.student_id
            FROM attendance a
            WHERE a.session_id = ?
        )
    ");
    $stmt->bind_param("iiii", $dept_id, $level_id, $semester_id, $session_id);
    $stmt->execute();
    $result = $stmt->get_result();

    $attendance_date = date('Y-m-d');
    $attendance_time = date('H:i:s');

    // Mark each absent student using INSERT IGNORE to avoid duplicate key errors
    while ($student = $result->fetch_assoc()) {
        $stmt2 = $conn->prepare("
            INSERT IGNORE INTO attendance (student_id, course_id, session_id, attendance_date, attendance_time, status, marked_by)
            VALUES (?, ?, ?, ?, ?, 'absent', 'system')
        ");
        $stmt2->bind_param("iiiss", $student['student_id'], $course_id, $session_id, $attendance_date, $attendance_time);
        $stmt2->execute();
    }
}

// Function to get active class sessions for a student
function getActiveClassSessionsForStudent($student_id) {
    global $conn;

    // Get student details
    $student = getStudentDetails($student_id);

    // Get active sessions for courses in student's department, level, and semester
    $stmt = $conn->prepare("
        SELECT cs.*, c.course_code, c.course_title, l.name as lecturer_name
        FROM class_sessions cs
        JOIN courses c ON cs.course_id = c.course_id
        JOIN lecturers l ON cs.lecturer_id = l.lecturer_id
        WHERE c.dept_id = ? AND c.level_id = ? AND c.semester_id = ? AND cs.status = 'active'
        ORDER BY cs.start_time DESC
    ");
    $stmt->bind_param("iii", $student['dept_id'], $student['level_id'], $student['semester_id']);
    $stmt->execute();

    return $stmt->get_result();
}

// Function to get active class sessions for a lecturer
function getActiveClassSessionsForLecturer($lecturer_id) {
    global $conn;

    $stmt = $conn->prepare("
        SELECT cs.*, c.course_code, c.course_title
        FROM class_sessions cs
        JOIN courses c ON cs.course_id = c.course_id
        WHERE cs.lecturer_id = ? AND cs.status = 'active'
        ORDER BY cs.start_time DESC
    ");
    $stmt->bind_param("i", $lecturer_id);
    $stmt->execute();

    return $stmt->get_result();
}

// Function to get courses assigned to a lecturer
function getLecturerCourses($lecturer_id) {
    global $conn;

    // Get lecturer details
    $lecturer = getLecturerDetails($lecturer_id);

    // Get courses assigned to this lecturer
    $stmt = $conn->prepare("
        SELECT c.*, d.dept_name, lev.level_name, s.semester_name
        FROM lecturer_courses lc
        JOIN courses c ON lc.course_id = c.course_id
        JOIN departments d ON c.dept_id = d.dept_id
        JOIN levels lev ON c.level_id = lev.level_id
        JOIN semesters s ON c.semester_id = s.semester_id
        WHERE lc.lecturer_id = ?
        ORDER BY c.course_code
    ");
    $stmt->bind_param("i", $lecturer_id);
    $stmt->execute();
    $assigned_courses = $stmt->get_result();

    // If no assigned courses, get all courses in lecturer's department
    if ($assigned_courses->num_rows === 0) {
        $stmt = $conn->prepare("
            SELECT c.*, d.dept_name, lev.level_name, s.semester_name
            FROM courses c
            JOIN departments d ON c.dept_id = d.dept_id
            JOIN levels lev ON c.level_id = lev.level_id
            JOIN semesters s ON c.semester_id = s.semester_id
            WHERE c.dept_id = ?
            ORDER BY c.course_code
        ");
        $stmt->bind_param("i", $lecturer['dept_id']);
        $stmt->execute();
        return $stmt->get_result();
    }

    return $assigned_courses;
}

// Function to create a notification
function createNotification($user_id, $user_type, $message, $related_id = null, $related_type = null) {
    global $conn;

    $stmt = $conn->prepare("INSERT INTO notifications (user_id, user_type, message, related_id, related_type) VALUES (?, ?, ?, ?, ?)");
    $stmt->bind_param("issss", $user_id, $user_type, $message, $related_id, $related_type);

    return $stmt->execute();
}

// Function to get unread notifications for a user
function getUnreadNotifications($user_id, $user_type) {
    global $conn;

    $stmt = $conn->prepare("
        SELECT * FROM notifications
        WHERE user_id = ? AND user_type = ? AND is_read = 0
        ORDER BY created_at DESC
    ");
    $stmt->bind_param("is", $user_id, $user_type);
    $stmt->execute();

    return $stmt->get_result();
}

// Function to mark notification as read
function markNotificationAsRead($notification_id) {
    global $conn;

    $stmt = $conn->prepare("UPDATE notifications SET is_read = 1 WHERE notification_id = ?");
    $stmt->bind_param("i", $notification_id);

    return $stmt->execute();
}

// Function to get admin notifications count
function getAdminNotificationCount($admin_id = 1) {
    global $conn;

    $stmt = $conn->prepare("
        SELECT COUNT(*) as count FROM notifications
        WHERE user_id = ? AND user_type = 'admin' AND is_read = 0
    ");
    $stmt->bind_param("i", $admin_id);
    $stmt->execute();
    $result = $stmt->get_result();

    return $result->fetch_assoc()['count'];
}

// Function to get admin notifications
function getAdminNotifications($admin_id = 1, $limit = 10) {
    global $conn;

    $stmt = $conn->prepare("
        SELECT * FROM notifications
        WHERE user_id = ? AND user_type = 'admin'
        ORDER BY created_at DESC
        LIMIT ?
    ");
    $stmt->bind_param("ii", $admin_id, $limit);
    $stmt->execute();

    return $stmt->get_result();
}

// Function to mark all admin notifications as read
function markAllAdminNotificationsAsRead($admin_id = 1) {
    global $conn;

    $stmt = $conn->prepare("UPDATE notifications SET is_read = 1 WHERE user_id = ? AND user_type = 'admin'");
    $stmt->bind_param("i", $admin_id);

    return $stmt->execute();
}

// Function to mark related admin notification as read when action is taken
function markRelatedAdminNotificationAsRead($related_id, $related_type, $admin_id = 1) {
    global $conn;

    $stmt = $conn->prepare("UPDATE notifications SET is_read = 1 WHERE user_id = ? AND user_type = 'admin' AND related_id = ? AND related_type = ?");
    $stmt->bind_param("iis", $admin_id, $related_id, $related_type);

    return $stmt->execute();
}

// Function to check if student has marked attendance for a class session
function hasMarkedAttendance($student_id, $session_id) {
    global $conn;

    // Get class session details
    $stmt = $conn->prepare("SELECT course_id FROM class_sessions WHERE session_id = ?");
    $stmt->bind_param("i", $session_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        return false;
    }

    $session = $result->fetch_assoc();
    $course_id = $session['course_id'];

    // Check if student has marked attendance for this course today
    $today = date('Y-m-d');
    $stmt = $conn->prepare("
        SELECT * FROM attendance
        WHERE student_id = ? AND course_id = ? AND attendance_date = ?
    ");
    $stmt->bind_param("iis", $student_id, $course_id, $today);
    $stmt->execute();

    return $stmt->get_result()->num_rows > 0;
}

// Function to create a class schedule
function createClassSchedule($course_id, $lecturer_id, $week_number, $class_date, $start_time, $end_time) {
    global $conn;

    // Check if class_schedules table exists
    $result = $conn->query("SHOW TABLES LIKE 'class_schedules'");
    if ($result->num_rows == 0) {
        return ['success' => false, 'message' => 'Class scheduling tables not set up. Please run the setup script first.'];
    }

    // Check if there's already a schedule for this course and week
    $stmt = $conn->prepare("SELECT * FROM class_schedules WHERE course_id = ? AND week_number = ?");
    $stmt->bind_param("ii", $course_id, $week_number);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        // Update existing schedule
        $stmt = $conn->prepare("
            UPDATE class_schedules
            SET class_date = ?, start_time = ?, end_time = ?, status = 'scheduled'
            WHERE course_id = ? AND week_number = ?
        ");
        $stmt->bind_param("sssii", $class_date, $start_time, $end_time, $course_id, $week_number);

        if ($stmt->execute()) {
            return ['success' => true, 'message' => 'Schedule updated successfully'];
        } else {
            return ['success' => false, 'message' => 'Failed to update schedule: ' . $conn->error];
        }
    } else {
        // Check for time conflicts with other schedules
        $conflict = checkScheduleConflict($lecturer_id, $class_date, $start_time, $end_time);
        if ($conflict) {
            return ['success' => false, 'message' => 'Time conflict with another class: ' . $conflict];
        }

        // Create new schedule
        $stmt = $conn->prepare("
            INSERT INTO class_schedules
            (course_id, lecturer_id, week_number, class_date, start_time, end_time, status)
            VALUES (?, ?, ?, ?, ?, ?, 'scheduled')
        ");
        $stmt->bind_param("iiisss", $course_id, $lecturer_id, $week_number, $class_date, $start_time, $end_time);

        if ($stmt->execute()) {
            // If this is one of the first two weeks, try to detect a pattern
            if ($week_number <= 2) {
                detectSchedulePattern($course_id, $lecturer_id);
            }

            return ['success' => true, 'message' => 'Schedule created successfully'];
        } else {
            return ['success' => false, 'message' => 'Failed to create schedule: ' . $conn->error];
        }
    }
}

// Function to check for schedule conflicts
function checkScheduleConflict($lecturer_id, $class_date, $start_time, $end_time) {
    global $conn;

    // Check if there's already a schedule for this lecturer at the same time
    $stmt = $conn->prepare("
        SELECT cs.*, c.course_code, c.course_title, l.name as lecturer_name
        FROM class_schedules cs
        JOIN courses c ON cs.course_id = c.course_id
        JOIN lecturers l ON cs.lecturer_id = l.lecturer_id
        WHERE cs.lecturer_id = ? AND cs.class_date = ? AND
        ((cs.start_time <= ? AND cs.end_time > ?) OR
         (cs.start_time < ? AND cs.end_time >= ?) OR
         (cs.start_time >= ? AND cs.end_time <= ?))
    ");
    $stmt->bind_param("isssssss", $lecturer_id, $class_date, $start_time, $start_time, $end_time, $end_time, $start_time, $end_time);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $conflict = $result->fetch_assoc();
        return $conflict['course_code'] . ' - ' . $conflict['course_title'] . ' (' .
               date('h:i A', strtotime($conflict['start_time'])) . ' - ' .
               date('h:i A', strtotime($conflict['end_time'])) . ')';
    }

    return false;
}

// Function to detect schedule pattern
function detectSchedulePattern($course_id, $lecturer_id) {
    global $conn;

    // Check if tables exist
    $result = $conn->query("SHOW TABLES LIKE 'class_schedules'");
    if ($result->num_rows == 0) {
        return false; // Table doesn't exist
    }

    $result = $conn->query("SHOW TABLES LIKE 'schedule_patterns'");
    if ($result->num_rows == 0) {
        return false; // Table doesn't exist
    }

    // Get the first two weeks of schedules for this course
    $stmt = $conn->prepare("
        SELECT * FROM class_schedules
        WHERE course_id = ? AND lecturer_id = ? AND week_number <= 2
        ORDER BY week_number ASC
    ");
    $stmt->bind_param("ii", $course_id, $lecturer_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows < 2) {
        return false; // Need at least two weeks to detect a pattern
    }

    $schedules = [];
    while ($row = $result->fetch_assoc()) {
        $schedules[] = $row;
    }

    // Check if the day of week and time are consistent
    $day1 = date('l', strtotime($schedules[0]['class_date']));
    $day2 = date('l', strtotime($schedules[1]['class_date']));

    if ($day1 == $day2 &&
        $schedules[0]['start_time'] == $schedules[1]['start_time'] &&
        $schedules[0]['end_time'] == $schedules[1]['end_time']) {

        // We have a pattern! Save it
        $stmt = $conn->prepare("
            INSERT INTO schedule_patterns
            (course_id, lecturer_id, day_of_week, start_time, end_time)
            VALUES (?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
            day_of_week = VALUES(day_of_week),
            start_time = VALUES(start_time),
            end_time = VALUES(end_time)
        ");
        $stmt->bind_param("iisss", $course_id, $lecturer_id, $day1, $schedules[0]['start_time'], $schedules[0]['end_time']);
        $stmt->execute();

        return true;
    }

    return false;
}

// Function to get schedule pattern for a course
function getSchedulePattern($course_id, $lecturer_id) {
    global $conn;

    // Check if schedule_patterns table exists
    $result = $conn->query("SHOW TABLES LIKE 'schedule_patterns'");
    if ($result->num_rows == 0) {
        return false; // Table doesn't exist
    }

    // Table exists, proceed with query
    $stmt = $conn->prepare("
        SELECT * FROM schedule_patterns
        WHERE course_id = ? AND lecturer_id = ?
    ");
    $stmt->bind_param("ii", $course_id, $lecturer_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        return $result->fetch_assoc();
    }

    return false;
}

// Function to auto-fill remaining weeks based on pattern
function autoFillSchedule($course_id, $lecturer_id) {
    global $conn;

    // Check if class_schedules table exists
    $result = $conn->query("SHOW TABLES LIKE 'class_schedules'");
    if ($result->num_rows == 0) {
        return ['success' => false, 'message' => 'Class scheduling tables not set up. Please run the setup script first.'];
    }

    // Get the pattern
    $pattern = getSchedulePattern($course_id, $lecturer_id);
    if (!$pattern) {
        return ['success' => false, 'message' => 'No pattern detected yet. Please schedule at least two weeks first.'];
    }

    // Get the last scheduled week
    $stmt = $conn->prepare("
        SELECT MAX(week_number) as last_week, class_date
        FROM class_schedules
        WHERE course_id = ? AND lecturer_id = ?
    ");
    $stmt->bind_param("ii", $course_id, $lecturer_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $last = $result->fetch_assoc();

    if (!$last['last_week']) {
        return ['success' => false, 'message' => 'No existing schedules found'];
    }

    $last_week = $last['last_week'];
    $last_date = new DateTime($last['class_date']);

    // Auto-fill weeks 3-9 based on the pattern
    $success_count = 0;
    for ($week = $last_week + 1; $week <= 9; $week++) {
        // Add 7 days to get to the next week
        $last_date->modify('+7 days');
        $next_date = $last_date->format('Y-m-d');

        // Create the schedule
        $result = createClassSchedule(
            $course_id,
            $lecturer_id,
            $week,
            $next_date,
            $pattern['start_time'],
            $pattern['end_time']
        );

        if ($result['success']) {
            $success_count++;
        }
    }

    if ($success_count > 0) {
        return ['success' => true, 'message' => "Auto-filled $success_count weeks successfully"];
    } else {
        return ['success' => false, 'message' => 'Failed to auto-fill any weeks'];
    }
}

// Function to get class schedules for a course
function getCourseSchedules($course_id) {
    global $conn;

    $stmt = $conn->prepare("
        SELECT * FROM class_schedules
        WHERE course_id = ?
        ORDER BY week_number ASC
    ");
    $stmt->bind_param("i", $course_id);
    $stmt->execute();

    return $stmt->get_result();
}

// Function to get class schedules for a lecturer
function getLecturerSchedules($lecturer_id) {
    global $conn;

    // Check if class_schedules table exists
    $result = $conn->query("SHOW TABLES LIKE 'class_schedules'");
    if ($result->num_rows == 0) {
        // Return empty result set
        return $conn->query("SELECT 1 LIMIT 0");
    }

    $stmt = $conn->prepare("
        SELECT cs.*, c.course_code, c.course_title, l.level_name, s.semester_name
        FROM class_schedules cs
        JOIN courses c ON cs.course_id = c.course_id
        JOIN levels l ON c.level_id = l.level_id
        JOIN semesters s ON c.semester_id = s.semester_id
        WHERE cs.lecturer_id = ?
        ORDER BY cs.class_date ASC, cs.start_time ASC
    ");
    $stmt->bind_param("i", $lecturer_id);
    $stmt->execute();

    return $stmt->get_result();
}

// Function to get class schedules for a student
function getStudentSchedules($student_id) {
    global $conn;

    // Check if class_schedules table exists
    $result = $conn->query("SHOW TABLES LIKE 'class_schedules'");
    if ($result->num_rows == 0) {
        // Return empty result set
        return $conn->query("SELECT 1 LIMIT 0");
    }

    // Get student details
    $student = getStudentDetails($student_id);

    $stmt = $conn->prepare("
        SELECT cs.*, c.course_code, c.course_title, l.name as lecturer_name
        FROM class_schedules cs
        JOIN courses c ON cs.course_id = c.course_id
        JOIN lecturers l ON cs.lecturer_id = l.lecturer_id
        WHERE c.dept_id = ? AND c.level_id = ? AND c.semester_id = ?
        ORDER BY cs.class_date ASC, cs.start_time ASC
    ");
    $stmt->bind_param("iii", $student['dept_id'], $student['level_id'], $student['semester_id']);
    $stmt->execute();

    return $stmt->get_result();
}

// Function to start a scheduled class
function startScheduledClass($schedule_id) {
    global $conn;

    // Get schedule details
    $stmt = $conn->prepare("
        SELECT * FROM class_schedules
        WHERE schedule_id = ? AND status = 'scheduled'
    ");
    $stmt->bind_param("i", $schedule_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        return ['success' => false, 'message' => 'No scheduled class found with this ID'];
    }

    $schedule = $result->fetch_assoc();

    // Update schedule status
    $stmt = $conn->prepare("
        UPDATE class_schedules
        SET status = 'started'
        WHERE schedule_id = ?
    ");
    $stmt->bind_param("i", $schedule_id);

    if ($stmt->execute()) {
        // End any other active classes for this lecturer
        $stmt = $conn->prepare("
            UPDATE class_sessions
            SET status = 'ended', end_time = NOW()
            WHERE lecturer_id = ? AND status = 'active'
        ");
        $stmt->bind_param("i", $schedule['lecturer_id']);
        $stmt->execute();

        // Start a new class session
        $result = startClassSession($schedule['course_id'], $schedule['lecturer_id']);

        if ($result['success']) {
            return ['success' => true, 'message' => 'Class started successfully'];
        } else {
            return ['success' => false, 'message' => $result['message']];
        }
    } else {
        return ['success' => false, 'message' => 'Failed to start class: ' . $conn->error];
    }
}

// Function to end a scheduled class
function endScheduledClass($schedule_id) {
    global $conn;

    // Get schedule details
    $stmt = $conn->prepare("
        SELECT * FROM class_schedules
        WHERE schedule_id = ? AND status = 'started'
    ");
    $stmt->bind_param("i", $schedule_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        return ['success' => false, 'message' => 'No active class found with this ID'];
    }

    $schedule = $result->fetch_assoc();

    // Update schedule status
    $stmt = $conn->prepare("
        UPDATE class_schedules
        SET status = 'ended'
        WHERE schedule_id = ?
    ");
    $stmt->bind_param("i", $schedule_id);

    if ($stmt->execute()) {
        // End any active class sessions for this course
        $stmt = $conn->prepare("
            SELECT session_id FROM class_sessions
            WHERE course_id = ? AND lecturer_id = ? AND status = 'active'
        ");
        $stmt->bind_param("ii", $schedule['course_id'], $schedule['lecturer_id']);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $session = $result->fetch_assoc();
            $result = endClassSession($session['session_id']);

            if ($result['success']) {
                return ['success' => true, 'message' => 'Class ended successfully'];
            } else {
                return ['success' => false, 'message' => $result['message']];
            }
        }

        return ['success' => true, 'message' => 'Class ended successfully'];
    } else {
        return ['success' => false, 'message' => 'Failed to end class: ' . $conn->error];
    }
}

// Function to cancel a scheduled class
function cancelScheduledClass($schedule_id) {
    global $conn;

    // Get schedule details
    $stmt = $conn->prepare("
        SELECT cs.*, c.course_code, c.course_title
        FROM class_schedules cs
        JOIN courses c ON cs.course_id = c.course_id
        WHERE cs.schedule_id = ? AND cs.status = 'scheduled'
    ");
    $stmt->bind_param("i", $schedule_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        return ['success' => false, 'message' => 'No scheduled class found with this ID'];
    }

    $schedule = $result->fetch_assoc();

    // Update schedule status
    $stmt = $conn->prepare("
        UPDATE class_schedules
        SET status = 'cancelled'
        WHERE schedule_id = ?
    ");
    $stmt->bind_param("i", $schedule_id);

    if ($stmt->execute()) {
        // Notify students about cancellation
        $course = getCourseDetails($schedule['course_id']);

        // Get all students in this course's department, level, and semester
        $stmt = $conn->prepare("
            SELECT student_id FROM students
            WHERE dept_id = ? AND level_id = ? AND semester_id = ? AND status = 'approved'
        ");
        $stmt->bind_param("iii", $course['dept_id'], $course['level_id'], $course['semester_id']);
        $stmt->execute();
        $students = $stmt->get_result();

        // Create notifications for all students
        while ($student = $students->fetch_assoc()) {
            $message = "Class for " . $schedule['course_code'] . " - " . $schedule['course_title'] .
                       " scheduled on " . date('d M Y', strtotime($schedule['class_date'])) .
                       " at " . date('h:i A', strtotime($schedule['start_time'])) .
                       " has been cancelled.";
            createNotification($student['student_id'], 'student', $message, $schedule_id, 'class_cancelled');
        }

        return ['success' => true, 'message' => 'Class cancelled successfully'];
    } else {
        return ['success' => false, 'message' => 'Failed to cancel class: ' . $conn->error];
    }
}

// Function to create a class reminder
function createClassReminder($schedule_id) {
    global $conn;

    // Get schedule details
    $stmt = $conn->prepare("
        SELECT * FROM class_schedules
        WHERE schedule_id = ? AND status = 'scheduled'
    ");
    $stmt->bind_param("i", $schedule_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        return ['success' => false, 'message' => 'No scheduled class found with this ID'];
    }

    $schedule = $result->fetch_assoc();

    // Calculate reminder time (10 minutes before class)
    $class_datetime = $schedule['class_date'] . ' ' . $schedule['start_time'];
    $reminder_time = date('Y-m-d H:i:s', strtotime($class_datetime) - 600); // 10 minutes before

    // Create reminder
    $stmt = $conn->prepare("
        INSERT INTO class_reminders
        (schedule_id, reminder_time, status)
        VALUES (?, ?, 'pending')
    ");
    $stmt->bind_param("is", $schedule_id, $reminder_time);

    if ($stmt->execute()) {
        return ['success' => true, 'message' => 'Reminder created successfully'];
    } else {
        return ['success' => false, 'message' => 'Failed to create reminder: ' . $conn->error];
    }
}

// Function to process pending reminders
function processPendingReminders() {
    global $conn;

    // Get pending reminders that are due
    $stmt = $conn->prepare("
        SELECT r.*, cs.course_id, cs.lecturer_id, c.course_code, c.course_title
        FROM class_reminders r
        JOIN class_schedules cs ON r.schedule_id = cs.schedule_id
        JOIN courses c ON cs.course_id = c.course_id
        WHERE r.status = 'pending' AND r.reminder_time <= NOW()
    ");
    $stmt->execute();
    $result = $stmt->get_result();

    $processed = 0;
    while ($reminder = $result->fetch_assoc()) {
        // Update reminder status
        $stmt = $conn->prepare("
            UPDATE class_reminders
            SET status = 'sent'
            WHERE reminder_id = ?
        ");
        $stmt->bind_param("i", $reminder['reminder_id']);
        $stmt->execute();

        // Notify lecturer
        $message = "Reminder: Your class for " . $reminder['course_code'] . " - " . $reminder['course_title'] .
                   " is scheduled to start in 10 minutes. Please confirm if you will start this class.";
        createNotification($reminder['lecturer_id'], 'lecturer', $message, $reminder['schedule_id'], 'class_reminder');

        $processed++;
    }

    return ['success' => true, 'processed' => $processed];
}

// Function to respond to a class reminder
function respondToReminder($reminder_id, $response) {
    global $conn;

    // Get reminder details
    $stmt = $conn->prepare("
        SELECT r.*, cs.schedule_id, cs.course_id, cs.lecturer_id, c.course_code, c.course_title, c.dept_id, c.level_id, c.semester_id
        FROM class_reminders r
        JOIN class_schedules cs ON r.schedule_id = cs.schedule_id
        JOIN courses c ON cs.course_id = c.course_id
        WHERE r.reminder_id = ?
    ");
    $stmt->bind_param("i", $reminder_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        return ['success' => false, 'message' => 'Reminder not found'];
    }

    $reminder = $result->fetch_assoc();

    // Update reminder status
    $stmt = $conn->prepare("
        UPDATE class_reminders
        SET status = 'responded', response = ?
        WHERE reminder_id = ?
    ");
    $stmt->bind_param("si", $response, $reminder_id);

    if ($stmt->execute()) {
        if ($response === 'accepted') {
            // Start the class
            return startScheduledClass($reminder['schedule_id']);
        } else {
            // Cancel the class
            return cancelScheduledClass($reminder['schedule_id']);
        }
    } else {
        return ['success' => false, 'message' => 'Failed to update reminder: ' . $conn->error];
    }
}

// Function to handle expired reminders
function handleExpiredReminders() {
    global $conn;

    // Get sent reminders that are more than 10 minutes old
    $stmt = $conn->prepare("
        SELECT r.*, cs.schedule_id
        FROM class_reminders r
        JOIN class_schedules cs ON r.schedule_id = cs.schedule_id
        WHERE r.status = 'sent' AND r.reminder_time <= DATE_SUB(NOW(), INTERVAL 10 MINUTE)
    ");
    $stmt->execute();
    $result = $stmt->get_result();

    $processed = 0;
    while ($reminder = $result->fetch_assoc()) {
        // Update reminder status
        $stmt = $conn->prepare("
            UPDATE class_reminders
            SET status = 'expired', response = 'no_response'
            WHERE reminder_id = ?
        ");
        $stmt->bind_param("i", $reminder['reminder_id']);
        $stmt->execute();

        // Cancel the class
        cancelScheduledClass($reminder['schedule_id']);

        $processed++;
    }

    return ['success' => true, 'processed' => $processed];
}

// Get current academic session
function getCurrentAcademicSession() {
    global $conn;

    $result = $conn->query("
        SELECT a.academic_year, s.semester_name, a.semester_id
        FROM academic_sessions a
        JOIN semesters s ON a.semester_id = s.semester_id
        WHERE a.is_current = 1
        LIMIT 1
    ");

    if ($result && $result->num_rows > 0) {
        return $result->fetch_assoc();
    }

    // Return default if no current session set
    return [
        'academic_year' => '2024/2025',
        'semester_name' => 'First Semester',
        'semester_id' => 1
    ];
}
?>
