<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database connection
require_once '../config/database.php';
require_once '../config/functions.php';
require_once 'auth_check.php';

// Get all lecturers with department names
$query = "
    SELECT l.*
    FROM lecturers l
    ORDER BY l.lecturer_id DESC
";

$lecturers = $conn->query($query);

if ($lecturers === false) {
    die("Error in query: " . $conn->error);
}

// Process delete lecturer if requested
$message = '';
if (isset($_GET['delete']) && !empty($_GET['delete'])) {
    $lecturer_id = sanitize($_GET['delete']);

    // Check if lecturer exists
    $stmt = $conn->prepare("SELECT lecturer_id FROM lecturers WHERE lecturer_id = ?");
    $stmt->bind_param("i", $lecturer_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        // Delete lecturer
        $stmt = $conn->prepare("DELETE FROM lecturers WHERE lecturer_id = ?");
        $stmt->bind_param("i", $lecturer_id);

        if ($stmt->execute()) {
            $message = displaySuccess("Lecturer deleted successfully!");
            // Refresh lecturers list
            $lecturers = $conn->query("
                SELECT l.*, d.dept_name
                FROM lecturers l
                LEFT JOIN departments d ON l.dept_id = d.dept_id
                ORDER BY l.lecturer_id DESC
            ");
        } else {
            $message = displayError("Error deleting lecturer: " . $conn->error);
        }
    } else {
        $message = displayError("Lecturer not found");
    }
}

// Process approve lecturer if requested
if (isset($_GET['approve']) && !empty($_GET['approve'])) {
    $lecturer_id = sanitize($_GET['approve']);

    // Check if lecturer exists
    $stmt = $conn->prepare("SELECT lecturer_id FROM lecturers WHERE lecturer_id = ?");
    $stmt->bind_param("i", $lecturer_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        // Approve lecturer
        $stmt = $conn->prepare("UPDATE lecturers SET status = 'approved' WHERE lecturer_id = ?");
        $stmt->bind_param("i", $lecturer_id);

        if ($stmt->execute()) {
            $message = displaySuccess("Lecturer approved successfully!");
            // Refresh lecturers list
            $lecturers = $conn->query("
                SELECT l.*, d.dept_name
                FROM lecturers l
                LEFT JOIN departments d ON l.dept_id = d.dept_id
                ORDER BY l.lecturer_id DESC
            ");
        } else {
            $message = displayError("Error approving lecturer: " . $conn->error);
        }
    } else {
        $message = displayError("Lecturer not found");
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All Lecturers - Ogbonnaya Onu Polytechnic</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }

        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .card-header {
            background-color: #343a40;
            color: white;
            border-radius: 10px 10px 0 0 !important;
        }

        .status-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="../images/logo.jpg" alt="Logo" width="30" height="30" class="d-inline-block align-text-top me-2 rounded-circle">
                Admin Dashboard
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Students
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="students.php">All Students</a></li>
                            <li><a class="dropdown-item" href="pending_students.php">Pending Approvals</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Lecturers
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item active" href="lecturers.php">All Lecturers</a></li>
                            <li><a class="dropdown-item" href="pending_lecturers.php">Pending Approvals</a></li>
                            <li><a class="dropdown-item" href="assign_courses.php">Assign Courses</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Courses
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="courses.php">All Courses</a></li>
                            <li><a class="dropdown-item" href="add_course.php">Add New Course</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Departments
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="departments.php">All Departments</a></li>
                            <li><a class="dropdown-item" href="add_department.php">Add New Department</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">Reports</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-1"></i> <?php echo $_SESSION['username']; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profile</a></li>
                            <li><a class="dropdown-item" href="change_password.php">Change Password</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col-md-12">
                <h2>All Lecturers</h2>
                <p class="text-muted">Manage lecturers registered in the system</p>
            </div>
        </div>

        <?php echo $message; ?>

        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Lecturers List</h5>
            </div>
            <div class="card-body">
                <?php if ($lecturers && $lecturers->num_rows > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover data-table">
                            <thead>
                                <tr>
                                    <th>Staff ID</th>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Phone</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($lecturer = $lecturers->fetch_assoc()): ?>
                                    <tr>
                                        <td><?php echo $lecturer['staff_code']; ?></td>
                                        <td><?php echo $lecturer['name']; ?></td>
                                        <td><?php echo $lecturer['email']; ?></td>
                                        <td><?php echo $lecturer['phone']; ?></td>
                                        <td>
                                            <?php if ($lecturer['status'] == 'approved'): ?>
                                                <span class="badge bg-success status-badge">Approved</span>
                                            <?php else: ?>
                                                <span class="badge bg-warning status-badge">Pending</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <a href="view_lecturer.php?id=<?php echo $lecturer['lecturer_id']; ?>" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php if ($lecturer['status'] == 'pending'): ?>
                                                <a href="lecturers.php?approve=<?php echo $lecturer['lecturer_id']; ?>" class="btn btn-sm btn-success" onclick="return confirm('Are you sure you want to approve this lecturer?')">
                                                    <i class="fas fa-check"></i>
                                                </a>
                                            <?php endif; ?>
                                            <a href="lecturers.php?delete=<?php echo $lecturer['lecturer_id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this lecturer?')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info">No lecturers found in the system.</div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Add extra space before footer -->
    <div class="container">
        <div class="row">
            <div class="col-12" style="height: 100px;"></div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-3 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; <?php echo date('Y'); ?> Ogbonnaya Onu Polytechnic. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="../about.php" class="text-white me-3">About</a>
                    <a href="../support.php" class="text-white">Support</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/script.js"></script>
    <!-- Session Manager -->
    <script src="../assets/js/session_manager.js"></script>
    <script>
        $(document).ready(function() {
            $('.data-table').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
            });
        });
    </script>
</body>
</html>
