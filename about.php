<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About - Ogbonnaya Onu Polytechnic</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 120px 0;
            position: relative;
            overflow: hidden;
        }
        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,255,0.1)"><polygon points="0,0 1000,100 1000,0"/></svg>');
            background-size: cover;
        }
        .hero-content {
            position: relative;
            z-index: 2;
        }
        .feature-card {
            border: none;
            box-shadow: 0 15px 35px rgba(0,123,255,0.1);
            transition: all 0.4s ease;
            height: 100%;
            border-radius: 20px;
            overflow: hidden;
        }
        .feature-card:hover {
            transform: translateY(-15px);
            box-shadow: 0 25px 50px rgba(0,123,255,0.2);
        }
        .icon-circle {
            width: 90px;
            height: 90px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 25px;
            font-size: 2.2rem;
            background: linear-gradient(135deg, #007bff, #0056b3);
            box-shadow: 0 10px 25px rgba(0,123,255,0.3);
        }
        .school-image {
            border-radius: 25px;
            box-shadow: 0 20px 40px rgba(0,123,255,0.15);
            transition: transform 0.3s ease;
        }
        .school-image:hover {
            transform: scale(1.02);
        }
        .mission-card {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border-radius: 25px;
            padding: 50px;
            box-shadow: 0 20px 40px rgba(0,123,255,0.2);
            position: relative;
            overflow: hidden;
        }
        .mission-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float-once 6s ease-in-out forwards;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        @keyframes float-once {
            0% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
            100% { transform: translateY(0px) rotate(360deg); }
        }
        .value-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,123,255,0.1);
            transition: all 0.3s ease;
            height: 100%;
        }
        .value-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,123,255,0.15);
        }
        .value-icon {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 1.8rem;
            color: white;
        }
        .section-title {
            position: relative;
            margin-bottom: 50px;
        }
        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(135deg, #007bff, #0056b3);
            border-radius: 2px;
        }
        .smooth-section {
            padding: 80px 0;
        }
        .bg-light-blue {
            background: linear-gradient(135deg, #f8fbff 0%, #e3f2fd 100%);
        }
        .text-gradient {
            background: linear-gradient(135deg, #007bff, #0056b3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .floating-element {
            animation: float-once-simple 3s ease-in-out forwards;
        }
        .floating-element:nth-child(2) {
            animation-delay: 0.5s;
        }
        .floating-element:nth-child(3) {
            animation-delay: 1s;
        }
        @keyframes float-once-simple {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-15px); }
            100% { transform: translateY(0px); }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-graduation-cap me-2"></i>Attendance System
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="about.php">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="support.php">Support</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section text-center">
        <div class="container hero-content">
            <div class="floating-element">
                <h1 class="display-3 fw-bold mb-4">
                    <i class="fas fa-university me-3"></i>Ogbonnaya Onu Polytechnic
                </h1>
                <p class="lead fs-4 mb-4">A Citadel of Academic Excellence</p>
                <p class="fs-5 opacity-75">Empowering minds, shaping futures, building tomorrow's leaders</p>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section class="smooth-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6 mb-5 mb-lg-0">
                    <h2 class="display-5 fw-bold mb-4 text-gradient">About Our Institution</h2>
                    <p class="lead mb-4 text-muted">
                        Ogbonnaya Onu Polytechnic is located in the heart of Abia State, Aba, Nigeria.
                        It is composed of different schools, such as the School of Science & Engineering
                        and the School of Business Administration.
                    </p>
                    <p class="mb-4 text-muted">
                        Ogbonnaya Onu Polytechnic is a citadel of academic excellence. We are committed
                        to quality education aimed at empowering and improving lives which make positive
                        influence in the society and the world at large.
                    </p>
                    <div class="row g-4">
                        <div class="col-sm-6">
                            <div class="d-flex align-items-center">
                                <div class="me-3" style="width: 50px; height: 50px; background: linear-gradient(135deg, #007bff, #0056b3); border-radius: 15px; display: flex; align-items: center; justify-content: center; box-shadow: 0 5px 15px rgba(0,123,255,0.3);">
                                    <i class="fas fa-map-marker-alt text-white"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1 fw-bold">Location</h6>
                                    <p class="mb-0 text-muted">Aba, Abia State, Nigeria</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="d-flex align-items-center">
                                <div class="me-3" style="width: 50px; height: 50px; background: linear-gradient(135deg, #007bff, #0056b3); border-radius: 15px; display: flex; align-items: center; justify-content: center; box-shadow: 0 5px 15px rgba(0,123,255,0.3);">
                                    <i class="fas fa-award text-white"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1 fw-bold">Excellence</h6>
                                    <p class="mb-0 text-muted">Quality Education</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="text-center floating-element">
                        <img src="images/image2.jpg"
                             alt="Ogbonnaya Onu Polytechnic" class="img-fluid school-image">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Mission Section -->
    <section class="smooth-section bg-light-blue">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="mission-card text-center">
                        <div class="position-relative" style="z-index: 2;">
                            <h2 class="display-5 fw-bold mb-4">Our Mission</h2>
                            <p class="lead mb-0 opacity-90">
                                We are committed to quality education aimed at empowering and improving lives
                                which make positive influence in the society and the world at large. Through
                                innovative teaching methods, cutting-edge technology, and industry partnerships,
                                we prepare our students to become leaders and innovators in their chosen fields.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Schools Section -->
    <section class="smooth-section">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold section-title text-gradient">Our Schools</h2>
                <p class="lead text-muted">Diverse academic programs across multiple disciplines</p>
            </div>
            <div class="row g-4">
                <div class="col-lg-6">
                    <div class="card feature-card floating-element">
                        <div class="card-body text-center p-5">
                            <div class="icon-circle text-white">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <h4 class="card-title fw-bold mb-3">School of Science & Engineering</h4>
                            <p class="card-text text-muted mb-4">
                                Cutting-edge programs in engineering, computer science, and applied sciences.
                                Our students gain practical skills and theoretical knowledge to excel in
                                technology-driven careers.
                            </p>
                            <div class="row g-2">
                                <div class="col-12">
                                    <div class="d-flex align-items-center justify-content-center">
                                        <div class="me-2" style="width: 25px; height: 25px; background: linear-gradient(135deg, #007bff, #0056b3); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                            <i class="fas fa-check text-white" style="font-size: 0.7rem;"></i>
                                        </div>
                                        <span class="fw-medium">Computer Science</span>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="d-flex align-items-center justify-content-center">
                                        <div class="me-2" style="width: 25px; height: 25px; background: linear-gradient(135deg, #007bff, #0056b3); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                            <i class="fas fa-check text-white" style="font-size: 0.7rem;"></i>
                                        </div>
                                        <span class="fw-medium">Engineering Technology</span>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="d-flex align-items-center justify-content-center">
                                        <div class="me-2" style="width: 25px; height: 25px; background: linear-gradient(135deg, #007bff, #0056b3); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                            <i class="fas fa-check text-white" style="font-size: 0.7rem;"></i>
                                        </div>
                                        <span class="fw-medium">Applied Sciences</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="card feature-card floating-element">
                        <div class="card-body text-center p-5">
                            <div class="icon-circle text-white">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <h4 class="card-title fw-bold mb-3">School of Business Administration</h4>
                            <p class="card-text text-muted mb-4">
                                Comprehensive business education preparing students for leadership roles
                                in various industries. Focus on entrepreneurship, management, and
                                sustainable business practices.
                            </p>
                            <div class="row g-2">
                                <div class="col-12">
                                    <div class="d-flex align-items-center justify-content-center">
                                        <div class="me-2" style="width: 25px; height: 25px; background: linear-gradient(135deg, #007bff, #0056b3); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                            <i class="fas fa-check text-white" style="font-size: 0.7rem;"></i>
                                        </div>
                                        <span class="fw-medium">Business Administration</span>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="d-flex align-items-center justify-content-center">
                                        <div class="me-2" style="width: 25px; height: 25px; background: linear-gradient(135deg, #007bff, #0056b3); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                            <i class="fas fa-check text-white" style="font-size: 0.7rem;"></i>
                                        </div>
                                        <span class="fw-medium">Accounting</span>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="d-flex align-items-center justify-content-center">
                                        <div class="me-2" style="width: 25px; height: 25px; background: linear-gradient(135deg, #007bff, #0056b3); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                            <i class="fas fa-check text-white" style="font-size: 0.7rem;"></i>
                                        </div>
                                        <span class="fw-medium">Marketing</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Values Section -->
    <section class="smooth-section bg-light-blue">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold section-title text-gradient">Our Core Values</h2>
                <p class="lead text-muted">The principles that guide our educational mission</p>
            </div>
            <div class="row g-4">
                <div class="col-lg-3 col-md-6">
                    <div class="value-card floating-element">
                        <div class="value-icon" style="background: linear-gradient(135deg, #007bff, #0056b3);">
                            <i class="fas fa-lightbulb"></i>
                        </div>
                        <h5 class="fw-bold mb-3">Innovation</h5>
                        <p class="text-muted mb-0">Embracing new technologies and teaching methods to stay ahead</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="value-card floating-element">
                        <div class="value-icon" style="background: linear-gradient(135deg, #007bff, #0056b3);">
                            <i class="fas fa-star"></i>
                        </div>
                        <h5 class="fw-bold mb-3">Excellence</h5>
                        <p class="text-muted mb-0">Striving for the highest standards in education and service</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="value-card floating-element">
                        <div class="value-icon" style="background: linear-gradient(135deg, #007bff, #0056b3);">
                            <i class="fas fa-heart"></i>
                        </div>
                        <h5 class="fw-bold mb-3">Integrity</h5>
                        <p class="text-muted mb-0">Upholding ethical standards and honesty in all we do</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="value-card floating-element">
                        <div class="value-icon" style="background: linear-gradient(135deg, #007bff, #0056b3);">
                            <i class="fas fa-users"></i>
                        </div>
                        <h5 class="fw-bold mb-3">Community</h5>
                        <p class="text-muted mb-0">Building strong relationships and meaningful partnerships</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container text-center">
            <p class="mb-0">&copy; <?php echo date('Y'); ?> Ogbonnaya Onu Polytechnic. All rights reserved.</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // One-time animation system
        document.addEventListener('DOMContentLoaded', function() {
            const animatedElements = document.querySelectorAll('.floating-element');
            const missionCard = document.querySelector('.mission-card');

            // Create intersection observer for scroll-triggered animations
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && !entry.target.classList.contains('animated')) {
                        entry.target.classList.add('animate-once');
                        entry.target.classList.add('animated');
                        // Stop observing this element after animation
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.3,
                rootMargin: '0px 0px -50px 0px'
            });

            // Observe all floating elements
            animatedElements.forEach(element => {
                observer.observe(element);
            });

            // Special handling for mission card
            if (missionCard) {
                observer.observe(missionCard);
            }
        });
    </script>

    <style>
        /* Override the CSS animations to be triggered by JavaScript */
        .floating-element {
            animation: none;
            transition: transform 0.6s ease-out;
        }

        .floating-element.animate-once {
            animation: float-once-simple 2s ease-in-out forwards;
        }

        .mission-card::before {
            animation: none;
        }

        .mission-card.animate-once::before {
            animation: float-once 4s ease-in-out forwards;
        }

        /* Stagger the animations for multiple elements */
        .floating-element:nth-child(2).animate-once {
            animation-delay: 0.3s;
        }

        .floating-element:nth-child(3).animate-once {
            animation-delay: 0.6s;
        }

        .floating-element:nth-child(4).animate-once {
            animation-delay: 0.9s;
        }
    </style>
</body>
</html>
