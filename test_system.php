<?php
require_once 'config/functions.php';

echo "<h2>System Test Results</h2>";

// Test database connection
echo "<h3>1. Database Connection</h3>";
if ($conn) {
    echo "✅ Database connected successfully<br>";
} else {
    echo "❌ Database connection failed<br>";
    exit;
}

// Test tables existence
echo "<h3>2. Tables Check</h3>";
$tables = ['students', 'lecturers', 'courses', 'departments', 'levels', 'semesters', 'class_sessions', 'attendance'];
foreach ($tables as $table) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result->num_rows > 0) {
        echo "✅ Table '$table' exists<br>";
    } else {
        echo "❌ Table '$table' missing<br>";
    }
}

// Test student data
echo "<h3>3. Student Data</h3>";
$students = $conn->query("SELECT COUNT(*) as count FROM students");
$student_count = $students->fetch_assoc()['count'];
echo "Students in database: $student_count<br>";

if ($student_count > 0) {
    $sample_student = $conn->query("SELECT * FROM students LIMIT 1")->fetch_assoc();
    echo "Sample student: " . $sample_student['full_name'] . " (" . $sample_student['matric_number'] . ")<br>";
}

// Test lecturer data
echo "<h3>4. Lecturer Data</h3>";
$lecturers = $conn->query("SELECT COUNT(*) as count FROM lecturers");
$lecturer_count = $lecturers->fetch_assoc()['count'];
echo "Lecturers in database: $lecturer_count<br>";

// Test course data
echo "<h3>5. Course Data</h3>";
$courses = $conn->query("SELECT COUNT(*) as count FROM courses");
$course_count = $courses->fetch_assoc()['count'];
echo "Courses in database: $course_count<br>";

if ($course_count > 0) {
    $sample_course = $conn->query("SELECT * FROM courses LIMIT 1")->fetch_assoc();
    echo "Sample course: " . $sample_course['course_code'] . " - " . $sample_course['course_title'] . "<br>";
}

// Test active sessions
echo "<h3>6. Active Class Sessions</h3>";
$active_sessions = $conn->query("SELECT COUNT(*) as count FROM class_sessions WHERE status = 'active'");
$active_count = $active_sessions->fetch_assoc()['count'];
echo "Active sessions: $active_count<br>";

// Test attendance records
echo "<h3>7. Attendance Records</h3>";
$attendance = $conn->query("SELECT COUNT(*) as count FROM attendance");
$attendance_count = $attendance->fetch_assoc()['count'];
echo "Attendance records: $attendance_count<br>";

// Test functions
echo "<h3>8. Function Tests</h3>";
if (function_exists('isStudentLoggedIn')) {
    echo "✅ isStudentLoggedIn function exists<br>";
} else {
    echo "❌ isStudentLoggedIn function missing<br>";
}

if (function_exists('getStudentDetails')) {
    echo "✅ getStudentDetails function exists<br>";
} else {
    echo "❌ getStudentDetails function missing<br>";
}

if (function_exists('startClassSession')) {
    echo "✅ startClassSession function exists<br>";
} else {
    echo "❌ startClassSession function missing<br>";
}

if (function_exists('endClassSession')) {
    echo "✅ endClassSession function exists<br>";
} else {
    echo "❌ endClassSession function missing<br>";
}

echo "<h3>9. Session Test</h3>";
session_start();
if (isset($_SESSION['student_id'])) {
    echo "✅ Student session active: " . $_SESSION['student_id'] . "<br>";
} else {
    echo "ℹ️ No student session active<br>";
}

if (isset($_SESSION['lecturer_id'])) {
    echo "✅ Lecturer session active: " . $_SESSION['lecturer_id'] . "<br>";
} else {
    echo "ℹ️ No lecturer session active<br>";
}

echo "<br><a href='index.php'>Back to Login</a>";
?>
