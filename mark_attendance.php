<?php
require_once 'config/functions.php';

header('Content-Type: application/json');

// Check if student is logged in
if (!isStudentLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'Please log in to mark attendance']);
    exit;
}

// Check if required data is provided
if (!isset($_POST['course_id']) || !isset($_POST['session_id'])) {
    echo json_encode(['success' => false, 'message' => 'Missing required data']);
    exit;
}

$student_id = $_SESSION['student_id'];
$course_id = sanitize($_POST['course_id']);
$session_id = sanitize($_POST['session_id']);

// Get student details
$student = getStudentDetails($student_id);
if (!$student) {
    echo json_encode(['success' => false, 'message' => 'Student not found']);
    exit;
}

// Verify the session is active
$stmt = $conn->prepare("
    SELECT cs.*, c.course_code, c.course_title, c.dept_id, c.level_id, c.semester_id
    FROM class_sessions cs
    JOIN courses c ON cs.course_id = c.course_id
    WHERE cs.session_id = ? AND cs.course_id = ? AND cs.status = 'active'
");
$stmt->bind_param("ii", $session_id, $course_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    echo json_encode(['success' => false, 'message' => 'Class session not found or has ended']);
    exit;
}

$session = $result->fetch_assoc();

// Check if student is eligible for this course (same department, level, semester)
if ($session['dept_id'] != $student['dept_id'] || 
    $session['level_id'] != $student['level_id'] || 
    $session['semester_id'] != $student['semester_id']) {
    echo json_encode(['success' => false, 'message' => 'You are not enrolled in this course']);
    exit;
}

// Check if student has already marked attendance for this session
$stmt = $conn->prepare("
    SELECT attendance_id FROM attendance 
    WHERE student_id = ? AND session_id = ?
");
$stmt->bind_param("ii", $student_id, $session_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    echo json_encode(['success' => false, 'message' => 'You have already marked attendance for this class']);
    exit;
}

// Mark attendance as present
$attendance_date = date('Y-m-d');
$attendance_time = date('H:i:s');

$stmt = $conn->prepare("
    INSERT INTO attendance (student_id, course_id, session_id, attendance_date, attendance_time, status, marked_by)
    VALUES (?, ?, ?, ?, ?, 'present', 'student')
");
$stmt->bind_param("iiiss", $student_id, $course_id, $session_id, $attendance_date, $attendance_time);

if ($stmt->execute()) {
    // Success response
    echo json_encode([
        'success' => true,
        'message' => 'Attendance marked successfully',
        'course' => $session['course_code'] . ' - ' . $session['course_title'],
        'student' => $student['full_name'],
        'date' => date('d M Y', strtotime($attendance_date)),
        'time' => date('h:i A', strtotime($attendance_time))
    ]);
} else {
    echo json_encode(['success' => false, 'message' => 'Failed to mark attendance: ' . $conn->error]);
}
?>
