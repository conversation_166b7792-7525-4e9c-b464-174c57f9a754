<?php
require_once '../config/functions.php';
require_once 'auth_check.php';

// Get all approved lecturers
$lecturers = $conn->query("SELECT * FROM lecturers WHERE status = 'approved' ORDER BY name");

// Get current academic session
$current_session = getCurrentAcademicSession();

// Get courses for current semester only
$courses = $conn->query("
    SELECT c.*, l.level_name, s.semester_name
    FROM courses c
    JOIN levels l ON c.level_id = l.level_id
    JOIN semesters s ON c.semester_id = s.semester_id
    WHERE c.semester_id = {$current_session['semester_id']}
    ORDER BY c.course_code
");

// Process form submission
$message = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $lecturer_id = sanitize($_POST['lecturer_id']);
    $course_ids = isset($_POST['course_ids']) ? $_POST['course_ids'] : [];

    if (empty($lecturer_id)) {
        $message = displayError("Please select a lecturer");
    } elseif (empty($course_ids)) {
        $message = displayError("Please select at least one course");
    } else {
        // Begin transaction
        $conn->begin_transaction();

        try {
            // First, remove all existing assignments for this lecturer
            $stmt = $conn->prepare("DELETE FROM lecturer_courses WHERE lecturer_id = ?");
            $stmt->bind_param("i", $lecturer_id);
            $stmt->execute();

            // Then, add new assignments
            $stmt = $conn->prepare("INSERT INTO lecturer_courses (lecturer_id, course_id) VALUES (?, ?)");

            foreach ($course_ids as $course_id) {
                $stmt->bind_param("ii", $lecturer_id, $course_id);
                $stmt->execute();
            }

            // Commit transaction
            $conn->commit();
            $message = displaySuccess("Courses assigned successfully!");
        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollback();
            $message = displayError("Error assigning courses: " . $e->getMessage());
        }
    }
}

// Get lecturer's assigned courses if lecturer is selected
$assigned_courses = [];
if (isset($_GET['lecturer_id']) && !empty($_GET['lecturer_id'])) {
    $selected_lecturer_id = sanitize($_GET['lecturer_id']);

    $stmt = $conn->prepare("
        SELECT c.course_id, c.course_code, c.course_title, l.level_name, s.semester_name
        FROM lecturer_courses lc
        JOIN courses c ON lc.course_id = c.course_id
        JOIN levels l ON c.level_id = l.level_id
        JOIN semesters s ON c.semester_id = s.semester_id
        WHERE lc.lecturer_id = ?
        ORDER BY c.course_code
    ");
    $stmt->bind_param("i", $selected_lecturer_id);
    $stmt->execute();
    $result = $stmt->get_result();

    while ($row = $result->fetch_assoc()) {
        $assigned_courses[] = $row;
    }

    // Get lecturer details
    $stmt = $conn->prepare("SELECT * FROM lecturers WHERE lecturer_id = ?");
    $stmt->bind_param("i", $selected_lecturer_id);
    $stmt->execute();
    $selected_lecturer = $stmt->get_result()->fetch_assoc();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Assign Courses - Ogbonnaya Onu Polytechnic</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="../images/logo.jpg" alt="Logo" width="30" height="30" class="d-inline-block align-text-top me-2 rounded-circle">
                Admin Dashboard
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Students
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="students.php">All Students</a></li>
                            <li><a class="dropdown-item" href="pending_students.php">Pending Approvals</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Lecturers
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="lecturers.php">All Lecturers</a></li>
                            <li><a class="dropdown-item" href="pending_lecturers.php">Pending Approvals</a></li>
                            <li><a class="dropdown-item active" href="assign_courses.php">Assign Courses</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Courses
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="courses.php">All Courses</a></li>
                            <li><a class="dropdown-item" href="add_course.php">Add New Course</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Departments
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="departments.php">All Departments</a></li>
                            <li><a class="dropdown-item" href="add_department.php">Add New Department</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">Reports</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-1"></i> <?php echo $_SESSION['username']; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profile</a></li>
                            <li><a class="dropdown-item" href="change_password.php">Change Password</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col-md-8">
                <h2>Assign Courses to Lecturers</h2>
                <p class="text-muted">Manage which courses are taught by each lecturer</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="alert alert-info mb-0 py-2 px-3">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Current Semester:</strong><br>
                    <?php echo htmlspecialchars($current_session['academic_year'] . ' - ' . $current_session['semester_name']); ?>
                </div>
            </div>
        </div>

        <?php echo $message; ?>

        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5>Select Lecturer</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="">
                            <div class="mb-3">
                                <label for="lecturer_id" class="form-label">Lecturer</label>
                                <select class="form-select" id="lecturer_id" name="lecturer_id" required onchange="this.form.submit()">
                                    <option value="">Select Lecturer</option>
                                    <?php while ($lecturer = $lecturers->fetch_assoc()): ?>
                                        <option value="<?php echo $lecturer['lecturer_id']; ?>" <?php echo (isset($_GET['lecturer_id']) && $_GET['lecturer_id'] == $lecturer['lecturer_id']) ? 'selected' : ''; ?>>
                                            <?php echo $lecturer['name']; ?> (<?php echo $lecturer['staff_code']; ?>)
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <?php if (isset($selected_lecturer)): ?>
                <div class="col-md-8 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5>Assign Courses to <?php echo $selected_lecturer['name']; ?></h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="">
                                <input type="hidden" name="lecturer_id" value="<?php echo $selected_lecturer['lecturer_id']; ?>">

                                <div class="mb-3">
                                    <label for="course_ids" class="form-label">Select Courses</label>
                                    <select class="form-select" id="course_ids" name="course_ids[]" multiple required>
                                        <?php
                                        // Reset the courses result pointer
                                        $courses->data_seek(0);

                                        // Get array of assigned course IDs for easier checking
                                        $assigned_course_ids = array_map(function($course) {
                                            return $course['course_id'];
                                        }, $assigned_courses);

                                        while ($course = $courses->fetch_assoc()):
                                        ?>
                                            <option value="<?php echo $course['course_id']; ?>" <?php echo in_array($course['course_id'], $assigned_course_ids) ? 'selected' : ''; ?>>
                                                <?php echo $course['course_code']; ?> - <?php echo $course['course_title']; ?>
                                                (<?php echo $course['level_name']; ?>, <?php echo $course['semester_name']; ?>)
                                            </option>
                                        <?php endwhile; ?>
                                    </select>
                                    <div class="form-text">Hold Ctrl (or Cmd on Mac) to select multiple courses</div>
                                </div>

                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">Save Assignments</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5>Currently Assigned Courses</h5>
                        </div>
                        <div class="card-body">
                            <?php if (count($assigned_courses) > 0): ?>
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover data-table">
                                        <thead>
                                            <tr>
                                                <th>Course Code</th>
                                                <th>Course Title</th>
                                                <!-- Department column removed -->
                                                <th>Level</th>
                                                <th>Semester</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($assigned_courses as $course): ?>
                                                <tr>
                                                    <td><?php echo $course['course_code']; ?></td>
                                                    <td><?php echo $course['course_title']; ?></td>
                                                    <!-- Department column removed -->
                                                    <td><?php echo $course['level_name']; ?></td>
                                                    <td><?php echo $course['semester_name']; ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-info">No courses currently assigned to this lecturer.</div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <div class="col-md-8">
                    <div class="alert alert-info">Please select a lecturer to assign courses.</div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-3 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; <?php echo date('Y'); ?> Ogbonnaya Onu Polytechnic. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="../about.php" class="text-white me-3">About</a>
                    <a href="../support.php" class="text-white">Support</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/script.js"></script>
    <!-- Session Manager -->
    <script src="../assets/js/session_manager.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize Select2
            $('#course_ids').select2({
                theme: 'bootstrap-5',
                placeholder: 'Select courses to assign',
                width: '100%'
            });

            // Initialize DataTable
            $('.data-table').DataTable({
                "order": [[0, "asc"]]
            });
        });
    </script>
</body>
</html>
