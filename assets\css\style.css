/* Main Styles for Ogbonnaya Onu Polytechnic Attendance System */

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

.card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.card-header {
    background-color: #343a40;
    color: white;
    border-radius: 10px 10px 0 0 !important;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0069d9;
    border-color: #0062cc;
}

.btn-success {
    background-color: #28a745;
    border-color: #28a745;
}

.btn-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
}

.login-container, .register-container {
    max-width: 500px;
    margin: 50px auto;
}

.dashboard-stats .card {
    transition: transform 0.3s;
}

.dashboard-stats .card:hover,
.card-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.card-hover {
    transition: transform 0.3s, box-shadow 0.3s;
}

/* Course Cards Styling */
.border-left-primary {
    border-left: 4px solid #007bff !important;
}

.border-left-success {
    border-left: 4px solid #28a745 !important;
}

.border-left-info {
    border-left: 4px solid #17a2b8 !important;
}

.border-left-warning {
    border-left: 4px solid #ffc107 !important;
}

.course-card {
    transition: all 0.3s ease;
    border: 1px solid #e3e6f0;
}

.course-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    border-color: #007bff;
}

.course-card .card-title {
    font-weight: 600;
    font-size: 1rem;
}

.course-card .card-text {
    color: #5a5c69;
    line-height: 1.4;
}

/* Sidebar Styling */
.sidebar {
    position: fixed;
    top: 56px;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
}

.sidebar .nav-link {
    font-weight: 500;
    color: #333;
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    margin: 0.125rem 0.5rem;
}

.sidebar .nav-link:hover {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
}

.sidebar .nav-link.active {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
    border-left: 3px solid #007bff;
}

.sidebar-heading {
    font-size: .75rem;
    text-transform: uppercase;
}

/* Main content adjustment for sidebar */
main {
    margin-left: 0;
}

@media (min-width: 768px) {
    main {
        margin-left: 240px;
    }
}

.qr-container {
    text-align: center;
    margin: 20px 0;
}

.qr-code {
    padding: 15px;
    background: white;
    display: inline-block;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.scanner-container {
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
    padding: 10px;
}

#qr-reader {
    width: 100%;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

#qr-reader__scan_region {
    background: rgba(0, 123, 255, 0.1);
    border-radius: 10px;
}

#qr-reader__dashboard {
    padding: 10px;
}

#qr-reader__dashboard button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
    margin: 5px;
}

#qr-reader__dashboard button:hover {
    background-color: #0069d9;
}

#scan-result {
    margin-top: 20px;
}

.table-container {
    overflow-x: auto;
}

.password-container {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #6c757d;
}

@media (max-width: 768px) {
    .card {
        margin-bottom: 15px;
    }

    .login-container, .register-container {
        margin: 30px auto;
    }
}

/* Custom styles for attendance status */
.present {
    background-color: #d4edda;
    color: #155724;
}

.absent {
    background-color: #f8d7da;
    color: #721c24;
}

/* Animation for alerts */
.alert {
    animation: fadeIn 0.5s;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Navbar styles */
.navbar-custom {
    background-color: #343a40;
}

.navbar-custom .navbar-brand,
.navbar-custom .nav-link {
    color: white;
}

.navbar-custom .nav-link:hover {
    color: rgba(255, 255, 255, 0.8);
}

/* Profile image styles */
.profile-img {
    width: 150px;
    height: 150px;
    object-fit: cover;
    border-radius: 50%;
    border: 5px solid #f8f9fa;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Dashboard card icons */
.dashboard-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
    color: #007bff;
}

/* Approval badges */
.badge-pending {
    background-color: #ffc107;
    color: #212529;
}

.badge-approved {
    background-color: #28a745;
    color: white;
}

.badge-rejected {
    background-color: #dc3545;
    color: white;
}

/* Footer positioning */
body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.container {
    flex: 1;
}

footer {
    margin-top: auto;
}

/* Map styles */
.map-container {
    height: 250px;
    width: 100%;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 15px;
    position: relative;
}

.map-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 10px;
    display: block;
}

.map-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 8px;
    text-align: center;
    font-size: 14px;
    transition: opacity 0.3s;
    opacity: 0;
}

.map-container:hover .map-overlay {
    opacity: 1;
}

.map-overlay-text {
    display: flex;
    align-items: center;
    justify-content: center;
}

.map-overlay-text:before {
    content: "\f35d";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    margin-right: 5px;
}
