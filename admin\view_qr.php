<?php
session_start();
require_once '../config/database.php';

// Check if course_id is provided
if (!isset($_GET['course_id']) || empty($_GET['course_id'])) {
    echo '<div class="alert alert-danger">Course ID is required</div>';
    exit;
}

$course_id = (int)$_GET['course_id'];

// Get course information
$course_query = $conn->prepare("
    SELECT c.*, d.dept_name 
    FROM courses c
    JOIN departments d ON c.dept_id = d.dept_id
    WHERE c.course_id = ?
");
$course_query->bind_param("i", $course_id);
$course_query->execute();
$course_result = $course_query->get_result();

if ($course_result->num_rows === 0) {
    echo '<div class="alert alert-danger">Course not found</div>';
    exit;
}

$course = $course_result->fetch_assoc();

// Check if QR code exists
if (empty($course['qr_code'])) {
    echo '<div class="alert alert-warning">QR code not available for this course</div>';
    exit;
}

// Check if QR code file exists
$qr_file_path = '../' . $course['qr_code'];
if (!file_exists($qr_file_path)) {
    echo '<div class="alert alert-warning">QR code file not found</div>';
    exit;
}
?>

<div class="text-center">
    <h5 class="mb-3"><?php echo htmlspecialchars($course['course_code']); ?></h5>
    <h6 class="text-muted mb-4"><?php echo htmlspecialchars($course['course_title']); ?></h6>
    
    <div class="qr-code-container mb-3">
        <img src="<?php echo $qr_file_path; ?>" alt="QR Code" class="img-fluid" style="max-width: 250px;">
    </div>
    
    <div class="course-info">
        <small class="text-muted">
            <strong>Department:</strong> <?php echo htmlspecialchars($course['dept_name']); ?><br>
            <strong>Credit Units:</strong> <?php echo $course['credit_units']; ?><br>
            <strong>Level:</strong> 
            <?php 
            $levels = ['', 'ND1', 'HND2', 'HND1', 'ND2'];
            echo $levels[$course['level_id']] ?? 'Unknown';
            ?><br>
            <strong>Semester:</strong> <?php echo $course['semester_id'] == 1 ? 'First' : 'Second'; ?>
        </small>
    </div>
    
    <div class="mt-3">
        <small class="text-info">
            <i class="fas fa-info-circle me-1"></i>
            Students can scan this QR code to mark their attendance
        </small>
    </div>
</div>
